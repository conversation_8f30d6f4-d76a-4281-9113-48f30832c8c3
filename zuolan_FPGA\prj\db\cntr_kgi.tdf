--lpm_counter CBX_DECLARE_ALL_CONNECTED_PORTS="OFF" DEVICE_FAMILY="Cyclone IV E" lpm_modulus=54 lpm_port_updown="PORT_CONNECTIVITY" lpm_width=6 clock q sclr CARRY_CHAIN="MANUAL" CARRY_CHAIN_LENGTH=48
--VERSION_BEGIN 18.1 cbx_cycloneii 2018:09:12:13:04:24:SJ cbx_lpm_add_sub 2018:09:12:13:04:24:SJ cbx_lpm_compare 2018:09:12:13:04:24:SJ cbx_lpm_counter 2018:09:12:13:04:24:SJ cbx_lpm_decode 2018:09:12:13:04:24:SJ cbx_mgl 2018:09:12:13:10:36:SJ cbx_nadder 2018:09:12:13:04:24:SJ cbx_stratix 2018:09:12:13:04:24:SJ cbx_stratixii 2018:09:12:13:04:24:SJ  VERSION_END


-- Copyright (C) 2018  Intel Corporation. All rights reserved.
--  Your use of Intel Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Intel Program License 
--  Subscription Agreement, the Intel Quartus Prime License Agreement,
--  the Intel FPGA IP License Agreement, or other applicable license
--  agreement, including, without limitation, that your use is for
--  the sole purpose of programming logic devices manufactured by
--  Intel and sold by Intel or its authorized distributors.  Please
--  refer to the applicable agreement for further details.


FUNCTION cycloneive_lcell_comb (cin, dataa, datab, datac, datad)
WITH ( DONT_TOUCH, LUT_MASK, SUM_LUTC_INPUT)
RETURNS ( combout, cout);
FUNCTION cmpr_sgc (dataa[5..0], datab[5..0])
RETURNS ( aeb);

--synthesis_resources = lut 6 reg 6 
SUBDESIGN cntr_kgi
( 
	clock	:	input;
	q[5..0]	:	output;
	sclr	:	input;
) 
VARIABLE 
	counter_comb_bita0 : cycloneive_lcell_comb
		WITH (
			LUT_MASK = "5A90",
			SUM_LUTC_INPUT = "cin"
		);
	counter_comb_bita1 : cycloneive_lcell_comb
		WITH (
			LUT_MASK = "5A90",
			SUM_LUTC_INPUT = "cin"
		);
	counter_comb_bita2 : cycloneive_lcell_comb
		WITH (
			LUT_MASK = "5A90",
			SUM_LUTC_INPUT = "cin"
		);
	counter_comb_bita3 : cycloneive_lcell_comb
		WITH (
			LUT_MASK = "5A90",
			SUM_LUTC_INPUT = "cin"
		);
	counter_comb_bita4 : cycloneive_lcell_comb
		WITH (
			LUT_MASK = "5A90",
			SUM_LUTC_INPUT = "cin"
		);
	counter_comb_bita5 : cycloneive_lcell_comb
		WITH (
			LUT_MASK = "5A90",
			SUM_LUTC_INPUT = "cin"
		);
	counter_reg_bit[5..0] : dffeas;
	cmpr1 : cmpr_sgc;
	aclr_actual	: WIRE;
	clk_en	: NODE;
	cnt_en	: NODE;
	compare_result	: WIRE;
	cout_actual	: WIRE;
	data[5..0]	: NODE;
	external_cin	: WIRE;
	modulus_bus[5..0]	: WIRE;
	modulus_trigger	: WIRE;
	s_val[5..0]	: WIRE;
	safe_q[5..0]	: WIRE;
	sload	: NODE;
	sset	: NODE;
	time_to_clear	: WIRE;
	updown_dir	: WIRE;

BEGIN 
	counter_comb_bita[5..0].cin = ( counter_comb_bita[4..0].cout, external_cin);
	counter_comb_bita[5..0].dataa = ( counter_reg_bit[5..0].q);
	counter_comb_bita[5..0].datab = ( updown_dir, updown_dir, updown_dir, updown_dir, updown_dir, updown_dir);
	counter_comb_bita[5..0].datad = ( B"1", B"1", B"1", B"1", B"1", B"1");
	counter_reg_bit[].asdata = ((! sclr) & ((sset & s_val[]) # ((! sset) & ((sload & data[]) # (((! sload) & modulus_bus[]) & (! updown_dir))))));
	counter_reg_bit[].clk = clock;
	counter_reg_bit[].clrn = (! aclr_actual);
	counter_reg_bit[].d = ( counter_comb_bita[5..0].combout);
	counter_reg_bit[].ena = (clk_en & (((sclr # sset) # sload) # cnt_en));
	counter_reg_bit[].sload = (((sclr # sset) # sload) # modulus_trigger);
	cmpr1.dataa[] = safe_q[];
	cmpr1.datab[] = modulus_bus[];
	aclr_actual = B"0";
	clk_en = VCC;
	cnt_en = VCC;
	compare_result = cmpr1.aeb;
	cout_actual = (counter_comb_bita[5].cout # (time_to_clear & updown_dir));
	data[] = GND;
	external_cin = B"1";
	modulus_bus[] = B"110101";
	modulus_trigger = cout_actual;
	q[] = safe_q[];
	s_val[] = B"111111";
	safe_q[] = counter_reg_bit[].q;
	sload = GND;
	sset = GND;
	time_to_clear = compare_result;
	updown_dir = B"1";
END;
--VALID FILE
