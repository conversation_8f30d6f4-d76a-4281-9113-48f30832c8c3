<session jtag_chain="USB-Blaster [USB-0]" jtag_device="@1: 10CL006(Y|Z)/10CL010(Y|Z)/.. (0x020F10DD)" sof_file="../output_files/JYX_FPGA_OBJECT.sof">
  <display_tree gui_logging_enabled="1">
    <display_branch instance="auto_signaltap_0" log="log: Trig @ 2025/07/17 15:31:51 (0:0:0.1 elapsed) #1" signal_set="signal_set: 2025/07/17 15:28:28  #0" trigger="trigger: 2025/07/17 15:28:28  #1"/>
  </display_tree>
  <instance enabled="true" entity_name="sld_signaltap" is_auto_node="yes" is_expanded="true" name="auto_signaltap_0" source_file="sld_signaltap.vhd">
    <node_ip_info instance_id="0" mfg_id="110" node_id="0" version="6"/>
    <signal_set global_temp="1" is_expanded="true" name="signal_set: 2024/07/13 20:34:46  #0">
      <clock name="CLKBASE" polarity="posedge" tap_mode="classic"/>
      <config pipeline_level="0" ram_type="AUTO" reserved_data_nodes="0" reserved_storage_qualifier_nodes="0" reserved_trigger_nodes="0" sample_depth="1024" trigger_in_enable="no" trigger_out_enable="no"/>
      <top_entity/>
      <signal_vec>
        <trigger_input_vec>
          <wire name="ADDR[0]" tap_mode="classic"/>
          <wire name="ADDR[10]" tap_mode="classic"/>
          <wire name="ADDR[11]" tap_mode="classic"/>
          <wire name="ADDR[12]" tap_mode="classic"/>
          <wire name="ADDR[13]" tap_mode="classic"/>
          <wire name="ADDR[14]" tap_mode="classic"/>
          <wire name="ADDR[15]" tap_mode="classic"/>
          <wire name="ADDR[1]" tap_mode="classic"/>
          <wire name="ADDR[2]" tap_mode="classic"/>
          <wire name="ADDR[3]" tap_mode="classic"/>
          <wire name="ADDR[4]" tap_mode="classic"/>
          <wire name="ADDR[5]" tap_mode="classic"/>
          <wire name="ADDR[6]" tap_mode="classic"/>
          <wire name="ADDR[7]" tap_mode="classic"/>
          <wire name="ADDR[8]" tap_mode="classic"/>
          <wire name="ADDR[9]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[0]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[10]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[11]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[12]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[13]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[14]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[15]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[1]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[2]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[3]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[4]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[5]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[6]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[7]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[8]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[9]" tap_mode="classic"/>
        </trigger_input_vec>
        <data_input_vec>
          <wire name="ADDR[0]" tap_mode="classic"/>
          <wire name="ADDR[10]" tap_mode="classic"/>
          <wire name="ADDR[11]" tap_mode="classic"/>
          <wire name="ADDR[12]" tap_mode="classic"/>
          <wire name="ADDR[13]" tap_mode="classic"/>
          <wire name="ADDR[14]" tap_mode="classic"/>
          <wire name="ADDR[15]" tap_mode="classic"/>
          <wire name="ADDR[1]" tap_mode="classic"/>
          <wire name="ADDR[2]" tap_mode="classic"/>
          <wire name="ADDR[3]" tap_mode="classic"/>
          <wire name="ADDR[4]" tap_mode="classic"/>
          <wire name="ADDR[5]" tap_mode="classic"/>
          <wire name="ADDR[6]" tap_mode="classic"/>
          <wire name="ADDR[7]" tap_mode="classic"/>
          <wire name="ADDR[8]" tap_mode="classic"/>
          <wire name="ADDR[9]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[0]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[10]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[11]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[12]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[13]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[14]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[15]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[1]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[2]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[3]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[4]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[5]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[6]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[7]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[8]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[9]" tap_mode="classic"/>
        </data_input_vec>
        <storage_qualifier_input_vec>
          <wire name="ADDR[0]" tap_mode="classic"/>
          <wire name="ADDR[10]" tap_mode="classic"/>
          <wire name="ADDR[11]" tap_mode="classic"/>
          <wire name="ADDR[12]" tap_mode="classic"/>
          <wire name="ADDR[13]" tap_mode="classic"/>
          <wire name="ADDR[14]" tap_mode="classic"/>
          <wire name="ADDR[15]" tap_mode="classic"/>
          <wire name="ADDR[1]" tap_mode="classic"/>
          <wire name="ADDR[2]" tap_mode="classic"/>
          <wire name="ADDR[3]" tap_mode="classic"/>
          <wire name="ADDR[4]" tap_mode="classic"/>
          <wire name="ADDR[5]" tap_mode="classic"/>
          <wire name="ADDR[6]" tap_mode="classic"/>
          <wire name="ADDR[7]" tap_mode="classic"/>
          <wire name="ADDR[8]" tap_mode="classic"/>
          <wire name="ADDR[9]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[0]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[10]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[11]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[12]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[13]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[14]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[15]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[1]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[2]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[3]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[4]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[5]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[6]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[7]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[8]" tap_mode="classic"/>
          <wire name="STM32_IN:inst4|DBOUT[9]" tap_mode="classic"/>
        </storage_qualifier_input_vec>
      </signal_vec>
      <presentation>
        <unified_setup_data_view>
          <node is_selected="false" level-0="alt_or" name="ADDR[0..15]" order="lsb_to_msb" radix="unsigned_dec" storage-0="alt_or" storage-1="alt_or" storage-2="alt_or" type="combinatorial">
            <node data_index="0" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[0]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="0" tap_mode="classic" trigger_index="0" type="unknown"/>
            <node data_index="7" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[1]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="7" tap_mode="classic" trigger_index="7" type="unknown"/>
            <node data_index="8" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[2]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="8" tap_mode="classic" trigger_index="8" type="unknown"/>
            <node data_index="9" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[3]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="9" tap_mode="classic" trigger_index="9" type="unknown"/>
            <node data_index="10" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[4]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="10" tap_mode="classic" trigger_index="10" type="unknown"/>
            <node data_index="11" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[5]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="11" tap_mode="classic" trigger_index="11" type="unknown"/>
            <node data_index="12" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[6]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="12" tap_mode="classic" trigger_index="12" type="unknown"/>
            <node data_index="13" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[7]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="13" tap_mode="classic" trigger_index="13" type="unknown"/>
            <node data_index="14" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[8]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="14" tap_mode="classic" trigger_index="14" type="unknown"/>
            <node data_index="15" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[9]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="15" tap_mode="classic" trigger_index="15" type="unknown"/>
            <node data_index="1" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[10]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="1" tap_mode="classic" trigger_index="1" type="unknown"/>
            <node data_index="2" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[11]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="2" tap_mode="classic" trigger_index="2" type="unknown"/>
            <node data_index="3" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[12]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="3" tap_mode="classic" trigger_index="3" type="unknown"/>
            <node data_index="4" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[13]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="4" tap_mode="classic" trigger_index="4" type="unknown"/>
            <node data_index="5" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[14]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="5" tap_mode="classic" trigger_index="5" type="unknown"/>
            <node data_index="6" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[15]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="6" tap_mode="classic" trigger_index="6" type="unknown"/>
          </node>
          <node is_selected="false" level-0="alt_or" name="STM32_IN:inst4|DBOUT[0..15]" order="lsb_to_msb" radix="unsigned_dec" state="collapse" storage-0="alt_or" storage-1="alt_or" storage-2="alt_or" type="combinatorial">
            <node data_index="16" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[0]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="16" tap_mode="classic" trigger_index="16" type="unknown"/>
            <node data_index="23" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[1]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="23" tap_mode="classic" trigger_index="23" type="unknown"/>
            <node data_index="24" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[2]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="24" tap_mode="classic" trigger_index="24" type="unknown"/>
            <node data_index="25" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[3]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="25" tap_mode="classic" trigger_index="25" type="unknown"/>
            <node data_index="26" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[4]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="26" tap_mode="classic" trigger_index="26" type="unknown"/>
            <node data_index="27" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[5]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="27" tap_mode="classic" trigger_index="27" type="unknown"/>
            <node data_index="28" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[6]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="28" tap_mode="classic" trigger_index="28" type="unknown"/>
            <node data_index="29" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[7]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="29" tap_mode="classic" trigger_index="29" type="unknown"/>
            <node data_index="30" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[8]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="30" tap_mode="classic" trigger_index="30" type="unknown"/>
            <node data_index="31" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[9]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="31" tap_mode="classic" trigger_index="31" type="unknown"/>
            <node data_index="17" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[10]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="17" tap_mode="classic" trigger_index="17" type="unknown"/>
            <node data_index="18" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[11]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="18" tap_mode="classic" trigger_index="18" type="unknown"/>
            <node data_index="19" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[12]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="19" tap_mode="classic" trigger_index="19" type="unknown"/>
            <node data_index="20" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[13]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="20" tap_mode="classic" trigger_index="20" type="unknown"/>
            <node data_index="21" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[14]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="21" tap_mode="classic" trigger_index="21" type="unknown"/>
            <node data_index="22" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[15]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="22" tap_mode="classic" trigger_index="22" type="unknown"/>
          </node>
        </unified_setup_data_view>
        <data_view>
          <bus is_selected="false" level-0="alt_or" name="ADDR[0..15]" order="lsb_to_msb" radix="unsigned_dec" storage-0="alt_or" storage-1="alt_or" storage-2="alt_or" type="combinatorial">
            <net data_index="0" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[0]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="0" tap_mode="classic" trigger_index="0" type="unknown"/>
            <net data_index="7" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[1]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="7" tap_mode="classic" trigger_index="7" type="unknown"/>
            <net data_index="8" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[2]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="8" tap_mode="classic" trigger_index="8" type="unknown"/>
            <net data_index="9" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[3]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="9" tap_mode="classic" trigger_index="9" type="unknown"/>
            <net data_index="10" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[4]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="10" tap_mode="classic" trigger_index="10" type="unknown"/>
            <net data_index="11" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[5]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="11" tap_mode="classic" trigger_index="11" type="unknown"/>
            <net data_index="12" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[6]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="12" tap_mode="classic" trigger_index="12" type="unknown"/>
            <net data_index="13" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[7]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="13" tap_mode="classic" trigger_index="13" type="unknown"/>
            <net data_index="14" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[8]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="14" tap_mode="classic" trigger_index="14" type="unknown"/>
            <net data_index="15" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[9]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="15" tap_mode="classic" trigger_index="15" type="unknown"/>
            <net data_index="1" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[10]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="1" tap_mode="classic" trigger_index="1" type="unknown"/>
            <net data_index="2" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[11]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="2" tap_mode="classic" trigger_index="2" type="unknown"/>
            <net data_index="3" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[12]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="3" tap_mode="classic" trigger_index="3" type="unknown"/>
            <net data_index="4" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[13]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="4" tap_mode="classic" trigger_index="4" type="unknown"/>
            <net data_index="5" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[14]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="5" tap_mode="classic" trigger_index="5" type="unknown"/>
            <net data_index="6" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[15]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="6" tap_mode="classic" trigger_index="6" type="unknown"/>
          </bus>
          <bus is_selected="false" level-0="alt_or" name="STM32_IN:inst4|DBOUT[0..15]" order="lsb_to_msb" radix="unsigned_dec" state="collapse" storage-0="alt_or" storage-1="alt_or" storage-2="alt_or" type="combinatorial">
            <net data_index="16" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[0]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="16" tap_mode="classic" trigger_index="16" type="unknown"/>
            <net data_index="23" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[1]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="23" tap_mode="classic" trigger_index="23" type="unknown"/>
            <net data_index="24" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[2]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="24" tap_mode="classic" trigger_index="24" type="unknown"/>
            <net data_index="25" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[3]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="25" tap_mode="classic" trigger_index="25" type="unknown"/>
            <net data_index="26" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[4]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="26" tap_mode="classic" trigger_index="26" type="unknown"/>
            <net data_index="27" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[5]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="27" tap_mode="classic" trigger_index="27" type="unknown"/>
            <net data_index="28" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[6]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="28" tap_mode="classic" trigger_index="28" type="unknown"/>
            <net data_index="29" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[7]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="29" tap_mode="classic" trigger_index="29" type="unknown"/>
            <net data_index="30" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[8]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="30" tap_mode="classic" trigger_index="30" type="unknown"/>
            <net data_index="31" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[9]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="31" tap_mode="classic" trigger_index="31" type="unknown"/>
            <net data_index="17" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[10]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="17" tap_mode="classic" trigger_index="17" type="unknown"/>
            <net data_index="18" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[11]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="18" tap_mode="classic" trigger_index="18" type="unknown"/>
            <net data_index="19" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[12]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="19" tap_mode="classic" trigger_index="19" type="unknown"/>
            <net data_index="20" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[13]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="20" tap_mode="classic" trigger_index="20" type="unknown"/>
            <net data_index="21" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[14]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="21" tap_mode="classic" trigger_index="21" type="unknown"/>
            <net data_index="22" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[15]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="22" tap_mode="classic" trigger_index="22" type="unknown"/>
          </bus>
        </data_view>
        <setup_view>
          <bus is_selected="false" level-0="alt_or" name="ADDR[0..15]" order="lsb_to_msb" radix="unsigned_dec" storage-0="alt_or" storage-1="alt_or" storage-2="alt_or" type="combinatorial">
            <net data_index="0" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[0]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="0" tap_mode="classic" trigger_index="0" type="unknown"/>
            <net data_index="7" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[1]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="7" tap_mode="classic" trigger_index="7" type="unknown"/>
            <net data_index="8" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[2]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="8" tap_mode="classic" trigger_index="8" type="unknown"/>
            <net data_index="9" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[3]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="9" tap_mode="classic" trigger_index="9" type="unknown"/>
            <net data_index="10" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[4]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="10" tap_mode="classic" trigger_index="10" type="unknown"/>
            <net data_index="11" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[5]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="11" tap_mode="classic" trigger_index="11" type="unknown"/>
            <net data_index="12" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[6]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="12" tap_mode="classic" trigger_index="12" type="unknown"/>
            <net data_index="13" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[7]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="13" tap_mode="classic" trigger_index="13" type="unknown"/>
            <net data_index="14" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[8]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="14" tap_mode="classic" trigger_index="14" type="unknown"/>
            <net data_index="15" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[9]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="15" tap_mode="classic" trigger_index="15" type="unknown"/>
            <net data_index="1" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[10]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="1" tap_mode="classic" trigger_index="1" type="unknown"/>
            <net data_index="2" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[11]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="2" tap_mode="classic" trigger_index="2" type="unknown"/>
            <net data_index="3" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[12]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="3" tap_mode="classic" trigger_index="3" type="unknown"/>
            <net data_index="4" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[13]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="4" tap_mode="classic" trigger_index="4" type="unknown"/>
            <net data_index="5" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[14]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="5" tap_mode="classic" trigger_index="5" type="unknown"/>
            <net data_index="6" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="ADDR[15]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="6" tap_mode="classic" trigger_index="6" type="unknown"/>
          </bus>
          <bus is_selected="false" level-0="alt_or" name="STM32_IN:inst4|DBOUT[0..15]" order="lsb_to_msb" radix="unsigned_dec" state="collapse" storage-0="alt_or" storage-1="alt_or" storage-2="alt_or" type="combinatorial">
            <net data_index="16" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[0]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="16" tap_mode="classic" trigger_index="16" type="unknown"/>
            <net data_index="23" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[1]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="23" tap_mode="classic" trigger_index="23" type="unknown"/>
            <net data_index="24" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[2]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="24" tap_mode="classic" trigger_index="24" type="unknown"/>
            <net data_index="25" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[3]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="25" tap_mode="classic" trigger_index="25" type="unknown"/>
            <net data_index="26" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[4]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="26" tap_mode="classic" trigger_index="26" type="unknown"/>
            <net data_index="27" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[5]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="27" tap_mode="classic" trigger_index="27" type="unknown"/>
            <net data_index="28" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[6]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="28" tap_mode="classic" trigger_index="28" type="unknown"/>
            <net data_index="29" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[7]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="29" tap_mode="classic" trigger_index="29" type="unknown"/>
            <net data_index="30" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[8]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="30" tap_mode="classic" trigger_index="30" type="unknown"/>
            <net data_index="31" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[9]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="31" tap_mode="classic" trigger_index="31" type="unknown"/>
            <net data_index="17" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[10]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="17" tap_mode="classic" trigger_index="17" type="unknown"/>
            <net data_index="18" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[11]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="18" tap_mode="classic" trigger_index="18" type="unknown"/>
            <net data_index="19" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[12]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="19" tap_mode="classic" trigger_index="19" type="unknown"/>
            <net data_index="20" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[13]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="20" tap_mode="classic" trigger_index="20" type="unknown"/>
            <net data_index="21" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[14]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="21" tap_mode="classic" trigger_index="21" type="unknown"/>
            <net data_index="22" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" level-0="rising edge" name="STM32_IN:inst4|DBOUT[15]" pwr_level-0="dont_care" pwr_storage-0="dont_care" pwr_storage-1="dont_care" pwr_storage-2="dont_care" storage-0="dont_care" storage-1="dont_care" storage-2="dont_care" storage_index="22" tap_mode="classic" trigger_index="22" type="unknown"/>
          </bus>
        </setup_view>
        <trigger_in_editor/>
        <trigger_out_editor/>
      </presentation>
      <trigger CRC="259624DD" attribute_mem_mode="false" gap_record="true" global_temp="1" is_expanded="true" name="trigger: 2024/07/13 20:39:16  #0" position="pre" power_up_trigger_mode="false" record_data_gap="true" segment_size="1024" storage_mode="off" storage_qualifier_disabled="no" storage_qualifier_port_is_pin="true" storage_qualifier_port_name="auto_stp_external_storage_qualifier" storage_qualifier_port_tap_mode="classic" trigger_type="circular">
        <power_up_trigger position="pre" storage_qualifier_disabled="no"/>
        <events use_custom_flow_control="no">
          <level enabled="yes" name="condition1" type="basic">'ADDR[0]' == rising edge &amp;&amp; 'ADDR[10]' == rising edge &amp;&amp; 'ADDR[11]' == rising edge &amp;&amp; 'ADDR[12]' == rising edge &amp;&amp; 'ADDR[13]' == rising edge &amp;&amp; 'ADDR[14]' == rising edge &amp;&amp; 'ADDR[15]' == rising edge &amp;&amp; 'ADDR[1]' == rising edge &amp;&amp; 'ADDR[2]' == rising edge &amp;&amp; 'ADDR[3]' == rising edge &amp;&amp; 'ADDR[4]' == rising edge &amp;&amp; 'ADDR[5]' == rising edge &amp;&amp; 'ADDR[6]' == rising edge &amp;&amp; 'ADDR[7]' == rising edge &amp;&amp; 'ADDR[8]' == rising edge &amp;&amp; 'ADDR[9]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[0]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[10]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[11]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[12]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[13]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[14]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[15]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[1]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[2]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[3]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[4]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[5]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[6]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[7]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[8]' == rising edge &amp;&amp; 'STM32_IN:inst4|DBOUT[9]' == rising edge
            <power_up enabled="yes">
            </power_up>
            <op_node/>
          </level>
        </events>
        <storage_qualifier_events>
          <transitional>11111111111111111111111111111111
            <pwr_up_transitional>11111111111111111111111111111111</pwr_up_transitional>
          </transitional>
          <storage_qualifier_level type="basic">
            <power_up>
            </power_up>
            <op_node/>
          </storage_qualifier_level>
          <storage_qualifier_level type="basic">
            <power_up>
            </power_up>
            <op_node/>
          </storage_qualifier_level>
          <storage_qualifier_level type="basic">
            <power_up>
            </power_up>
            <op_node/>
          </storage_qualifier_level>
        </storage_qualifier_events>
        <log>
          <data global_temp="1" name="log: Trig @ 2024/07/13 20:39:20 (0:0:3.4 elapsed)" power_up_mode="false" sample_depth="1023" trigger_position="-1">100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000100000010000000000000000000000001000000100000000000000000000000010000001000000000000000000000000</data>
          <extradata>111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111</extradata>
        </log>
      </trigger>
    </signal_set>
    <signal_set is_expanded="true" name="signal_set: 2025/07/17 15:28:28  #0">
      <clock name="DA1CLK" polarity="posedge" tap_mode="classic"/>
      <config pipeline_level="0" ram_type="AUTO" reserved_data_nodes="0" reserved_storage_qualifier_nodes="0" reserved_trigger_nodes="0" sample_depth="2048" trigger_in_enable="no" trigger_out_enable="no"/>
      <top_entity/>
      <signal_vec>
        <trigger_input_vec>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[0]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[1]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[2]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[3]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[4]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[5]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[6]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[7]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[8]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[9]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[0]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[1]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[2]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[3]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[4]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[5]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[6]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[7]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[8]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[9]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[0]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[1]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[2]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[3]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[4]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[5]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[6]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[7]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[8]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[9]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[0]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[1]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[2]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[3]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[4]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[5]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[6]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[7]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[8]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[9]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|flag_reg" tap_mode="classic"/>
        </trigger_input_vec>
        <data_input_vec>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[0]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[1]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[2]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[3]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[4]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[5]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[6]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[7]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[8]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[9]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[0]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[1]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[2]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[3]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[4]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[5]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[6]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[7]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[8]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[9]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[0]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[1]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[2]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[3]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[4]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[5]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[6]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[7]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[8]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[9]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[0]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[1]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[2]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[3]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[4]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[5]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[6]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[7]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[8]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[9]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|flag_reg" tap_mode="classic"/>
        </data_input_vec>
        <storage_qualifier_input_vec>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[0]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[1]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[2]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[3]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[4]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[5]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[6]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[7]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[8]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A[9]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[0]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[1]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[2]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[3]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[4]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[5]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[6]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[7]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[8]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[9]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[0]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[1]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[2]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[3]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[4]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[5]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[6]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[7]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[8]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B[9]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[0]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[1]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[2]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[3]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[4]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[5]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[6]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[7]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[8]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[9]" tap_mode="classic"/>
          <wire name="DA_PARAMETER_CTRL:inst6|flag_reg" tap_mode="classic"/>
        </storage_qualifier_input_vec>
      </signal_vec>
      <presentation>
        <unified_setup_data_view>
          <node data_index="40" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|flag_reg" storage_index="40" tap_mode="classic" trigger_index="40" type="unknown"/>
          <node is_selected="false" level-0="alt_or" name="DA_PARAMETER_CTRL:inst6|COUT_A[9..0]" order="msb_to_lsb" radix="unsigned_dec" type="combinatorial">
            <node data_index="9" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[9]" storage_index="9" tap_mode="classic" trigger_index="9" type="unknown"/>
            <node data_index="8" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[8]" storage_index="8" tap_mode="classic" trigger_index="8" type="unknown"/>
            <node data_index="7" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[7]" storage_index="7" tap_mode="classic" trigger_index="7" type="unknown"/>
            <node data_index="6" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[6]" storage_index="6" tap_mode="classic" trigger_index="6" type="unknown"/>
            <node data_index="5" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[5]" storage_index="5" tap_mode="classic" trigger_index="5" type="unknown"/>
            <node data_index="4" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[4]" storage_index="4" tap_mode="classic" trigger_index="4" type="unknown"/>
            <node data_index="3" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[3]" storage_index="3" tap_mode="classic" trigger_index="3" type="unknown"/>
            <node data_index="2" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[2]" storage_index="2" tap_mode="classic" trigger_index="2" type="unknown"/>
            <node data_index="1" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[1]" storage_index="1" tap_mode="classic" trigger_index="1" type="unknown"/>
            <node data_index="0" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[0]" storage_index="0" tap_mode="classic" trigger_index="0" type="unknown"/>
          </node>
          <node is_selected="false" level-0="alt_or" name="DA_PARAMETER_CTRL:inst6|COUT_B[9..0]" order="msb_to_lsb" radix="unsigned_dec" type="combinatorial">
            <node data_index="29" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[9]" storage_index="29" tap_mode="classic" trigger_index="29" type="unknown"/>
            <node data_index="28" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[8]" storage_index="28" tap_mode="classic" trigger_index="28" type="unknown"/>
            <node data_index="27" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[7]" storage_index="27" tap_mode="classic" trigger_index="27" type="unknown"/>
            <node data_index="26" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[6]" storage_index="26" tap_mode="classic" trigger_index="26" type="unknown"/>
            <node data_index="25" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[5]" storage_index="25" tap_mode="classic" trigger_index="25" type="unknown"/>
            <node data_index="24" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[4]" storage_index="24" tap_mode="classic" trigger_index="24" type="unknown"/>
            <node data_index="23" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[3]" storage_index="23" tap_mode="classic" trigger_index="23" type="unknown"/>
            <node data_index="22" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[2]" storage_index="22" tap_mode="classic" trigger_index="22" type="unknown"/>
            <node data_index="21" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[1]" storage_index="21" tap_mode="classic" trigger_index="21" type="unknown"/>
            <node data_index="20" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[0]" storage_index="20" tap_mode="classic" trigger_index="20" type="unknown"/>
          </node>
          <node is_selected="false" level-0="alt_or" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[9..0]" order="msb_to_lsb" radix="unsigned_dec" type="combinatorial">
            <node data_index="19" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[9]" storage_index="19" tap_mode="classic" trigger_index="19" type="unknown"/>
            <node data_index="18" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[8]" storage_index="18" tap_mode="classic" trigger_index="18" type="unknown"/>
            <node data_index="17" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[7]" storage_index="17" tap_mode="classic" trigger_index="17" type="unknown"/>
            <node data_index="16" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[6]" storage_index="16" tap_mode="classic" trigger_index="16" type="unknown"/>
            <node data_index="15" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[5]" storage_index="15" tap_mode="classic" trigger_index="15" type="unknown"/>
            <node data_index="14" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[4]" storage_index="14" tap_mode="classic" trigger_index="14" type="unknown"/>
            <node data_index="13" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[3]" storage_index="13" tap_mode="classic" trigger_index="13" type="unknown"/>
            <node data_index="12" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[2]" storage_index="12" tap_mode="classic" trigger_index="12" type="unknown"/>
            <node data_index="11" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[1]" storage_index="11" tap_mode="classic" trigger_index="11" type="unknown"/>
            <node data_index="10" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[0]" storage_index="10" tap_mode="classic" trigger_index="10" type="unknown"/>
          </node>
          <node is_selected="true" level-0="alt_or" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[9..0]" order="msb_to_lsb" radix="unsigned_dec" type="combinatorial">
            <node data_index="39" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[9]" storage_index="39" tap_mode="classic" trigger_index="39" type="unknown"/>
            <node data_index="38" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[8]" storage_index="38" tap_mode="classic" trigger_index="38" type="unknown"/>
            <node data_index="37" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[7]" storage_index="37" tap_mode="classic" trigger_index="37" type="unknown"/>
            <node data_index="36" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[6]" storage_index="36" tap_mode="classic" trigger_index="36" type="unknown"/>
            <node data_index="35" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[5]" storage_index="35" tap_mode="classic" trigger_index="35" type="unknown"/>
            <node data_index="34" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[4]" storage_index="34" tap_mode="classic" trigger_index="34" type="unknown"/>
            <node data_index="33" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[3]" storage_index="33" tap_mode="classic" trigger_index="33" type="unknown"/>
            <node data_index="32" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[2]" storage_index="32" tap_mode="classic" trigger_index="32" type="unknown"/>
            <node data_index="31" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[1]" storage_index="31" tap_mode="classic" trigger_index="31" type="unknown"/>
            <node data_index="30" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[0]" storage_index="30" tap_mode="classic" trigger_index="30" type="unknown"/>
          </node>
        </unified_setup_data_view>
        <data_view>
          <net data_index="40" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|flag_reg" storage_index="40" tap_mode="classic" trigger_index="40" type="unknown"/>
          <bus is_selected="false" level-0="alt_or" name="DA_PARAMETER_CTRL:inst6|COUT_A[9..0]" order="msb_to_lsb" radix="unsigned_dec" type="combinatorial">
            <net data_index="9" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[9]" storage_index="9" tap_mode="classic" trigger_index="9" type="unknown"/>
            <net data_index="8" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[8]" storage_index="8" tap_mode="classic" trigger_index="8" type="unknown"/>
            <net data_index="7" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[7]" storage_index="7" tap_mode="classic" trigger_index="7" type="unknown"/>
            <net data_index="6" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[6]" storage_index="6" tap_mode="classic" trigger_index="6" type="unknown"/>
            <net data_index="5" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[5]" storage_index="5" tap_mode="classic" trigger_index="5" type="unknown"/>
            <net data_index="4" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[4]" storage_index="4" tap_mode="classic" trigger_index="4" type="unknown"/>
            <net data_index="3" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[3]" storage_index="3" tap_mode="classic" trigger_index="3" type="unknown"/>
            <net data_index="2" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[2]" storage_index="2" tap_mode="classic" trigger_index="2" type="unknown"/>
            <net data_index="1" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[1]" storage_index="1" tap_mode="classic" trigger_index="1" type="unknown"/>
            <net data_index="0" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[0]" storage_index="0" tap_mode="classic" trigger_index="0" type="unknown"/>
          </bus>
          <bus is_selected="false" level-0="alt_or" name="DA_PARAMETER_CTRL:inst6|COUT_B[9..0]" order="msb_to_lsb" radix="unsigned_dec" type="combinatorial">
            <net data_index="29" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[9]" storage_index="29" tap_mode="classic" trigger_index="29" type="unknown"/>
            <net data_index="28" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[8]" storage_index="28" tap_mode="classic" trigger_index="28" type="unknown"/>
            <net data_index="27" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[7]" storage_index="27" tap_mode="classic" trigger_index="27" type="unknown"/>
            <net data_index="26" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[6]" storage_index="26" tap_mode="classic" trigger_index="26" type="unknown"/>
            <net data_index="25" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[5]" storage_index="25" tap_mode="classic" trigger_index="25" type="unknown"/>
            <net data_index="24" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[4]" storage_index="24" tap_mode="classic" trigger_index="24" type="unknown"/>
            <net data_index="23" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[3]" storage_index="23" tap_mode="classic" trigger_index="23" type="unknown"/>
            <net data_index="22" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[2]" storage_index="22" tap_mode="classic" trigger_index="22" type="unknown"/>
            <net data_index="21" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[1]" storage_index="21" tap_mode="classic" trigger_index="21" type="unknown"/>
            <net data_index="20" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[0]" storage_index="20" tap_mode="classic" trigger_index="20" type="unknown"/>
          </bus>
          <bus is_selected="false" level-0="alt_or" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[9..0]" order="msb_to_lsb" radix="unsigned_dec" type="combinatorial">
            <net data_index="19" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[9]" storage_index="19" tap_mode="classic" trigger_index="19" type="unknown"/>
            <net data_index="18" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[8]" storage_index="18" tap_mode="classic" trigger_index="18" type="unknown"/>
            <net data_index="17" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[7]" storage_index="17" tap_mode="classic" trigger_index="17" type="unknown"/>
            <net data_index="16" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[6]" storage_index="16" tap_mode="classic" trigger_index="16" type="unknown"/>
            <net data_index="15" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[5]" storage_index="15" tap_mode="classic" trigger_index="15" type="unknown"/>
            <net data_index="14" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[4]" storage_index="14" tap_mode="classic" trigger_index="14" type="unknown"/>
            <net data_index="13" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[3]" storage_index="13" tap_mode="classic" trigger_index="13" type="unknown"/>
            <net data_index="12" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[2]" storage_index="12" tap_mode="classic" trigger_index="12" type="unknown"/>
            <net data_index="11" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[1]" storage_index="11" tap_mode="classic" trigger_index="11" type="unknown"/>
            <net data_index="10" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[0]" storage_index="10" tap_mode="classic" trigger_index="10" type="unknown"/>
          </bus>
          <bus is_selected="true" level-0="alt_or" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[9..0]" order="msb_to_lsb" radix="unsigned_dec" type="combinatorial">
            <net data_index="39" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[9]" storage_index="39" tap_mode="classic" trigger_index="39" type="unknown"/>
            <net data_index="38" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[8]" storage_index="38" tap_mode="classic" trigger_index="38" type="unknown"/>
            <net data_index="37" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[7]" storage_index="37" tap_mode="classic" trigger_index="37" type="unknown"/>
            <net data_index="36" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[6]" storage_index="36" tap_mode="classic" trigger_index="36" type="unknown"/>
            <net data_index="35" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[5]" storage_index="35" tap_mode="classic" trigger_index="35" type="unknown"/>
            <net data_index="34" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[4]" storage_index="34" tap_mode="classic" trigger_index="34" type="unknown"/>
            <net data_index="33" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[3]" storage_index="33" tap_mode="classic" trigger_index="33" type="unknown"/>
            <net data_index="32" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[2]" storage_index="32" tap_mode="classic" trigger_index="32" type="unknown"/>
            <net data_index="31" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[1]" storage_index="31" tap_mode="classic" trigger_index="31" type="unknown"/>
            <net data_index="30" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[0]" storage_index="30" tap_mode="classic" trigger_index="30" type="unknown"/>
          </bus>
        </data_view>
        <setup_view>
          <net data_index="40" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|flag_reg" storage_index="40" tap_mode="classic" trigger_index="40" type="unknown"/>
          <bus is_selected="false" level-0="alt_or" name="DA_PARAMETER_CTRL:inst6|COUT_A[9..0]" order="msb_to_lsb" radix="unsigned_dec" type="combinatorial">
            <net data_index="9" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[9]" storage_index="9" tap_mode="classic" trigger_index="9" type="unknown"/>
            <net data_index="8" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[8]" storage_index="8" tap_mode="classic" trigger_index="8" type="unknown"/>
            <net data_index="7" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[7]" storage_index="7" tap_mode="classic" trigger_index="7" type="unknown"/>
            <net data_index="6" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[6]" storage_index="6" tap_mode="classic" trigger_index="6" type="unknown"/>
            <net data_index="5" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[5]" storage_index="5" tap_mode="classic" trigger_index="5" type="unknown"/>
            <net data_index="4" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[4]" storage_index="4" tap_mode="classic" trigger_index="4" type="unknown"/>
            <net data_index="3" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[3]" storage_index="3" tap_mode="classic" trigger_index="3" type="unknown"/>
            <net data_index="2" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[2]" storage_index="2" tap_mode="classic" trigger_index="2" type="unknown"/>
            <net data_index="1" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[1]" storage_index="1" tap_mode="classic" trigger_index="1" type="unknown"/>
            <net data_index="0" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A[0]" storage_index="0" tap_mode="classic" trigger_index="0" type="unknown"/>
          </bus>
          <bus is_selected="false" level-0="alt_or" name="DA_PARAMETER_CTRL:inst6|COUT_B[9..0]" order="msb_to_lsb" radix="unsigned_dec" type="combinatorial">
            <net data_index="29" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[9]" storage_index="29" tap_mode="classic" trigger_index="29" type="unknown"/>
            <net data_index="28" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[8]" storage_index="28" tap_mode="classic" trigger_index="28" type="unknown"/>
            <net data_index="27" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[7]" storage_index="27" tap_mode="classic" trigger_index="27" type="unknown"/>
            <net data_index="26" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[6]" storage_index="26" tap_mode="classic" trigger_index="26" type="unknown"/>
            <net data_index="25" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[5]" storage_index="25" tap_mode="classic" trigger_index="25" type="unknown"/>
            <net data_index="24" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[4]" storage_index="24" tap_mode="classic" trigger_index="24" type="unknown"/>
            <net data_index="23" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[3]" storage_index="23" tap_mode="classic" trigger_index="23" type="unknown"/>
            <net data_index="22" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[2]" storage_index="22" tap_mode="classic" trigger_index="22" type="unknown"/>
            <net data_index="21" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[1]" storage_index="21" tap_mode="classic" trigger_index="21" type="unknown"/>
            <net data_index="20" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B[0]" storage_index="20" tap_mode="classic" trigger_index="20" type="unknown"/>
          </bus>
          <bus is_selected="false" level-0="alt_or" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[9..0]" order="msb_to_lsb" radix="unsigned_dec" type="combinatorial">
            <net data_index="19" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[9]" storage_index="19" tap_mode="classic" trigger_index="19" type="unknown"/>
            <net data_index="18" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[8]" storage_index="18" tap_mode="classic" trigger_index="18" type="unknown"/>
            <net data_index="17" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[7]" storage_index="17" tap_mode="classic" trigger_index="17" type="unknown"/>
            <net data_index="16" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[6]" storage_index="16" tap_mode="classic" trigger_index="16" type="unknown"/>
            <net data_index="15" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[5]" storage_index="15" tap_mode="classic" trigger_index="15" type="unknown"/>
            <net data_index="14" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[4]" storage_index="14" tap_mode="classic" trigger_index="14" type="unknown"/>
            <net data_index="13" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[3]" storage_index="13" tap_mode="classic" trigger_index="13" type="unknown"/>
            <net data_index="12" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[2]" storage_index="12" tap_mode="classic" trigger_index="12" type="unknown"/>
            <net data_index="11" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[1]" storage_index="11" tap_mode="classic" trigger_index="11" type="unknown"/>
            <net data_index="10" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_A_FINAL[0]" storage_index="10" tap_mode="classic" trigger_index="10" type="unknown"/>
          </bus>
          <bus is_selected="true" level-0="alt_or" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[9..0]" order="msb_to_lsb" radix="unsigned_dec" type="combinatorial">
            <net data_index="39" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[9]" storage_index="39" tap_mode="classic" trigger_index="39" type="unknown"/>
            <net data_index="38" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[8]" storage_index="38" tap_mode="classic" trigger_index="38" type="unknown"/>
            <net data_index="37" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[7]" storage_index="37" tap_mode="classic" trigger_index="37" type="unknown"/>
            <net data_index="36" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[6]" storage_index="36" tap_mode="classic" trigger_index="36" type="unknown"/>
            <net data_index="35" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[5]" storage_index="35" tap_mode="classic" trigger_index="35" type="unknown"/>
            <net data_index="34" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[4]" storage_index="34" tap_mode="classic" trigger_index="34" type="unknown"/>
            <net data_index="33" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[3]" storage_index="33" tap_mode="classic" trigger_index="33" type="unknown"/>
            <net data_index="32" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[2]" storage_index="32" tap_mode="classic" trigger_index="32" type="unknown"/>
            <net data_index="31" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[1]" storage_index="31" tap_mode="classic" trigger_index="31" type="unknown"/>
            <net data_index="30" duplicate_name_allowed="false" is_data_input="true" is_node_valid="true" is_selected="false" is_storage_input="true" is_trigger_input="true" name="DA_PARAMETER_CTRL:inst6|COUT_B_FINAL[0]" storage_index="30" tap_mode="classic" trigger_index="30" type="unknown"/>
          </bus>
        </setup_view>
        <trigger_in_editor/>
        <trigger_out_editor/>
      </presentation>
      <trigger CRC="F5E82973" attribute_mem_mode="false" gap_record="true" is_expanded="true" name="trigger: 2025/07/17 15:28:28  #1" position="pre" power_up_trigger_mode="false" record_data_gap="true" segment_size="512" storage_mode="off" storage_qualifier_disabled="no" storage_qualifier_port_is_pin="true" storage_qualifier_port_name="auto_stp_external_storage_qualifier" storage_qualifier_port_tap_mode="classic" trigger_type="circular">
        <power_up_trigger position="pre" storage_qualifier_disabled="no"/>
        <events use_custom_flow_control="no">
          <level enabled="yes" name="condition1" type="basic">
            <power_up enabled="yes">
            </power_up>
            <op_node/>
          </level>
        </events>
        <storage_qualifier_events>
          <transitional>11111111111111111111111111111111111111111
            <pwr_up_transitional>11111111111111111111111111111111111111111</pwr_up_transitional>
          </transitional>
          <storage_qualifier_level type="basic">
            <power_up>
            </power_up>
            <op_node/>
          </storage_qualifier_level>
          <storage_qualifier_level type="basic">
            <power_up>
            </power_up>
            <op_node/>
          </storage_qualifier_level>
          <storage_qualifier_level type="basic">
            <power_up>
            </power_up>
            <op_node/>
          </storage_qualifier_level>
        </storage_qualifier_events>
      </trigger>
    </signal_set>
    <position_info>
      <single attribute="active tab" value="0"/>
      <single attribute="data horizontal scroll position" value="401"/>
      <single attribute="data vertical scroll position" value="0"/>
      <single attribute="setup horizontal scroll position" value="0"/>
      <single attribute="setup vertical scroll position" value="0"/>
      <single attribute="zoom level denominator" value="1"/>
      <single attribute="zoom level numerator" value="16384"/>
      <single attribute="zoom offset denominator" value="1"/>
      <single attribute="zoom offset numerator" value="0"/>
    </position_info>
  </instance>
  <mnemonics/>
  <static_plugin_mnemonics/>
  <global_info>
    <single attribute="active instance" value="0"/>
    <single attribute="config widget visible" value="1"/>
    <single attribute="data log widget visible" value="1"/>
    <single attribute="hierarchy widget visible" value="1"/>
    <single attribute="instance widget visible" value="1"/>
    <single attribute="jtag widget visible" value="1"/>
    <single attribute="lock mode" value="0"/>
    <single attribute="sof manager visible" value="0"/>
    <multi attribute="frame size" size="2" value="1920,991"/>
    <multi attribute="jtag widget size" size="2" value="461,146"/>
  </global_info>
</session>
