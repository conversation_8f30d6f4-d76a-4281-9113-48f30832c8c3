{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1752772060766 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus Prime " "Running Quartus Prime Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Standard Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Standard Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1752772060770 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Jul 18 01:07:40 2025 " "Processing started: Fri Jul 18 01:07:40 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1752772060770 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772060770 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off JYX_FPGA_OBJECT -c JYX_FPGA_OBJECT " "Command: quartus_map --read_settings_files=on --write_settings_files=off JYX_FPGA_OBJECT -c JYX_FPGA_OBJECT" {  } {  } 0 0 "Command: %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772060770 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "16 16 " "Parallel compilation is enabled and will use 16 of the 16 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Analysis & Synthesis" 0 -1 1752772061118 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/ad_freq_measure.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/ad_freq_measure.v" { { "Info" "ISGN_ENTITY_NAME" "1 AD_FREQ_MEASURE " "Found entity 1: AD_FREQ_MEASURE" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067720 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067720 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/cnt32.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/cnt32.v" { { "Info" "ISGN_ENTITY_NAME" "1 CNT32 " "Found entity 1: CNT32" {  } { { "../src/CNT32.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT32.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067721 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067721 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/ad_data_deal.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/ad_data_deal.v" { { "Info" "ISGN_ENTITY_NAME" "1 AD_DATA_DEAL " "Found entity 1: AD_DATA_DEAL" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067722 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067722 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/ad_freq_word.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/ad_freq_word.v" { { "Info" "ISGN_ENTITY_NAME" "1 AD_FREQ_WORD " "Found entity 1: AD_FREQ_WORD" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067723 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067723 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_freq_word.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_freq_word.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_FREQ_WORD " "Found entity 1: DA_FREQ_WORD" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067724 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067724 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/cnt10.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/cnt10.v" { { "Info" "ISGN_ENTITY_NAME" "1 CNT10 " "Found entity 1: CNT10" {  } { { "../src/CNT10.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT10.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067725 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067725 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/freq_dev.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/freq_dev.v" { { "Info" "ISGN_ENTITY_NAME" "1 FREQ_DEV " "Found entity 1: FREQ_DEV" {  } { { "../src/FREQ_DEV.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FREQ_DEV.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067727 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067727 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/master_ctrl.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/master_ctrl.v" { { "Info" "ISGN_ENTITY_NAME" "1 MASTER_CTRL " "Found entity 1: MASTER_CTRL" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067728 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067728 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/fmc/fmc_control.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/fmc/fmc_control.v" { { "Info" "ISGN_ENTITY_NAME" "1 fmc_control " "Found entity 1: fmc_control" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 2 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067728 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067728 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/test.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/test.v" { { "Info" "ISGN_ENTITY_NAME" "1 test " "Found entity 1: test" {  } { { "../src/test.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/test.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067729 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067729 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "top.bdf 1 1 " "Found 1 design units, including 1 entities, in source file top.bdf" { { "Info" "ISGN_ENTITY_NAME" "1 TOP " "Found entity 1: TOP" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { } } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067730 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067730 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/mypll.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/mypll.v" { { "Info" "ISGN_ENTITY_NAME" "1 MYPLL " "Found entity 1: MYPLL" {  } { { "../ip/MYPLL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/MYPLL.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067731 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067731 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sinrom.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sinrom.v" { { "Info" "ISGN_ENTITY_NAME" "1 SINROM " "Found entity 1: SINROM" {  } { { "../ip/SINROM.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/SINROM.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067732 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067732 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/tyfifo.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/tyfifo.v" { { "Info" "ISGN_ENTITY_NAME" "1 TYFIFO " "Found entity 1: TYFIFO" {  } { { "../ip/TYFIFO.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/TYFIFO.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067734 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067734 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sinromvpp/sinromvpp.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sinromvpp/sinromvpp.v" { { "Info" "ISGN_ENTITY_NAME" "1 sinromvpp " "Found entity 1: sinromvpp" {  } { { "../ip/sinromvpp/sinromvpp.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sinromvpp/sinromvpp.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067735 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067735 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/voltage_scaler_clocked.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/voltage_scaler_clocked.v" { { "Info" "ISGN_ENTITY_NAME" "1 voltage_scaler_clocked " "Found entity 1: voltage_scaler_clocked" {  } { { "../src/voltage_scaler_clocked.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067736 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067736 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_apmplitude.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_apmplitude.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_APMPLITUDE " "Found entity 1: DA_APMPLITUDE" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067737 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067737 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/cnt10_b.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/cnt10_b.v" { { "Info" "ISGN_ENTITY_NAME" "1 CNT10_B " "Found entity 1: CNT10_B" {  } { { "../src/CNT10_B.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT10_B.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067738 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067738 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/triangle_rom/triangle_rom.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/triangle_rom/triangle_rom.v" { { "Info" "ISGN_ENTITY_NAME" "1 triangle_rom " "Found entity 1: triangle_rom" {  } { { "../ip/triangle_rom/triangle_rom.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/triangle_rom/triangle_rom.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067739 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067739 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sqaure_rom/sqaure_rom.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sqaure_rom/sqaure_rom.v" { { "Info" "ISGN_ENTITY_NAME" "1 sqaure_rom " "Found entity 1: sqaure_rom" {  } { { "../ip/sqaure_rom/sqaure_rom.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sqaure_rom/sqaure_rom.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067741 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067741 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sawtooth_rom/sawtooth_rom.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sawtooth_rom/sawtooth_rom.v" { { "Info" "ISGN_ENTITY_NAME" "1 sawtooth_rom " "Found entity 1: sawtooth_rom" {  } { { "../ip/sawtooth_rom/sawtooth_rom.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sawtooth_rom/sawtooth_rom.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067742 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067742 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_waveform_a.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_waveform_a.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_WAVEFORM_A " "Found entity 1: DA_WAVEFORM_A" {  } { { "../src/DA_WAVEFORM_A.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067743 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067743 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_waveform_b.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_waveform_b.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_WAVEFORM_B " "Found entity 1: DA_WAVEFORM_B" {  } { { "../src/DA_WAVEFORM_B.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_B.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067744 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067744 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/gate_generator.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/gate_generator.v" { { "Info" "ISGN_ENTITY_NAME" "1 gate_generator " "Found entity 1: gate_generator" {  } { { "../src/gate_generator.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/gate_generator.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067745 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067745 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_clk_ctrl.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_clk_ctrl.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_CLK_CTRL " "Found entity 1: DA_CLK_CTRL" {  } { { "../src/DA_CLK_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_CLK_CTRL.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067747 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067747 ""}
{ "Warning" "WVRFX_L3_VERI_MIXED_BLOCKING_NONBLOCKING_ASSIGNMENT" "DA_PARAMETER_CTRL.v(69) " "Verilog HDL information at DA_PARAMETER_CTRL.v(69): always construct contains both blocking and non-blocking assignments" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 69 0 0 } }  } 0 10268 "Verilog HDL information at %1!s!: always construct contains both blocking and non-blocking assignments" 1 0 "Analysis & Synthesis" 0 -1 1752772067748 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_parameter_ctrl.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_parameter_ctrl.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_PARAMETER_CTRL " "Found entity 1: DA_PARAMETER_CTRL" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067748 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067748 ""}
{ "Info" "ISGN_START_ELABORATION_TOP" "TOP " "Elaborating entity \"TOP\" for the top level hierarchy" {  } {  } 0 12127 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "Analysis & Synthesis" 0 -1 1752772067789 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "DA_PARAMETER_CTRL DA_PARAMETER_CTRL:inst7 " "Elaborating entity \"DA_PARAMETER_CTRL\" for hierarchy \"DA_PARAMETER_CTRL:inst7\"" {  } { { "TOP.bdf" "inst7" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 88 3368 3656 328 "inst7" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772067793 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 10 DA_PARAMETER_CTRL.v(74) " "Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(74): truncated value with size 32 to match size of target (10)" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 74 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752772067794 "|TOP|DA_PARAMETER_CTRL:inst7"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 10 DA_PARAMETER_CTRL.v(79) " "Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(79): truncated value with size 32 to match size of target (10)" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 79 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752772067794 "|TOP|DA_PARAMETER_CTRL:inst7"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 10 DA_PARAMETER_CTRL.v(99) " "Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(99): truncated value with size 32 to match size of target (10)" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 99 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752772067794 "|TOP|DA_PARAMETER_CTRL:inst7"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 10 DA_PARAMETER_CTRL.v(104) " "Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(104): truncated value with size 32 to match size of target (10)" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 104 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752772067794 "|TOP|DA_PARAMETER_CTRL:inst7"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "MYPLL MYPLL:inst6 " "Elaborating entity \"MYPLL\" for hierarchy \"MYPLL:inst6\"" {  } { { "TOP.bdf" "inst6" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1248 2160 2480 1424 "inst6" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772067799 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altpll MYPLL:inst6\|altpll:altpll_component " "Elaborating entity \"altpll\" for hierarchy \"MYPLL:inst6\|altpll:altpll_component\"" {  } { { "../ip/MYPLL.v" "altpll_component" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/MYPLL.v" 90 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772067817 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "MYPLL:inst6\|altpll:altpll_component " "Elaborated megafunction instantiation \"MYPLL:inst6\|altpll:altpll_component\"" {  } { { "../ip/MYPLL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/MYPLL.v" 90 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772067818 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "MYPLL:inst6\|altpll:altpll_component " "Instantiated megafunction \"MYPLL:inst6\|altpll:altpll_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "bandwidth_type AUTO " "Parameter \"bandwidth_type\" = \"AUTO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_divide_by 1 " "Parameter \"clk0_divide_by\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_duty_cycle 50 " "Parameter \"clk0_duty_cycle\" = \"50\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_multiply_by 3 " "Parameter \"clk0_multiply_by\" = \"3\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_phase_shift 0 " "Parameter \"clk0_phase_shift\" = \"0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "compensate_clock CLK0 " "Parameter \"compensate_clock\" = \"CLK0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "inclk0_input_frequency 20000 " "Parameter \"inclk0_input_frequency\" = \"20000\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint CBX_MODULE_PREFIX=MYPLL " "Parameter \"lpm_hint\" = \"CBX_MODULE_PREFIX=MYPLL\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altpll " "Parameter \"lpm_type\" = \"altpll\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode NORMAL " "Parameter \"operation_mode\" = \"NORMAL\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "pll_type AUTO " "Parameter \"pll_type\" = \"AUTO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_activeclock PORT_UNUSED " "Parameter \"port_activeclock\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_areset PORT_UNUSED " "Parameter \"port_areset\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad0 PORT_UNUSED " "Parameter \"port_clkbad0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad1 PORT_UNUSED " "Parameter \"port_clkbad1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkloss PORT_UNUSED " "Parameter \"port_clkloss\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkswitch PORT_UNUSED " "Parameter \"port_clkswitch\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_configupdate PORT_UNUSED " "Parameter \"port_configupdate\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_fbin PORT_UNUSED " "Parameter \"port_fbin\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk0 PORT_USED " "Parameter \"port_inclk0\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk1 PORT_UNUSED " "Parameter \"port_inclk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_locked PORT_UNUSED " "Parameter \"port_locked\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pfdena PORT_UNUSED " "Parameter \"port_pfdena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasecounterselect PORT_UNUSED " "Parameter \"port_phasecounterselect\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasedone PORT_UNUSED " "Parameter \"port_phasedone\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasestep PORT_UNUSED " "Parameter \"port_phasestep\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phaseupdown PORT_UNUSED " "Parameter \"port_phaseupdown\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pllena PORT_UNUSED " "Parameter \"port_pllena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanaclr PORT_UNUSED " "Parameter \"port_scanaclr\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclk PORT_UNUSED " "Parameter \"port_scanclk\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclkena PORT_UNUSED " "Parameter \"port_scanclkena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandata PORT_UNUSED " "Parameter \"port_scandata\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandataout PORT_UNUSED " "Parameter \"port_scandataout\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandone PORT_UNUSED " "Parameter \"port_scandone\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanread PORT_UNUSED " "Parameter \"port_scanread\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanwrite PORT_UNUSED " "Parameter \"port_scanwrite\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk0 PORT_USED " "Parameter \"port_clk0\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk1 PORT_UNUSED " "Parameter \"port_clk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk2 PORT_UNUSED " "Parameter \"port_clk2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk3 PORT_UNUSED " "Parameter \"port_clk3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk4 PORT_UNUSED " "Parameter \"port_clk4\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk5 PORT_UNUSED " "Parameter \"port_clk5\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena0 PORT_UNUSED " "Parameter \"port_clkena0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena1 PORT_UNUSED " "Parameter \"port_clkena1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena2 PORT_UNUSED " "Parameter \"port_clkena2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena3 PORT_UNUSED " "Parameter \"port_clkena3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena4 PORT_UNUSED " "Parameter \"port_clkena4\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena5 PORT_UNUSED " "Parameter \"port_clkena5\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk0 PORT_UNUSED " "Parameter \"port_extclk0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk1 PORT_UNUSED " "Parameter \"port_extclk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk2 PORT_UNUSED " "Parameter \"port_extclk2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk3 PORT_UNUSED " "Parameter \"port_extclk3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_clock 5 " "Parameter \"width_clock\" = \"5\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772067818 ""}  } { { "../ip/MYPLL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/MYPLL.v" 90 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1752772067818 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/mypll_altpll1.v 1 1 " "Found 1 design units, including 1 entities, in source file db/mypll_altpll1.v" { { "Info" "ISGN_ENTITY_NAME" "1 MYPLL_altpll1 " "Found entity 1: MYPLL_altpll1" {  } { { "db/mypll_altpll1.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/mypll_altpll1.v" 29 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772067847 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067847 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "MYPLL_altpll1 MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated " "Elaborating entity \"MYPLL_altpll1\" for hierarchy \"MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated\"" {  } { { "altpll.tdf" "auto_generated" { Text "d:/intelfpga/18.1/quartus/libraries/megafunctions/altpll.tdf" 897 3 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772067847 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "MASTER_CTRL MASTER_CTRL:inst3 " "Elaborating entity \"MASTER_CTRL\" for hierarchy \"MASTER_CTRL:inst3\"" {  } { { "TOP.bdf" "inst3" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1856 1416 1656 1968 "inst3" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772067850 ""}
{ "Warning" "WVRFX_VERI_INCOMPLETE_SENSITIVITY_LIST" "DATA MASTER_CTRL.v(12) " "Verilog HDL Always Construct warning at MASTER_CTRL.v(12): variable \"DATA\" is read inside the Always Construct but isn't in the Always Construct's Event Control" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 12 0 0 } }  } 0 10235 "Verilog HDL Always Construct warning at %2!s!: variable \"%1!s!\" is read inside the Always Construct but isn't in the Always Construct's Event Control" 0 0 "Analysis & Synthesis" 0 -1 1752772067850 "|TOP|MASTER_CTRL:inst3"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "CTRL_DATA MASTER_CTRL.v(10) " "Verilog HDL Always Construct warning at MASTER_CTRL.v(10): inferring latch(es) for variable \"CTRL_DATA\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772067850 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[0\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[0\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067850 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[1\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[1\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067850 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[2\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[2\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067850 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[3\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[3\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067851 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[4\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[4\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067851 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[5\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[5\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067851 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[6\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[6\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067851 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[7\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[7\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067851 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[8\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[8\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067851 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[9\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[9\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067851 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[10\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[10\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067851 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[11\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[11\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067851 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[12\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[12\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067851 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[13\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[13\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067851 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[14\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[14\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067851 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[15\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[15\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067851 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "fmc_control fmc_control:inst " "Elaborating entity \"fmc_control\" for hierarchy \"fmc_control:inst\"" {  } { { "TOP.bdf" "inst" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1464 2264 2536 1864 "inst" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772067852 ""}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[0\] fmc_control.v(94) " "Inferred latch for \"addr\[0\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[1\] fmc_control.v(94) " "Inferred latch for \"addr\[1\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[2\] fmc_control.v(94) " "Inferred latch for \"addr\[2\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[3\] fmc_control.v(94) " "Inferred latch for \"addr\[3\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[4\] fmc_control.v(94) " "Inferred latch for \"addr\[4\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[5\] fmc_control.v(94) " "Inferred latch for \"addr\[5\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[6\] fmc_control.v(94) " "Inferred latch for \"addr\[6\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[7\] fmc_control.v(94) " "Inferred latch for \"addr\[7\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[8\] fmc_control.v(94) " "Inferred latch for \"addr\[8\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[9\] fmc_control.v(94) " "Inferred latch for \"addr\[9\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[10\] fmc_control.v(94) " "Inferred latch for \"addr\[10\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[11\] fmc_control.v(94) " "Inferred latch for \"addr\[11\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[12\] fmc_control.v(94) " "Inferred latch for \"addr\[12\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[13\] fmc_control.v(94) " "Inferred latch for \"addr\[13\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[14\] fmc_control.v(94) " "Inferred latch for \"addr\[14\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[15\] fmc_control.v(94) " "Inferred latch for \"addr\[15\]\" at fmc_control.v(94)" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 94 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067854 "|TOP|fmc_control:inst"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "AD_FREQ_MEASURE AD_FREQ_MEASURE:inst21 " "Elaborating entity \"AD_FREQ_MEASURE\" for hierarchy \"AD_FREQ_MEASURE:inst21\"" {  } { { "TOP.bdf" "inst21" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 288 1824 2192 496 "inst21" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772067855 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "gate_generator gate_generator:inst22 " "Elaborating entity \"gate_generator\" for hierarchy \"gate_generator:inst22\"" {  } { { "TOP.bdf" "inst22" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 208 1400 1544 320 "inst22" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772067856 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 28 gate_generator.v(33) " "Verilog HDL assignment warning at gate_generator.v(33): truncated value with size 32 to match size of target (28)" {  } { { "../src/gate_generator.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/gate_generator.v" 33 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752772067858 "|TOP|gate_generator:inst22"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "CNT32 CNT32:u_AD1_CNT32 " "Elaborating entity \"CNT32\" for hierarchy \"CNT32:u_AD1_CNT32\"" {  } { { "TOP.bdf" "u_AD1_CNT32" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 96 1664 1888 240 "u_AD1_CNT32" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772067859 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "test test:inst2 " "Elaborating entity \"test\" for hierarchy \"test:inst2\"" {  } { { "TOP.bdf" "inst2" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1360 2744 2960 1440 "inst2" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772067861 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "AD_DATA_DEAL AD_DATA_DEAL:u_AD_DATA_DEAL " "Elaborating entity \"AD_DATA_DEAL\" for hierarchy \"AD_DATA_DEAL:u_AD_DATA_DEAL\"" {  } { { "TOP.bdf" "u_AD_DATA_DEAL" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1896 3832 4184 2072 "u_AD_DATA_DEAL" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772067862 ""}
{ "Warning" "WVRFX_L2_VERI_INCOMPLETE_CASE_STATEMENT" "AD_DATA_DEAL.v(23) " "Verilog HDL Case Statement warning at AD_DATA_DEAL.v(23): incomplete case statement has no default case item" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 23 0 0 } }  } 0 10270 "Verilog HDL Case Statement warning at %1!s!: incomplete case statement has no default case item" 0 0 "Analysis & Synthesis" 0 -1 1752772067862 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "i AD_DATA_DEAL.v(22) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(22): inferring latch(es) for variable \"i\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "ad1_fifo_recv AD_DATA_DEAL.v(22) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(22): inferring latch(es) for variable \"ad1_fifo_recv\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_FIFO_DATA_OUT AD_DATA_DEAL.v(22) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(22): inferring latch(es) for variable \"AD1_FIFO_DATA_OUT\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_FLAG_SHOW AD_DATA_DEAL.v(22) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(22): inferring latch(es) for variable \"AD1_FLAG_SHOW\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "ad2_fifo_recv AD_DATA_DEAL.v(22) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(22): inferring latch(es) for variable \"ad2_fifo_recv\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_FIFO_DATA_OUT AD_DATA_DEAL.v(22) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(22): inferring latch(es) for variable \"AD2_FIFO_DATA_OUT\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_FLAG_SHOW AD_DATA_DEAL.v(22) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(22): inferring latch(es) for variable \"AD2_FLAG_SHOW\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[0\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[0\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[1\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[1\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[2\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[2\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[3\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[3\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[4\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[4\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[5\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[5\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[6\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[6\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[7\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[7\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[8\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[8\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[9\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[9\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[10\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[10\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[11\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[11\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[12\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[12\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[13\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[13\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[14\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[14\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[15\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[15\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[0\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[0\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[1\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[1\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[2\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[2\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[3\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[3\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[4\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[4\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[5\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[5\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[6\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[6\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[7\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[7\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[8\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[8\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[9\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[9\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[10\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[10\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[11\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[11\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[12\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[12\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[13\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[13\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[14\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[14\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[15\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[15\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[0\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[0\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[1\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[1\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[2\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[2\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[3\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[3\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[4\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[4\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[5\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[5\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[6\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[6\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[7\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[7\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[8\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[8\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[9\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[9\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[10\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[10\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[11\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[11\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[12\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[12\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067863 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[13\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[13\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[14\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[14\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[15\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[15\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[0\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[0\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[1\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[1\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[2\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[2\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[3\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[3\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[4\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[4\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[5\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[5\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[6\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[6\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[7\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[7\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[8\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[8\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[9\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[9\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[10\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[10\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[11\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[11\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[12\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[12\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[13\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[13\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[14\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[14\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[15\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[15\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772067864 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "TYFIFO TYFIFO:u_AD1_FIFO " "Elaborating entity \"TYFIFO\" for hierarchy \"TYFIFO:u_AD1_FIFO\"" {  } { { "TOP.bdf" "u_AD1_FIFO" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1560 3816 3992 1752 "u_AD1_FIFO" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772067868 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "dcfifo TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component " "Elaborating entity \"dcfifo\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\"" {  } { { "../ip/TYFIFO.v" "dcfifo_component" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/TYFIFO.v" 75 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068038 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component " "Elaborated megafunction instantiation \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\"" {  } { { "../ip/TYFIFO.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/TYFIFO.v" 75 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068039 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component " "Instantiated megafunction \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068039 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_numwords 1024 " "Parameter \"lpm_numwords\" = \"1024\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068039 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_showahead OFF " "Parameter \"lpm_showahead\" = \"OFF\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068039 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type dcfifo " "Parameter \"lpm_type\" = \"dcfifo\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068039 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_width 12 " "Parameter \"lpm_width\" = \"12\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068039 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_widthu 10 " "Parameter \"lpm_widthu\" = \"10\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068039 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "overflow_checking ON " "Parameter \"overflow_checking\" = \"ON\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068039 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "rdsync_delaypipe 4 " "Parameter \"rdsync_delaypipe\" = \"4\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068039 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "underflow_checking ON " "Parameter \"underflow_checking\" = \"ON\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068039 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "use_eab ON " "Parameter \"use_eab\" = \"ON\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068039 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "wrsync_delaypipe 4 " "Parameter \"wrsync_delaypipe\" = \"4\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068039 ""}  } { { "../ip/TYFIFO.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/TYFIFO.v" 75 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1752772068039 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/dcfifo_vve1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/dcfifo_vve1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 dcfifo_vve1 " "Found entity 1: dcfifo_vve1" {  } { { "db/dcfifo_vve1.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/dcfifo_vve1.tdf" 36 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772068065 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068065 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "dcfifo_vve1 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated " "Elaborating entity \"dcfifo_vve1\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\"" {  } { { "dcfifo.tdf" "auto_generated" { Text "d:/intelfpga/18.1/quartus/libraries/megafunctions/dcfifo.tdf" 190 3 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068065 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/a_graycounter_4p6.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/a_graycounter_4p6.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 a_graycounter_4p6 " "Found entity 1: a_graycounter_4p6" {  } { { "db/a_graycounter_4p6.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/a_graycounter_4p6.tdf" 24 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772068090 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068090 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "a_graycounter_4p6 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|a_graycounter_4p6:rdptr_g1p " "Elaborating entity \"a_graycounter_4p6\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|a_graycounter_4p6:rdptr_g1p\"" {  } { { "db/dcfifo_vve1.tdf" "rdptr_g1p" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/dcfifo_vve1.tdf" 47 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068091 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/a_graycounter_07c.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/a_graycounter_07c.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 a_graycounter_07c " "Found entity 1: a_graycounter_07c" {  } { { "db/a_graycounter_07c.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/a_graycounter_07c.tdf" 24 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772068113 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068113 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "a_graycounter_07c TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|a_graycounter_07c:wrptr_g1p " "Elaborating entity \"a_graycounter_07c\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|a_graycounter_07c:wrptr_g1p\"" {  } { { "db/dcfifo_vve1.tdf" "wrptr_g1p" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/dcfifo_vve1.tdf" 48 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068114 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_ce41.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_ce41.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_ce41 " "Found entity 1: altsyncram_ce41" {  } { { "db/altsyncram_ce41.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/altsyncram_ce41.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772068140 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068140 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_ce41 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram " "Elaborating entity \"altsyncram_ce41\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\"" {  } { { "db/dcfifo_vve1.tdf" "fifo_ram" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/dcfifo_vve1.tdf" 49 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068141 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/alt_synch_pipe_qal.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/alt_synch_pipe_qal.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 alt_synch_pipe_qal " "Found entity 1: alt_synch_pipe_qal" {  } { { "db/alt_synch_pipe_qal.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/alt_synch_pipe_qal.tdf" 26 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772068147 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068147 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "alt_synch_pipe_qal TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_qal:rs_dgwp " "Elaborating entity \"alt_synch_pipe_qal\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_qal:rs_dgwp\"" {  } { { "db/dcfifo_vve1.tdf" "rs_dgwp" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/dcfifo_vve1.tdf" 56 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068147 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/dffpipe_b09.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/dffpipe_b09.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 dffpipe_b09 " "Found entity 1: dffpipe_b09" {  } { { "db/dffpipe_b09.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/dffpipe_b09.tdf" 24 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772068153 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068153 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "dffpipe_b09 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_qal:rs_dgwp\|dffpipe_b09:dffpipe12 " "Elaborating entity \"dffpipe_b09\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_qal:rs_dgwp\|dffpipe_b09:dffpipe12\"" {  } { { "db/alt_synch_pipe_qal.tdf" "dffpipe12" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/alt_synch_pipe_qal.tdf" 33 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068153 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/alt_synch_pipe_ral.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/alt_synch_pipe_ral.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 alt_synch_pipe_ral " "Found entity 1: alt_synch_pipe_ral" {  } { { "db/alt_synch_pipe_ral.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/alt_synch_pipe_ral.tdf" 26 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772068158 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068158 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "alt_synch_pipe_ral TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_ral:ws_dgrp " "Elaborating entity \"alt_synch_pipe_ral\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_ral:ws_dgrp\"" {  } { { "db/dcfifo_vve1.tdf" "ws_dgrp" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/dcfifo_vve1.tdf" 57 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068159 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/dffpipe_c09.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/dffpipe_c09.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 dffpipe_c09 " "Found entity 1: dffpipe_c09" {  } { { "db/dffpipe_c09.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/dffpipe_c09.tdf" 24 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772068164 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068164 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "dffpipe_c09 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_ral:ws_dgrp\|dffpipe_c09:dffpipe15 " "Elaborating entity \"dffpipe_c09\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_ral:ws_dgrp\|dffpipe_c09:dffpipe15\"" {  } { { "db/alt_synch_pipe_ral.tdf" "dffpipe15" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/alt_synch_pipe_ral.tdf" 33 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068165 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/cmpr_o76.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/cmpr_o76.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 cmpr_o76 " "Found entity 1: cmpr_o76" {  } { { "db/cmpr_o76.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/cmpr_o76.tdf" 22 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772068190 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068190 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "cmpr_o76 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|cmpr_o76:rdempty_eq_comp " "Elaborating entity \"cmpr_o76\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|cmpr_o76:rdempty_eq_comp\"" {  } { { "db/dcfifo_vve1.tdf" "rdempty_eq_comp" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/dcfifo_vve1.tdf" 58 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068191 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "FREQ_DEV FREQ_DEV:u_AD1_DEV " "Elaborating entity \"FREQ_DEV\" for hierarchy \"FREQ_DEV:u_AD1_DEV\"" {  } { { "TOP.bdf" "u_AD1_DEV" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1328 4312 4544 1440 "u_AD1_DEV" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068195 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "AD_FREQ_WORD AD_FREQ_WORD:u_AD_FREQ_WORD " "Elaborating entity \"AD_FREQ_WORD\" for hierarchy \"AD_FREQ_WORD:u_AD_FREQ_WORD\"" {  } { { "TOP.bdf" "u_AD_FREQ_WORD" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1312 3696 3928 1488 "u_AD_FREQ_WORD" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068195 ""}
{ "Warning" "WVRFX_L2_VERI_INCOMPLETE_CASE_STATEMENT" "AD_FREQ_WORD.v(22) " "Verilog HDL Case Statement warning at AD_FREQ_WORD.v(22): incomplete case statement has no default case item" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 22 0 0 } }  } 0 10270 "Verilog HDL Case Statement warning at %1!s!: incomplete case statement has no default case item" 0 0 "Analysis & Synthesis" 0 -1 1752772068196 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_OUTH AD_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at AD_FREQ_WORD.v(21): inferring latch(es) for variable \"AD1_OUTH\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772068196 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_OUTL AD_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at AD_FREQ_WORD.v(21): inferring latch(es) for variable \"AD1_OUTL\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772068196 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_OUTH AD_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at AD_FREQ_WORD.v(21): inferring latch(es) for variable \"AD2_OUTH\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772068196 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_OUTL AD_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at AD_FREQ_WORD.v(21): inferring latch(es) for variable \"AD2_OUTL\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772068196 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[0\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[0\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068196 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[1\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[1\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068196 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[2\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[2\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[3\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[3\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[4\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[4\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[5\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[5\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[6\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[6\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[7\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[7\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[8\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[8\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[9\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[9\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[10\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[10\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[11\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[11\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[12\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[12\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[13\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[13\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[14\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[14\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[15\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[15\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[0\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[0\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[1\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[1\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[2\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[2\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[3\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[3\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[4\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[4\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[5\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[5\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[6\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[6\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[7\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[7\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[8\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[8\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[9\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[9\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[10\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[10\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[11\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[11\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[12\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[12\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[13\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[13\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[14\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[14\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[15\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[15\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[0\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[0\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[1\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[1\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[2\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[2\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[3\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[3\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[4\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[4\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[5\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[5\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[6\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[6\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[7\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[7\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[8\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[8\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[9\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[9\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[10\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[10\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[11\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[11\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[12\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[12\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[13\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[13\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[14\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[14\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[15\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[15\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[0\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[0\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[1\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[1\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[2\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[2\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[3\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[3\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[4\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[4\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068197 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[5\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[5\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068198 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[6\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[6\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068198 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[7\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[7\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068198 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[8\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[8\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068198 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[9\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[9\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068198 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[10\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[10\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068198 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[11\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[11\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068198 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[12\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[12\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068198 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[13\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[13\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068198 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[14\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[14\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068198 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[15\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[15\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068198 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "DA_FREQ_WORD DA_FREQ_WORD:u_DA_FREQ_WORD " "Elaborating entity \"DA_FREQ_WORD\" for hierarchy \"DA_FREQ_WORD:u_DA_FREQ_WORD\"" {  } { { "TOP.bdf" "u_DA_FREQ_WORD" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 520 3528 3760 696 "u_DA_FREQ_WORD" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068357 ""}
{ "Warning" "WVRFX_L2_VERI_INCOMPLETE_CASE_STATEMENT" "DA_FREQ_WORD.v(22) " "Verilog HDL Case Statement warning at DA_FREQ_WORD.v(22): incomplete case statement has no default case item" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 22 0 0 } }  } 0 10270 "Verilog HDL Case Statement warning at %1!s!: incomplete case statement has no default case item" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA1_OUTH DA_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at DA_FREQ_WORD.v(21): inferring latch(es) for variable \"DA1_OUTH\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA1_OUTL DA_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at DA_FREQ_WORD.v(21): inferring latch(es) for variable \"DA1_OUTL\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA2_OUTH DA_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at DA_FREQ_WORD.v(21): inferring latch(es) for variable \"DA2_OUTH\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA2_OUTL DA_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at DA_FREQ_WORD.v(21): inferring latch(es) for variable \"DA2_OUTL\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[0\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[0\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[1\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[1\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[2\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[2\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[3\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[3\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[4\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[4\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[5\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[5\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[6\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[6\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[7\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[7\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[8\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[8\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[9\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[9\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[10\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[10\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[11\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[11\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[12\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[12\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[13\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[13\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[14\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[14\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[15\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[15\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[0\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[0\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[1\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[1\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068358 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[2\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[2\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[3\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[3\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[4\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[4\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[5\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[5\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[6\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[6\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[7\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[7\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[8\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[8\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[9\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[9\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[10\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[10\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[11\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[11\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[12\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[12\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[13\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[13\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[14\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[14\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[15\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[15\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[0\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[0\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[1\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[1\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[2\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[2\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[3\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[3\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[4\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[4\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[5\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[5\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[6\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[6\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[7\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[7\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[8\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[8\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[9\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[9\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[10\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[10\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[11\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[11\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[12\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[12\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[13\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[13\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[14\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[14\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[15\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[15\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[0\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[0\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[1\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[1\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[2\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[2\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[3\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[3\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[4\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[4\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[5\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[5\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[6\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[6\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[7\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[7\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[8\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[8\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[9\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[9\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[10\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[10\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[11\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[11\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[12\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[12\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[13\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[13\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[14\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[14\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[15\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[15\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068359 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "voltage_scaler_clocked voltage_scaler_clocked:inst12 " "Elaborating entity \"voltage_scaler_clocked\" for hierarchy \"voltage_scaler_clocked:inst12\"" {  } { { "TOP.bdf" "inst12" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 256 4944 5184 368 "inst12" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068360 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 14 voltage_scaler_clocked.v(17) " "Verilog HDL assignment warning at voltage_scaler_clocked.v(17): truncated value with size 32 to match size of target (14)" {  } { { "../src/voltage_scaler_clocked.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 17 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752772068361 "|TOP|voltage_scaler_clocked:inst12"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 14 voltage_scaler_clocked.v(21) " "Verilog HDL assignment warning at voltage_scaler_clocked.v(21): truncated value with size 32 to match size of target (14)" {  } { { "../src/voltage_scaler_clocked.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 21 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752772068361 "|TOP|voltage_scaler_clocked:inst12"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "DA_WAVEFORM_A DA_WAVEFORM_A:inst15 " "Elaborating entity \"DA_WAVEFORM_A\" for hierarchy \"DA_WAVEFORM_A:inst15\"" {  } { { "TOP.bdf" "inst15" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 248 4480 4728 392 "inst15" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068362 ""}
{ "Warning" "WVRFX_L2_VERI_INCOMPLETE_CASE_STATEMENT" "DA_WAVEFORM_A.v(27) " "Verilog HDL Case Statement warning at DA_WAVEFORM_A.v(27): incomplete case statement has no default case item" {  } { { "../src/DA_WAVEFORM_A.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 27 0 0 } }  } 0 10270 "Verilog HDL Case Statement warning at %1!s!: incomplete case statement has no default case item" 0 0 "Analysis & Synthesis" 0 -1 1752772068362 "|TOP|DA_WAVEFORM_A:inst15"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "WAVEFORM_A DA_WAVEFORM_A.v(26) " "Verilog HDL Always Construct warning at DA_WAVEFORM_A.v(26): inferring latch(es) for variable \"WAVEFORM_A\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_WAVEFORM_A.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 26 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772068362 "|TOP|DA_WAVEFORM_A:inst15"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_A\[0\] DA_WAVEFORM_A.v(26) " "Inferred latch for \"WAVEFORM_A\[0\]\" at DA_WAVEFORM_A.v(26)" {  } { { "../src/DA_WAVEFORM_A.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 26 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068362 "|TOP|DA_WAVEFORM_A:inst15"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_A\[1\] DA_WAVEFORM_A.v(26) " "Inferred latch for \"WAVEFORM_A\[1\]\" at DA_WAVEFORM_A.v(26)" {  } { { "../src/DA_WAVEFORM_A.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 26 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068362 "|TOP|DA_WAVEFORM_A:inst15"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_A\[2\] DA_WAVEFORM_A.v(26) " "Inferred latch for \"WAVEFORM_A\[2\]\" at DA_WAVEFORM_A.v(26)" {  } { { "../src/DA_WAVEFORM_A.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 26 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068362 "|TOP|DA_WAVEFORM_A:inst15"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_A\[3\] DA_WAVEFORM_A.v(26) " "Inferred latch for \"WAVEFORM_A\[3\]\" at DA_WAVEFORM_A.v(26)" {  } { { "../src/DA_WAVEFORM_A.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 26 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068362 "|TOP|DA_WAVEFORM_A:inst15"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_A\[4\] DA_WAVEFORM_A.v(26) " "Inferred latch for \"WAVEFORM_A\[4\]\" at DA_WAVEFORM_A.v(26)" {  } { { "../src/DA_WAVEFORM_A.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 26 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068362 "|TOP|DA_WAVEFORM_A:inst15"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_A\[5\] DA_WAVEFORM_A.v(26) " "Inferred latch for \"WAVEFORM_A\[5\]\" at DA_WAVEFORM_A.v(26)" {  } { { "../src/DA_WAVEFORM_A.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 26 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068362 "|TOP|DA_WAVEFORM_A:inst15"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_A\[6\] DA_WAVEFORM_A.v(26) " "Inferred latch for \"WAVEFORM_A\[6\]\" at DA_WAVEFORM_A.v(26)" {  } { { "../src/DA_WAVEFORM_A.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 26 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068362 "|TOP|DA_WAVEFORM_A:inst15"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_A\[7\] DA_WAVEFORM_A.v(26) " "Inferred latch for \"WAVEFORM_A\[7\]\" at DA_WAVEFORM_A.v(26)" {  } { { "../src/DA_WAVEFORM_A.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 26 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068362 "|TOP|DA_WAVEFORM_A:inst15"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "sinromvpp DA_WAVEFORM_A:inst15\|sinromvpp:sinromvpp_inst " "Elaborating entity \"sinromvpp\" for hierarchy \"DA_WAVEFORM_A:inst15\|sinromvpp:sinromvpp_inst\"" {  } { { "../src/DA_WAVEFORM_A.v" "sinromvpp_inst" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 38 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068367 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram DA_WAVEFORM_A:inst15\|sinromvpp:sinromvpp_inst\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"DA_WAVEFORM_A:inst15\|sinromvpp:sinromvpp_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/sinromvpp/sinromvpp.v" "altsyncram_component" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sinromvpp/sinromvpp.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068388 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "DA_WAVEFORM_A:inst15\|sinromvpp:sinromvpp_inst\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"DA_WAVEFORM_A:inst15\|sinromvpp:sinromvpp_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/sinromvpp/sinromvpp.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sinromvpp/sinromvpp.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068389 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "DA_WAVEFORM_A:inst15\|sinromvpp:sinromvpp_inst\|altsyncram:altsyncram_component " "Instantiated megafunction \"DA_WAVEFORM_A:inst15\|sinromvpp:sinromvpp_inst\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068389 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068389 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068389 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file ../script/sin_1024x14.mif " "Parameter \"init_file\" = \"../script/sin_1024x14.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068389 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068389 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068389 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068389 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 1024 " "Parameter \"numwords_a\" = \"1024\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068389 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068389 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068389 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a UNREGISTERED " "Parameter \"outdata_reg_a\" = \"UNREGISTERED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068389 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 10 " "Parameter \"widthad_a\" = \"10\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068389 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068389 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068389 ""}  } { { "../ip/sinromvpp/sinromvpp.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sinromvpp/sinromvpp.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1752772068389 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_h3b1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_h3b1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_h3b1 " "Found entity 1: altsyncram_h3b1" {  } { { "db/altsyncram_h3b1.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/altsyncram_h3b1.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772068415 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068415 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_h3b1 DA_WAVEFORM_A:inst15\|sinromvpp:sinromvpp_inst\|altsyncram:altsyncram_component\|altsyncram_h3b1:auto_generated " "Elaborating entity \"altsyncram_h3b1\" for hierarchy \"DA_WAVEFORM_A:inst15\|sinromvpp:sinromvpp_inst\|altsyncram:altsyncram_component\|altsyncram_h3b1:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "d:/intelfpga/18.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068415 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "sqaure_rom DA_WAVEFORM_A:inst15\|sqaure_rom:sqaure_rom_inst " "Elaborating entity \"sqaure_rom\" for hierarchy \"DA_WAVEFORM_A:inst15\|sqaure_rom:sqaure_rom_inst\"" {  } { { "../src/DA_WAVEFORM_A.v" "sqaure_rom_inst" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 44 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068426 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram DA_WAVEFORM_A:inst15\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"DA_WAVEFORM_A:inst15\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/sqaure_rom/sqaure_rom.v" "altsyncram_component" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sqaure_rom/sqaure_rom.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068431 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "DA_WAVEFORM_A:inst15\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"DA_WAVEFORM_A:inst15\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/sqaure_rom/sqaure_rom.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sqaure_rom/sqaure_rom.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068432 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "DA_WAVEFORM_A:inst15\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component " "Instantiated megafunction \"DA_WAVEFORM_A:inst15\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068432 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068432 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068432 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file ../script/square_1024x14.mif " "Parameter \"init_file\" = \"../script/square_1024x14.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068432 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068432 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068432 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068432 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 1024 " "Parameter \"numwords_a\" = \"1024\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068432 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068432 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068432 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a UNREGISTERED " "Parameter \"outdata_reg_a\" = \"UNREGISTERED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068432 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 10 " "Parameter \"widthad_a\" = \"10\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068432 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068432 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068432 ""}  } { { "../ip/sqaure_rom/sqaure_rom.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sqaure_rom/sqaure_rom.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1752772068432 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_odb1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_odb1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_odb1 " "Found entity 1: altsyncram_odb1" {  } { { "db/altsyncram_odb1.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/altsyncram_odb1.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772068458 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068458 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_odb1 DA_WAVEFORM_A:inst15\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component\|altsyncram_odb1:auto_generated " "Elaborating entity \"altsyncram_odb1\" for hierarchy \"DA_WAVEFORM_A:inst15\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component\|altsyncram_odb1:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "d:/intelfpga/18.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068459 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "triangle_rom DA_WAVEFORM_A:inst15\|triangle_rom:triangle_rom_inst " "Elaborating entity \"triangle_rom\" for hierarchy \"DA_WAVEFORM_A:inst15\|triangle_rom:triangle_rom_inst\"" {  } { { "../src/DA_WAVEFORM_A.v" "triangle_rom_inst" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 50 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068468 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram DA_WAVEFORM_A:inst15\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"DA_WAVEFORM_A:inst15\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/triangle_rom/triangle_rom.v" "altsyncram_component" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/triangle_rom/triangle_rom.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068473 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "DA_WAVEFORM_A:inst15\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"DA_WAVEFORM_A:inst15\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/triangle_rom/triangle_rom.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/triangle_rom/triangle_rom.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068474 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "DA_WAVEFORM_A:inst15\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component " "Instantiated megafunction \"DA_WAVEFORM_A:inst15\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068474 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068474 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068474 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file ../script/triangle_1024x14.mif " "Parameter \"init_file\" = \"../script/triangle_1024x14.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068474 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068474 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068474 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068474 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 1024 " "Parameter \"numwords_a\" = \"1024\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068474 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068474 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068474 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a UNREGISTERED " "Parameter \"outdata_reg_a\" = \"UNREGISTERED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068474 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 10 " "Parameter \"widthad_a\" = \"10\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068474 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068474 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068474 ""}  } { { "../ip/triangle_rom/triangle_rom.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/triangle_rom/triangle_rom.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1752772068474 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_tjb1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_tjb1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_tjb1 " "Found entity 1: altsyncram_tjb1" {  } { { "db/altsyncram_tjb1.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/altsyncram_tjb1.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772068499 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068499 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_tjb1 DA_WAVEFORM_A:inst15\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component\|altsyncram_tjb1:auto_generated " "Elaborating entity \"altsyncram_tjb1\" for hierarchy \"DA_WAVEFORM_A:inst15\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component\|altsyncram_tjb1:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "d:/intelfpga/18.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068500 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "sawtooth_rom DA_WAVEFORM_A:inst15\|sawtooth_rom:sawtooth_rom_inst " "Elaborating entity \"sawtooth_rom\" for hierarchy \"DA_WAVEFORM_A:inst15\|sawtooth_rom:sawtooth_rom_inst\"" {  } { { "../src/DA_WAVEFORM_A.v" "sawtooth_rom_inst" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 57 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068511 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram DA_WAVEFORM_A:inst15\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"DA_WAVEFORM_A:inst15\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/sawtooth_rom/sawtooth_rom.v" "altsyncram_component" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sawtooth_rom/sawtooth_rom.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068515 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "DA_WAVEFORM_A:inst15\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"DA_WAVEFORM_A:inst15\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/sawtooth_rom/sawtooth_rom.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sawtooth_rom/sawtooth_rom.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068516 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "DA_WAVEFORM_A:inst15\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component " "Instantiated megafunction \"DA_WAVEFORM_A:inst15\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068517 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068517 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068517 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file ../script/sawtooth_1024x14.mif " "Parameter \"init_file\" = \"../script/sawtooth_1024x14.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068517 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068517 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068517 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068517 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 1024 " "Parameter \"numwords_a\" = \"1024\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068517 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068517 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068517 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a UNREGISTERED " "Parameter \"outdata_reg_a\" = \"UNREGISTERED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068517 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 10 " "Parameter \"widthad_a\" = \"10\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068517 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068517 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772068517 ""}  } { { "../ip/sawtooth_rom/sawtooth_rom.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sawtooth_rom/sawtooth_rom.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1752772068517 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_0lb1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_0lb1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_0lb1 " "Found entity 1: altsyncram_0lb1" {  } { { "db/altsyncram_0lb1.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/altsyncram_0lb1.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772068541 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068541 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_0lb1 DA_WAVEFORM_A:inst15\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component\|altsyncram_0lb1:auto_generated " "Elaborating entity \"altsyncram_0lb1\" for hierarchy \"DA_WAVEFORM_A:inst15\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component\|altsyncram_0lb1:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "d:/intelfpga/18.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068542 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "DA_APMPLITUDE DA_APMPLITUDE:inst10 " "Elaborating entity \"DA_APMPLITUDE\" for hierarchy \"DA_APMPLITUDE:inst10\"" {  } { { "TOP.bdf" "inst10" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 288 3864 4112 432 "inst10" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068548 ""}
{ "Warning" "WVRFX_L2_VERI_INCOMPLETE_CASE_STATEMENT" "DA_APMPLITUDE.v(21) " "Verilog HDL Case Statement warning at DA_APMPLITUDE.v(21): incomplete case statement has no default case item" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 21 0 0 } }  } 0 10270 "Verilog HDL Case Statement warning at %1!s!: incomplete case statement has no default case item" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA1_OUTA DA_APMPLITUDE.v(20) " "Verilog HDL Always Construct warning at DA_APMPLITUDE.v(20): inferring latch(es) for variable \"DA1_OUTA\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA2_OUTB DA_APMPLITUDE.v(20) " "Verilog HDL Always Construct warning at DA_APMPLITUDE.v(20): inferring latch(es) for variable \"DA2_OUTB\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTB\[0\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA2_OUTB\[0\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTB\[1\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA2_OUTB\[1\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTB\[2\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA2_OUTB\[2\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTB\[3\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA2_OUTB\[3\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTB\[4\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA2_OUTB\[4\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTB\[5\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA2_OUTB\[5\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTB\[6\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA2_OUTB\[6\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTB\[7\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA2_OUTB\[7\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTB\[8\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA2_OUTB\[8\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTB\[9\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA2_OUTB\[9\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTB\[10\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA2_OUTB\[10\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTB\[11\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA2_OUTB\[11\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTA\[0\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA1_OUTA\[0\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTA\[1\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA1_OUTA\[1\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTA\[2\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA1_OUTA\[2\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTA\[3\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA1_OUTA\[3\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTA\[4\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA1_OUTA\[4\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTA\[5\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA1_OUTA\[5\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTA\[6\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA1_OUTA\[6\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTA\[7\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA1_OUTA\[7\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTA\[8\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA1_OUTA\[8\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTA\[9\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA1_OUTA\[9\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTA\[10\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA1_OUTA\[10\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTA\[11\] DA_APMPLITUDE.v(20) " "Inferred latch for \"DA1_OUTA\[11\]\" at DA_APMPLITUDE.v(20)" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 20 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068549 "|TOP|DA_APMPLITUDE:inst10"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "DA_WAVEFORM_B DA_WAVEFORM_B:inst18 " "Elaborating entity \"DA_WAVEFORM_B\" for hierarchy \"DA_WAVEFORM_B:inst18\"" {  } { { "TOP.bdf" "inst18" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 520 4480 4728 664 "inst18" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772068551 ""}
{ "Warning" "WVRFX_L2_VERI_INCOMPLETE_CASE_STATEMENT" "DA_WAVEFORM_B.v(26) " "Verilog HDL Case Statement warning at DA_WAVEFORM_B.v(26): incomplete case statement has no default case item" {  } { { "../src/DA_WAVEFORM_B.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_B.v" 26 0 0 } }  } 0 10270 "Verilog HDL Case Statement warning at %1!s!: incomplete case statement has no default case item" 0 0 "Analysis & Synthesis" 0 -1 1752772068552 "|TOP|DA_WAVEFORM_B:inst18"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "WAVEFORM_B DA_WAVEFORM_B.v(25) " "Verilog HDL Always Construct warning at DA_WAVEFORM_B.v(25): inferring latch(es) for variable \"WAVEFORM_B\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_WAVEFORM_B.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_B.v" 25 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752772068552 "|TOP|DA_WAVEFORM_B:inst18"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_B\[0\] DA_WAVEFORM_B.v(25) " "Inferred latch for \"WAVEFORM_B\[0\]\" at DA_WAVEFORM_B.v(25)" {  } { { "../src/DA_WAVEFORM_B.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_B.v" 25 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068552 "|TOP|DA_WAVEFORM_B:inst18"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_B\[1\] DA_WAVEFORM_B.v(25) " "Inferred latch for \"WAVEFORM_B\[1\]\" at DA_WAVEFORM_B.v(25)" {  } { { "../src/DA_WAVEFORM_B.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_B.v" 25 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068552 "|TOP|DA_WAVEFORM_B:inst18"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_B\[2\] DA_WAVEFORM_B.v(25) " "Inferred latch for \"WAVEFORM_B\[2\]\" at DA_WAVEFORM_B.v(25)" {  } { { "../src/DA_WAVEFORM_B.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_B.v" 25 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068552 "|TOP|DA_WAVEFORM_B:inst18"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_B\[3\] DA_WAVEFORM_B.v(25) " "Inferred latch for \"WAVEFORM_B\[3\]\" at DA_WAVEFORM_B.v(25)" {  } { { "../src/DA_WAVEFORM_B.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_B.v" 25 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068552 "|TOP|DA_WAVEFORM_B:inst18"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_B\[4\] DA_WAVEFORM_B.v(25) " "Inferred latch for \"WAVEFORM_B\[4\]\" at DA_WAVEFORM_B.v(25)" {  } { { "../src/DA_WAVEFORM_B.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_B.v" 25 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068552 "|TOP|DA_WAVEFORM_B:inst18"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_B\[5\] DA_WAVEFORM_B.v(25) " "Inferred latch for \"WAVEFORM_B\[5\]\" at DA_WAVEFORM_B.v(25)" {  } { { "../src/DA_WAVEFORM_B.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_B.v" 25 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068552 "|TOP|DA_WAVEFORM_B:inst18"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_B\[6\] DA_WAVEFORM_B.v(25) " "Inferred latch for \"WAVEFORM_B\[6\]\" at DA_WAVEFORM_B.v(25)" {  } { { "../src/DA_WAVEFORM_B.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_B.v" 25 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068552 "|TOP|DA_WAVEFORM_B:inst18"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "WAVEFORM_B\[7\] DA_WAVEFORM_B.v(25) " "Inferred latch for \"WAVEFORM_B\[7\]\" at DA_WAVEFORM_B.v(25)" {  } { { "../src/DA_WAVEFORM_B.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_B.v" 25 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772068552 "|TOP|DA_WAVEFORM_B:inst18"}
{ "Warning" "WOPT_OPT_PROTECT_A_CLOCK_MUX" "" "Clock multiplexers are found and protected" { { "Warning" "WOPT_OPT_PROTECT_A_CLOCK_MUX_SUB" "DA_PARAMETER_CTRL:inst7\|FREQ_OUT_B_FINAL " "Found clock multiplexer DA_PARAMETER_CTRL:inst7\|FREQ_OUT_B_FINAL" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 25 -1 0 } }  } 0 19017 "Found clock multiplexer %1!s!" 0 0 "Design Software" 0 -1 1752772068780 "|TOP|DA_PARAMETER_CTRL:inst7|FREQ_OUT_B_FINAL"}  } {  } 0 19016 "Clock multiplexers are found and protected" 0 0 "Analysis & Synthesis" 0 -1 1752772068780 ""}
{ "Info" "ILPMS_INFERENCING_SUMMARY" "8 " "Inferred 8 megafunctions from design logic" { { "Info" "ILPMS_LPM_MULT_INFERRED" "voltage_scaler_clocked:inst12\|Mult0 lpm_mult " "Inferred multiplier megafunction (\"lpm_mult\") from the following logic: \"voltage_scaler_clocked:inst12\|Mult0\"" {  } { { "../src/voltage_scaler_clocked.v" "Mult0" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 17 -1 0 } }  } 0 278003 "Inferred multiplier megafunction (\"%2!s!\") from the following logic: \"%1!s!\"" 0 0 "Design Software" 0 -1 1752772069106 ""} { "Info" "ILPMS_LPM_DIVIDE_INFERRED" "voltage_scaler_clocked:inst12\|Div0 lpm_divide " "Inferred divider/modulo megafunction (\"lpm_divide\") from the following logic: \"voltage_scaler_clocked:inst12\|Div0\"" {  } { { "../src/voltage_scaler_clocked.v" "Div0" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 17 -1 0 } }  } 0 278004 "Inferred divider/modulo megafunction (\"%2!s!\") from the following logic: \"%1!s!\"" 0 0 "Design Software" 0 -1 1752772069106 ""} { "Info" "ILPMS_LPM_MULT_INFERRED" "voltage_scaler_clocked:inst12\|Mult1 lpm_mult " "Inferred multiplier megafunction (\"lpm_mult\") from the following logic: \"voltage_scaler_clocked:inst12\|Mult1\"" {  } { { "../src/voltage_scaler_clocked.v" "Mult1" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 21 -1 0 } }  } 0 278003 "Inferred multiplier megafunction (\"%2!s!\") from the following logic: \"%1!s!\"" 0 0 "Design Software" 0 -1 1752772069106 ""} { "Info" "ILPMS_LPM_DIVIDE_INFERRED" "voltage_scaler_clocked:inst12\|Div1 lpm_divide " "Inferred divider/modulo megafunction (\"lpm_divide\") from the following logic: \"voltage_scaler_clocked:inst12\|Div1\"" {  } { { "../src/voltage_scaler_clocked.v" "Div1" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 21 -1 0 } }  } 0 278004 "Inferred divider/modulo megafunction (\"%2!s!\") from the following logic: \"%1!s!\"" 0 0 "Design Software" 0 -1 1752772069106 ""} { "Info" "ILPMS_LPM_MULT_INFERRED" "voltage_scaler_clocked:inst11\|Mult0 lpm_mult " "Inferred multiplier megafunction (\"lpm_mult\") from the following logic: \"voltage_scaler_clocked:inst11\|Mult0\"" {  } { { "../src/voltage_scaler_clocked.v" "Mult0" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 17 -1 0 } }  } 0 278003 "Inferred multiplier megafunction (\"%2!s!\") from the following logic: \"%1!s!\"" 0 0 "Design Software" 0 -1 1752772069106 ""} { "Info" "ILPMS_LPM_DIVIDE_INFERRED" "voltage_scaler_clocked:inst11\|Div0 lpm_divide " "Inferred divider/modulo megafunction (\"lpm_divide\") from the following logic: \"voltage_scaler_clocked:inst11\|Div0\"" {  } { { "../src/voltage_scaler_clocked.v" "Div0" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 17 -1 0 } }  } 0 278004 "Inferred divider/modulo megafunction (\"%2!s!\") from the following logic: \"%1!s!\"" 0 0 "Design Software" 0 -1 1752772069106 ""} { "Info" "ILPMS_LPM_MULT_INFERRED" "voltage_scaler_clocked:inst11\|Mult1 lpm_mult " "Inferred multiplier megafunction (\"lpm_mult\") from the following logic: \"voltage_scaler_clocked:inst11\|Mult1\"" {  } { { "../src/voltage_scaler_clocked.v" "Mult1" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 21 -1 0 } }  } 0 278003 "Inferred multiplier megafunction (\"%2!s!\") from the following logic: \"%1!s!\"" 0 0 "Design Software" 0 -1 1752772069106 ""} { "Info" "ILPMS_LPM_DIVIDE_INFERRED" "voltage_scaler_clocked:inst11\|Div1 lpm_divide " "Inferred divider/modulo megafunction (\"lpm_divide\") from the following logic: \"voltage_scaler_clocked:inst11\|Div1\"" {  } { { "../src/voltage_scaler_clocked.v" "Div1" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 21 -1 0 } }  } 0 278004 "Inferred divider/modulo megafunction (\"%2!s!\") from the following logic: \"%1!s!\"" 0 0 "Design Software" 0 -1 1752772069106 ""}  } {  } 0 278001 "Inferred %1!llu! megafunctions from design logic" 0 0 "Analysis & Synthesis" 0 -1 1752772069106 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "voltage_scaler_clocked:inst12\|lpm_mult:Mult0 " "Elaborated megafunction instantiation \"voltage_scaler_clocked:inst12\|lpm_mult:Mult0\"" {  } { { "../src/voltage_scaler_clocked.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 17 -1 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772069130 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "voltage_scaler_clocked:inst12\|lpm_mult:Mult0 " "Instantiated megafunction \"voltage_scaler_clocked:inst12\|lpm_mult:Mult0\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHA 32 " "Parameter \"LPM_WIDTHA\" = \"32\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772069130 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHB 12 " "Parameter \"LPM_WIDTHB\" = \"12\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772069130 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHP 44 " "Parameter \"LPM_WIDTHP\" = \"44\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772069130 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHR 44 " "Parameter \"LPM_WIDTHR\" = \"44\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772069130 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHS 1 " "Parameter \"LPM_WIDTHS\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772069130 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_REPRESENTATION UNSIGNED " "Parameter \"LPM_REPRESENTATION\" = \"UNSIGNED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772069130 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "INPUT_A_IS_CONSTANT NO " "Parameter \"INPUT_A_IS_CONSTANT\" = \"NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772069130 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "INPUT_B_IS_CONSTANT NO " "Parameter \"INPUT_B_IS_CONSTANT\" = \"NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772069130 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "MAXIMIZE_SPEED 5 " "Parameter \"MAXIMIZE_SPEED\" = \"5\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772069130 ""}  } { { "../src/voltage_scaler_clocked.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 17 -1 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1752772069130 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/mult_3dt.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/mult_3dt.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 mult_3dt " "Found entity 1: mult_3dt" {  } { { "db/mult_3dt.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/mult_3dt.tdf" 30 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772069154 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772069154 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "voltage_scaler_clocked:inst12\|lpm_divide:Div0 " "Elaborated megafunction instantiation \"voltage_scaler_clocked:inst12\|lpm_divide:Div0\"" {  } { { "../src/voltage_scaler_clocked.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 17 -1 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772069169 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "voltage_scaler_clocked:inst12\|lpm_divide:Div0 " "Instantiated megafunction \"voltage_scaler_clocked:inst12\|lpm_divide:Div0\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHN 32 " "Parameter \"LPM_WIDTHN\" = \"32\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772069169 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHD 12 " "Parameter \"LPM_WIDTHD\" = \"12\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772069169 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_NREPRESENTATION UNSIGNED " "Parameter \"LPM_NREPRESENTATION\" = \"UNSIGNED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772069169 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_DREPRESENTATION UNSIGNED " "Parameter \"LPM_DREPRESENTATION\" = \"UNSIGNED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1752772069169 ""}  } { { "../src/voltage_scaler_clocked.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 17 -1 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1752772069169 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/lpm_divide_fkm.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/lpm_divide_fkm.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 lpm_divide_fkm " "Found entity 1: lpm_divide_fkm" {  } { { "db/lpm_divide_fkm.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/lpm_divide_fkm.tdf" 24 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772069191 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772069191 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/sign_div_unsign_7nh.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/sign_div_unsign_7nh.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 sign_div_unsign_7nh " "Found entity 1: sign_div_unsign_7nh" {  } { { "db/sign_div_unsign_7nh.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/sign_div_unsign_7nh.tdf" 24 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772069199 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772069199 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/alt_u_div_2af.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/alt_u_div_2af.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 alt_u_div_2af " "Found entity 1: alt_u_div_2af" {  } { { "db/alt_u_div_2af.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/alt_u_div_2af.tdf" 26 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772069223 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772069223 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/add_sub_7pc.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/add_sub_7pc.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 add_sub_7pc " "Found entity 1: add_sub_7pc" {  } { { "db/add_sub_7pc.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/add_sub_7pc.tdf" 22 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772069255 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772069255 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/add_sub_8pc.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/add_sub_8pc.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 add_sub_8pc " "Found entity 1: add_sub_8pc" {  } { { "db/add_sub_8pc.tdf" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/add_sub_8pc.tdf" 22 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752772069280 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772069280 ""}
{ "Info" "IMLS_MLS_IGNORED_SUMMARY" "160 " "Ignored 160 buffer(s)" { { "Info" "IMLS_MLS_IGNORED_SOFT" "160 " "Ignored 160 SOFT buffer(s)" {  } {  } 0 13019 "Ignored %1!d! SOFT buffer(s)" 0 0 "Design Software" 0 -1 1752772069628 ""}  } {  } 0 13014 "Ignored %1!d! buffer(s)" 0 0 "Analysis & Synthesis" 0 -1 1752772069628 ""}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING" "" "Timing-Driven Synthesis is running" {  } {  } 0 286030 "Timing-Driven Synthesis is running" 0 0 "Analysis & Synthesis" 0 -1 1752772070517 ""}
{ "Info" "ISCL_SCL_LOST_FANOUT_MSG_HDR" "1 " "1 registers lost all their fanouts during netlist optimizations." {  } {  } 0 17049 "%1!d! registers lost all their fanouts during netlist optimizations." 0 0 "Analysis & Synthesis" 0 -1 1752772071279 ""}
{ "Info" "IRDB_WROTE_SUPPRESSED_MSGS" "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/output_files/JYX_FPGA_OBJECT.map.smsg " "Generated suppressed messages file E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/output_files/JYX_FPGA_OBJECT.map.smsg" {  } {  } 0 144001 "Generated suppressed messages file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772071408 ""}
{ "Info" "IBPM_HARD_BLOCK_PARTITION_CREATED" "hard_block:auto_generated_inst " "Generating hard_block partition \"hard_block:auto_generated_inst\"" { { "Info" "IBPM_HARD_BLOCK_PARTITION_NODE" "1 0 1 0 0 " "Adding 1 node(s), including 0 DDIO, 1 PLL, 0 transceiver and 0 LCELL" {  } {  } 0 16011 "Adding %1!d! node(s), including %2!d! DDIO, %3!d! PLL, %4!d! transceiver and %5!d! LCELL" 0 0 "Design Software" 0 -1 1752772071586 ""}  } {  } 0 16010 "Generating hard_block partition \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752772071586 ""}
{ "Warning" "WCUT_CUT_UNNECESSARY_INPUT_PIN_HDR" "1 " "Design contains 1 input pin(s) that do not drive logic" { { "Warning" "WCUT_CUT_UNNECESSARY_INPUT_PIN" "AD2_INPUT_CLK " "No output dependent on input pin \"AD2_INPUT_CLK\"" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 8 2048 2248 24 "AD2_INPUT_CLK" "" } } } }  } 0 15610 "No output dependent on input pin \"%1!s!\"" 0 0 "Design Software" 0 -1 1752772071732 "|TOP|AD2_INPUT_CLK"}  } {  } 0 21074 "Design contains %1!d! input pin(s) that do not drive logic" 0 0 "Analysis & Synthesis" 0 -1 1752772071732 ""}
{ "Info" "ICUT_CUT_TM_SUMMARY" "4792 " "Implemented 4792 device resources after synthesis - the final resource count might be different" { { "Info" "ICUT_CUT_TM_IPINS" "32 " "Implemented 32 input pins" {  } {  } 0 21058 "Implemented %1!d! input pins" 0 0 "Design Software" 0 -1 1752772071732 ""} { "Info" "ICUT_CUT_TM_OPINS" "32 " "Implemented 32 output pins" {  } {  } 0 21059 "Implemented %1!d! output pins" 0 0 "Design Software" 0 -1 1752772071732 ""} { "Info" "ICUT_CUT_TM_BIDIRS" "16 " "Implemented 16 bidirectional pins" {  } {  } 0 21060 "Implemented %1!d! bidirectional pins" 0 0 "Design Software" 0 -1 1752772071732 ""} { "Info" "ICUT_CUT_TM_LCELLS" "4559 " "Implemented 4559 logic cells" {  } {  } 0 21061 "Implemented %1!d! logic cells" 0 0 "Design Software" 0 -1 1752772071732 ""} { "Info" "ICUT_CUT_TM_RAMS" "136 " "Implemented 136 RAM segments" {  } {  } 0 21064 "Implemented %1!d! RAM segments" 0 0 "Design Software" 0 -1 1752772071732 ""} { "Info" "ICUT_CUT_TM_PLLS" "1 " "Implemented 1 PLLs" {  } {  } 0 21065 "Implemented %1!d! PLLs" 0 0 "Design Software" 0 -1 1752772071732 ""} { "Info" "ICUT_CUT_TM_DSP_ELEM" "16 " "Implemented 16 DSP elements" {  } {  } 0 21062 "Implemented %1!d! DSP elements" 0 0 "Design Software" 0 -1 1752772071732 ""}  } {  } 0 21057 "Implemented %1!d! device resources after synthesis - the final resource count might be different" 0 0 "Analysis & Synthesis" 0 -1 1752772071732 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Analysis & Synthesis 0 s 38 s Quartus Prime " "Quartus Prime Analysis & Synthesis was successful. 0 errors, 38 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4935 " "Peak virtual memory: 4935 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752772071756 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Jul 18 01:07:51 2025 " "Processing ended: Fri Jul 18 01:07:51 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752772071756 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:11 " "Elapsed time: 00:00:11" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752772071756 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:17 " "Total CPU time (on all processors): 00:00:17" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752772071756 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Analysis & Synthesis" 0 -1 1752772071756 ""}
