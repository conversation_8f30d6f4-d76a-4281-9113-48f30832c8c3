-- MIF file generated by Python script
-- Waveform: square
--
WIDTH = 14;
DEPTH = 64;
ADDRESS_RADIX = DEC;
DATA_RADIX = DEC;

CONTENT BEGIN
	0 : 16383;
	1 : 16383;
	2 : 16383;
	3 : 16383;
	4 : 16383;
	5 : 16383;
	6 : 16383;
	7 : 16383;
	8 : 16383;
	9 : 16383;
	10 : 16383;
	11 : 16383;
	12 : 16383;
	13 : 16383;
	14 : 16383;
	15 : 16383;
	16 : 16383;
	17 : 16383;
	18 : 16383;
	19 : 16383;
	20 : 16383;
	21 : 16383;
	22 : 16383;
	23 : 16383;
	24 : 16383;
	25 : 16383;
	26 : 16383;
	27 : 16383;
	28 : 16383;
	29 : 16383;
	30 : 16383;
	31 : 16383;
	32 : 0;
	33 : 0;
	34 : 0;
	35 : 0;
	36 : 0;
	37 : 0;
	38 : 0;
	39 : 0;
	40 : 0;
	41 : 0;
	42 : 0;
	43 : 0;
	44 : 0;
	45 : 0;
	46 : 0;
	47 : 0;
	48 : 0;
	49 : 0;
	50 : 0;
	51 : 0;
	52 : 0;
	53 : 0;
	54 : 0;
	55 : 0;
	56 : 0;
	57 : 0;
	58 : 0;
	59 : 0;
	60 : 0;
	61 : 0;
	62 : 0;
	63 : 0;
END;
