/*
WARNING: Do NOT edit the input and output ports in this file in a text
editor if you plan to continue editing the block that represents it in
the Block Editor! File corruption is VERY likely to occur.
*/
/*
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.
*/
(header "symbol" (version "1.1"))
(symbol
	(rect 16 16 256 128)
	(text "voltage_scaler_clocked_fast" (rect 5 0 119 12)(font "Arial" ))
	(text "inst" (rect 8 96 20 108)(font "Arial" ))
	(port
		(pt 0 32)
		(input)
		(text "clk" (rect 0 0 10 12)(font "Arial" ))
		(text "clk" (rect 21 27 31 39)(font "Arial" ))
		(line (pt 0 32)(pt 16 32)(line_width 1))
	)
	(port
		(pt 0 48)
		(input)
		(text "rom_data[13..0]" (rect 0 0 62 12)(font "Arial" ))
		(text "rom_data[13..0]" (rect 21 43 83 55)(font "Arial" ))
		(line (pt 0 48)(pt 16 48)(line_width 3))
	)
	(port
		(pt 0 64)
		(input)
		(text "voltage_mv[11..0]" (rect 0 0 70 12)(font "Arial" ))
		(text "voltage_mv[11..0]" (rect 21 59 91 71)(font "Arial" ))
		(line (pt 0 64)(pt 16 64)(line_width 3))
	)
	(port
		(pt 240 32)
		(output)
		(text "scaled_data[13..0]" (rect 0 0 70 12)(font "Arial" ))
		(text "scaled_data[13..0]" (rect 149 27 219 39)(font "Arial" ))
		(line (pt 240 32)(pt 224 32)(line_width 3))
	)
	(parameter
		"ROM_MAX"
		"16383"
		""
		(type "PARAMETER_SIGNED_DEC")	)
	(parameter
		"DEFAULT_PEAK_MV"
		"3080"
		""
		(type "PARAMETER_SIGNED_DEC")	)
	(parameter
		"HALF_ROM_MAX"
		""
		""
		(type "PARAMETER_SIGNED_DEC")	)
	(parameter
		"SCALE_SHIFT"
		"12"
		""
		(type "PARAMETER_SIGNED_DEC")	)
	(parameter
		"CORRECTION_NUM"
		"3080"
		""
		(type "PARAMETER_SIGNED_DEC")	)
	(parameter
		"CORRECTION_DEN"
		"4096"
		""
		(type "PARAMETER_SIGNED_DEC")	)
	(drawing
		(rectangle (rect 16 16 224 96)(line_width 1))
	)
	(annotation_block (parameter)(rect 256 -64 356 16))
)
