|TOP
DA1_OUTCLK <= DA1CLK.DB_MAX_OUTPUT_PORT_TYPE
CLK => MYPLL:inst6.inclk0
FPGA_CS_NEL => MASTER_CTRL:inst3.CS
FPGA_CS_NEL => fmc_control:inst.fpga_cs_ne1
FPGA_CS_NEL => AD_FREQ_MEASURE:inst21.CS
FPGA_CS_NEL => AD_DATA_DEAL:u_AD_DATA_DEAL.CS
FPGA_CS_NEL => AD_FREQ_WORD:u_AD_FREQ_WORD.CS
FPGA_CS_NEL => DA_PARAMETER_CTRL:inst7.CS
FPGA_CS_NEL => DA_FREQ_WORD:u_DA_FREQ_WORD.CS
FPGA_CS_NEL => DA_WAVEFORM_A:inst15.CS
FPGA_CS_NEL => DA_APMPLITUDE:inst10.CS
FPGA_CS_NEL => DA_WAVEFORM_B:inst18.CS
RST => fmc_control:inst.rst
FPGA_NL_NADV => fmc_control:inst.fpga_nl_nadv
FPGA_WR_NWE => fmc_control:inst.fpga_wr_nwe
FPGA_RD_NOE => fmc_control:inst.fpga_rd_noe
FPGA_DB[0] <> fmc_control:inst.fpga_db[0]
FPGA_DB[1] <> fmc_control:inst.fpga_db[1]
FPGA_DB[2] <> fmc_control:inst.fpga_db[2]
FPGA_DB[3] <> fmc_control:inst.fpga_db[3]
FPGA_DB[4] <> fmc_control:inst.fpga_db[4]
FPGA_DB[5] <> fmc_control:inst.fpga_db[5]
FPGA_DB[6] <> fmc_control:inst.fpga_db[6]
FPGA_DB[7] <> fmc_control:inst.fpga_db[7]
FPGA_DB[8] <> fmc_control:inst.fpga_db[8]
FPGA_DB[9] <> fmc_control:inst.fpga_db[9]
FPGA_DB[10] <> fmc_control:inst.fpga_db[10]
FPGA_DB[11] <> fmc_control:inst.fpga_db[11]
FPGA_DB[12] <> fmc_control:inst.fpga_db[12]
FPGA_DB[13] <> fmc_control:inst.fpga_db[13]
FPGA_DB[14] <> fmc_control:inst.fpga_db[14]
FPGA_DB[15] <> fmc_control:inst.fpga_db[15]
AD1_INPUT_CLK => gate_generator:inst22.da_clk
AD1_INPUT_CLK => CNT32:u_AD1_CNT32.CLK
AD2_INPUT_CLK => CNT32:u_AD2_CNT32.CLK
AD1_INPUT[0] => TYFIFO:u_AD1_FIFO.data[0]
AD1_INPUT[1] => TYFIFO:u_AD1_FIFO.data[1]
AD1_INPUT[2] => TYFIFO:u_AD1_FIFO.data[2]
AD1_INPUT[3] => TYFIFO:u_AD1_FIFO.data[3]
AD1_INPUT[4] => TYFIFO:u_AD1_FIFO.data[4]
AD1_INPUT[5] => TYFIFO:u_AD1_FIFO.data[5]
AD1_INPUT[6] => TYFIFO:u_AD1_FIFO.data[6]
AD1_INPUT[7] => TYFIFO:u_AD1_FIFO.data[7]
AD1_INPUT[8] => TYFIFO:u_AD1_FIFO.data[8]
AD1_INPUT[9] => TYFIFO:u_AD1_FIFO.data[9]
AD1_INPUT[10] => TYFIFO:u_AD1_FIFO.data[10]
AD1_INPUT[11] => TYFIFO:u_AD1_FIFO.data[11]
AD2_INPUT[0] => TYFIFO:u_AD2_FIFO.data[0]
AD2_INPUT[1] => TYFIFO:u_AD2_FIFO.data[1]
AD2_INPUT[2] => TYFIFO:u_AD2_FIFO.data[2]
AD2_INPUT[3] => TYFIFO:u_AD2_FIFO.data[3]
AD2_INPUT[4] => TYFIFO:u_AD2_FIFO.data[4]
AD2_INPUT[5] => TYFIFO:u_AD2_FIFO.data[5]
AD2_INPUT[6] => TYFIFO:u_AD2_FIFO.data[6]
AD2_INPUT[7] => TYFIFO:u_AD2_FIFO.data[7]
AD2_INPUT[8] => TYFIFO:u_AD2_FIFO.data[8]
AD2_INPUT[9] => TYFIFO:u_AD2_FIFO.data[9]
AD2_INPUT[10] => TYFIFO:u_AD2_FIFO.data[10]
AD2_INPUT[11] => TYFIFO:u_AD2_FIFO.data[11]
DA2_OUTCLK <= DA2CLK.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTCLK <= AD1_FS.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTCLK <= AD2_FS.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUT[0] <= voltage_scaler_clocked:inst12.scaled_data[0]
DA1_OUT[1] <= voltage_scaler_clocked:inst12.scaled_data[1]
DA1_OUT[2] <= voltage_scaler_clocked:inst12.scaled_data[2]
DA1_OUT[3] <= voltage_scaler_clocked:inst12.scaled_data[3]
DA1_OUT[4] <= voltage_scaler_clocked:inst12.scaled_data[4]
DA1_OUT[5] <= voltage_scaler_clocked:inst12.scaled_data[5]
DA1_OUT[6] <= voltage_scaler_clocked:inst12.scaled_data[6]
DA1_OUT[7] <= voltage_scaler_clocked:inst12.scaled_data[7]
DA1_OUT[8] <= voltage_scaler_clocked:inst12.scaled_data[8]
DA1_OUT[9] <= voltage_scaler_clocked:inst12.scaled_data[9]
DA1_OUT[10] <= voltage_scaler_clocked:inst12.scaled_data[10]
DA1_OUT[11] <= voltage_scaler_clocked:inst12.scaled_data[11]
DA1_OUT[12] <= voltage_scaler_clocked:inst12.scaled_data[12]
DA1_OUT[13] <= voltage_scaler_clocked:inst12.scaled_data[13]
DA2_OUT[0] <= voltage_scaler_clocked:inst11.scaled_data[0]
DA2_OUT[1] <= voltage_scaler_clocked:inst11.scaled_data[1]
DA2_OUT[2] <= voltage_scaler_clocked:inst11.scaled_data[2]
DA2_OUT[3] <= voltage_scaler_clocked:inst11.scaled_data[3]
DA2_OUT[4] <= voltage_scaler_clocked:inst11.scaled_data[4]
DA2_OUT[5] <= voltage_scaler_clocked:inst11.scaled_data[5]
DA2_OUT[6] <= voltage_scaler_clocked:inst11.scaled_data[6]
DA2_OUT[7] <= voltage_scaler_clocked:inst11.scaled_data[7]
DA2_OUT[8] <= voltage_scaler_clocked:inst11.scaled_data[8]
DA2_OUT[9] <= voltage_scaler_clocked:inst11.scaled_data[9]
DA2_OUT[10] <= voltage_scaler_clocked:inst11.scaled_data[10]
DA2_OUT[11] <= voltage_scaler_clocked:inst11.scaled_data[11]
DA2_OUT[12] <= voltage_scaler_clocked:inst11.scaled_data[12]
DA2_OUT[13] <= voltage_scaler_clocked:inst11.scaled_data[13]


|TOP|DA_PARAMETER_CTRL:inst7
CLK_BASE => FREQ_WORD_B[0].CLK
CLK_BASE => FREQ_WORD_B[1].CLK
CLK_BASE => FREQ_WORD_B[2].CLK
CLK_BASE => FREQ_WORD_B[3].CLK
CLK_BASE => FREQ_WORD_B[4].CLK
CLK_BASE => FREQ_WORD_B[5].CLK
CLK_BASE => FREQ_WORD_B[6].CLK
CLK_BASE => FREQ_WORD_B[7].CLK
CLK_BASE => FREQ_WORD_B[8].CLK
CLK_BASE => FREQ_WORD_B[9].CLK
CLK_BASE => FREQ_WORD_B[10].CLK
CLK_BASE => FREQ_WORD_B[11].CLK
CLK_BASE => FREQ_WORD_B[12].CLK
CLK_BASE => FREQ_WORD_B[13].CLK
CLK_BASE => FREQ_WORD_B[14].CLK
CLK_BASE => FREQ_WORD_B[15].CLK
CLK_BASE => FREQ_WORD_B[16].CLK
CLK_BASE => FREQ_WORD_B[17].CLK
CLK_BASE => FREQ_WORD_B[18].CLK
CLK_BASE => FREQ_WORD_B[19].CLK
CLK_BASE => FREQ_WORD_B[20].CLK
CLK_BASE => FREQ_WORD_B[21].CLK
CLK_BASE => FREQ_WORD_B[22].CLK
CLK_BASE => FREQ_WORD_B[23].CLK
CLK_BASE => FREQ_WORD_B[24].CLK
CLK_BASE => FREQ_WORD_B[25].CLK
CLK_BASE => FREQ_WORD_B[26].CLK
CLK_BASE => FREQ_WORD_B[27].CLK
CLK_BASE => FREQ_WORD_B[28].CLK
CLK_BASE => FREQ_WORD_B[29].CLK
CLK_BASE => FREQ_WORD_B[30].CLK
CLK_BASE => FREQ_WORD_B[31].CLK
CLK_BASE => FREQ_OUT_B.CLK
CLK_BASE => flag_reg.CLK
CLK_BASE => flag.CLK
CLK_BASE => ACC_B[0].CLK
CLK_BASE => ACC_B[1].CLK
CLK_BASE => ACC_B[2].CLK
CLK_BASE => ACC_B[3].CLK
CLK_BASE => ACC_B[4].CLK
CLK_BASE => ACC_B[5].CLK
CLK_BASE => ACC_B[6].CLK
CLK_BASE => ACC_B[7].CLK
CLK_BASE => ACC_B[8].CLK
CLK_BASE => ACC_B[9].CLK
CLK_BASE => ACC_B[10].CLK
CLK_BASE => ACC_B[11].CLK
CLK_BASE => ACC_B[12].CLK
CLK_BASE => ACC_B[13].CLK
CLK_BASE => ACC_B[14].CLK
CLK_BASE => ACC_B[15].CLK
CLK_BASE => ACC_B[16].CLK
CLK_BASE => ACC_B[17].CLK
CLK_BASE => ACC_B[18].CLK
CLK_BASE => ACC_B[19].CLK
CLK_BASE => ACC_B[20].CLK
CLK_BASE => ACC_B[21].CLK
CLK_BASE => ACC_B[22].CLK
CLK_BASE => ACC_B[23].CLK
CLK_BASE => ACC_B[24].CLK
CLK_BASE => ACC_B[25].CLK
CLK_BASE => ACC_B[26].CLK
CLK_BASE => ACC_B[27].CLK
CLK_BASE => ACC_B[28].CLK
CLK_BASE => ACC_B[29].CLK
CLK_BASE => ACC_B[30].CLK
CLK_BASE => ACC_B[31].CLK
CLK_BASE => FREQ_WORD_A[0].CLK
CLK_BASE => FREQ_WORD_A[1].CLK
CLK_BASE => FREQ_WORD_A[2].CLK
CLK_BASE => FREQ_WORD_A[3].CLK
CLK_BASE => FREQ_WORD_A[4].CLK
CLK_BASE => FREQ_WORD_A[5].CLK
CLK_BASE => FREQ_WORD_A[6].CLK
CLK_BASE => FREQ_WORD_A[7].CLK
CLK_BASE => FREQ_WORD_A[8].CLK
CLK_BASE => FREQ_WORD_A[9].CLK
CLK_BASE => FREQ_WORD_A[10].CLK
CLK_BASE => FREQ_WORD_A[11].CLK
CLK_BASE => FREQ_WORD_A[12].CLK
CLK_BASE => FREQ_WORD_A[13].CLK
CLK_BASE => FREQ_WORD_A[14].CLK
CLK_BASE => FREQ_WORD_A[15].CLK
CLK_BASE => FREQ_WORD_A[16].CLK
CLK_BASE => FREQ_WORD_A[17].CLK
CLK_BASE => FREQ_WORD_A[18].CLK
CLK_BASE => FREQ_WORD_A[19].CLK
CLK_BASE => FREQ_WORD_A[20].CLK
CLK_BASE => FREQ_WORD_A[21].CLK
CLK_BASE => FREQ_WORD_A[22].CLK
CLK_BASE => FREQ_WORD_A[23].CLK
CLK_BASE => FREQ_WORD_A[24].CLK
CLK_BASE => FREQ_WORD_A[25].CLK
CLK_BASE => FREQ_WORD_A[26].CLK
CLK_BASE => FREQ_WORD_A[27].CLK
CLK_BASE => FREQ_WORD_A[28].CLK
CLK_BASE => FREQ_WORD_A[29].CLK
CLK_BASE => FREQ_WORD_A[30].CLK
CLK_BASE => FREQ_WORD_A[31].CLK
CLK_BASE => FREQ_OUT_A.CLK
CLK_BASE => ACC_A[0].CLK
CLK_BASE => ACC_A[1].CLK
CLK_BASE => ACC_A[2].CLK
CLK_BASE => ACC_A[3].CLK
CLK_BASE => ACC_A[4].CLK
CLK_BASE => ACC_A[5].CLK
CLK_BASE => ACC_A[6].CLK
CLK_BASE => ACC_A[7].CLK
CLK_BASE => ACC_A[8].CLK
CLK_BASE => ACC_A[9].CLK
CLK_BASE => ACC_A[10].CLK
CLK_BASE => ACC_A[11].CLK
CLK_BASE => ACC_A[12].CLK
CLK_BASE => ACC_A[13].CLK
CLK_BASE => ACC_A[14].CLK
CLK_BASE => ACC_A[15].CLK
CLK_BASE => ACC_A[16].CLK
CLK_BASE => ACC_A[17].CLK
CLK_BASE => ACC_A[18].CLK
CLK_BASE => ACC_A[19].CLK
CLK_BASE => ACC_A[20].CLK
CLK_BASE => ACC_A[21].CLK
CLK_BASE => ACC_A[22].CLK
CLK_BASE => ACC_A[23].CLK
CLK_BASE => ACC_A[24].CLK
CLK_BASE => ACC_A[25].CLK
CLK_BASE => ACC_A[26].CLK
CLK_BASE => ACC_A[27].CLK
CLK_BASE => ACC_A[28].CLK
CLK_BASE => ACC_A[29].CLK
CLK_BASE => ACC_A[30].CLK
CLK_BASE => ACC_A[31].CLK
EN => flag_reg.ENA
EN => flag.ENA
EN => ACC_B[0].ENA
EN => ACC_B[1].ENA
EN => ACC_B[2].ENA
EN => ACC_B[3].ENA
EN => ACC_B[4].ENA
EN => ACC_B[5].ENA
EN => ACC_B[6].ENA
EN => ACC_B[7].ENA
EN => ACC_B[8].ENA
EN => ACC_B[9].ENA
EN => ACC_B[10].ENA
EN => ACC_B[11].ENA
EN => ACC_B[12].ENA
EN => ACC_B[13].ENA
EN => ACC_B[14].ENA
EN => ACC_B[15].ENA
EN => ACC_B[16].ENA
EN => ACC_B[17].ENA
EN => ACC_B[18].ENA
EN => ACC_B[19].ENA
EN => ACC_B[20].ENA
EN => ACC_B[21].ENA
EN => ACC_B[22].ENA
EN => ACC_B[23].ENA
EN => ACC_B[24].ENA
EN => ACC_B[25].ENA
EN => ACC_B[26].ENA
EN => ACC_B[27].ENA
EN => ACC_B[28].ENA
EN => ACC_B[29].ENA
EN => ACC_B[30].ENA
EN => ACC_B[31].ENA
EN => ACC_A[0].ENA
EN => ACC_A[1].ENA
EN => ACC_A[2].ENA
EN => ACC_A[3].ENA
EN => ACC_A[4].ENA
EN => ACC_A[5].ENA
EN => ACC_A[6].ENA
EN => ACC_A[7].ENA
EN => ACC_A[8].ENA
EN => ACC_A[9].ENA
EN => ACC_A[10].ENA
EN => ACC_A[11].ENA
EN => ACC_A[12].ENA
EN => ACC_A[13].ENA
EN => ACC_A[14].ENA
EN => ACC_A[15].ENA
EN => ACC_A[16].ENA
EN => ACC_A[17].ENA
EN => ACC_A[18].ENA
EN => ACC_A[19].ENA
EN => ACC_A[20].ENA
EN => ACC_A[21].ENA
EN => ACC_A[22].ENA
EN => ACC_A[23].ENA
EN => ACC_A[24].ENA
EN => ACC_A[25].ENA
EN => ACC_A[26].ENA
EN => ACC_A[27].ENA
EN => ACC_A[28].ENA
EN => ACC_A[29].ENA
EN => ACC_A[30].ENA
EN => ACC_A[31].ENA
FREQAH_W[0] => FREQ_WORD_A[16].DATAIN
FREQAH_W[1] => FREQ_WORD_A[17].DATAIN
FREQAH_W[2] => FREQ_WORD_A[18].DATAIN
FREQAH_W[3] => FREQ_WORD_A[19].DATAIN
FREQAH_W[4] => FREQ_WORD_A[20].DATAIN
FREQAH_W[5] => FREQ_WORD_A[21].DATAIN
FREQAH_W[6] => FREQ_WORD_A[22].DATAIN
FREQAH_W[7] => FREQ_WORD_A[23].DATAIN
FREQAH_W[8] => FREQ_WORD_A[24].DATAIN
FREQAH_W[9] => FREQ_WORD_A[25].DATAIN
FREQAH_W[10] => FREQ_WORD_A[26].DATAIN
FREQAH_W[11] => FREQ_WORD_A[27].DATAIN
FREQAH_W[12] => FREQ_WORD_A[28].DATAIN
FREQAH_W[13] => FREQ_WORD_A[29].DATAIN
FREQAH_W[14] => FREQ_WORD_A[30].DATAIN
FREQAH_W[15] => FREQ_WORD_A[31].DATAIN
FREQAL_W[0] => FREQ_WORD_A[0].DATAIN
FREQAL_W[1] => FREQ_WORD_A[1].DATAIN
FREQAL_W[2] => FREQ_WORD_A[2].DATAIN
FREQAL_W[3] => FREQ_WORD_A[3].DATAIN
FREQAL_W[4] => FREQ_WORD_A[4].DATAIN
FREQAL_W[5] => FREQ_WORD_A[5].DATAIN
FREQAL_W[6] => FREQ_WORD_A[6].DATAIN
FREQAL_W[7] => FREQ_WORD_A[7].DATAIN
FREQAL_W[8] => FREQ_WORD_A[8].DATAIN
FREQAL_W[9] => FREQ_WORD_A[9].DATAIN
FREQAL_W[10] => FREQ_WORD_A[10].DATAIN
FREQAL_W[11] => FREQ_WORD_A[11].DATAIN
FREQAL_W[12] => FREQ_WORD_A[12].DATAIN
FREQAL_W[13] => FREQ_WORD_A[13].DATAIN
FREQAL_W[14] => FREQ_WORD_A[14].DATAIN
FREQAL_W[15] => FREQ_WORD_A[15].DATAIN
FREQBH_W[0] => FREQ_WORD_B[16].DATAIN
FREQBH_W[1] => FREQ_WORD_B[17].DATAIN
FREQBH_W[2] => FREQ_WORD_B[18].DATAIN
FREQBH_W[3] => FREQ_WORD_B[19].DATAIN
FREQBH_W[4] => FREQ_WORD_B[20].DATAIN
FREQBH_W[5] => FREQ_WORD_B[21].DATAIN
FREQBH_W[6] => FREQ_WORD_B[22].DATAIN
FREQBH_W[7] => FREQ_WORD_B[23].DATAIN
FREQBH_W[8] => FREQ_WORD_B[24].DATAIN
FREQBH_W[9] => FREQ_WORD_B[25].DATAIN
FREQBH_W[10] => FREQ_WORD_B[26].DATAIN
FREQBH_W[11] => FREQ_WORD_B[27].DATAIN
FREQBH_W[12] => FREQ_WORD_B[28].DATAIN
FREQBH_W[13] => FREQ_WORD_B[29].DATAIN
FREQBH_W[14] => FREQ_WORD_B[30].DATAIN
FREQBH_W[15] => FREQ_WORD_B[31].DATAIN
FREQBL_W[0] => FREQ_WORD_B[0].DATAIN
FREQBL_W[1] => FREQ_WORD_B[1].DATAIN
FREQBL_W[2] => FREQ_WORD_B[2].DATAIN
FREQBL_W[3] => FREQ_WORD_B[3].DATAIN
FREQBL_W[4] => FREQ_WORD_B[4].DATAIN
FREQBL_W[5] => FREQ_WORD_B[5].DATAIN
FREQBL_W[6] => FREQ_WORD_B[6].DATAIN
FREQBL_W[7] => FREQ_WORD_B[7].DATAIN
FREQBL_W[8] => FREQ_WORD_B[8].DATAIN
FREQBL_W[9] => FREQ_WORD_B[9].DATAIN
FREQBL_W[10] => FREQ_WORD_B[10].DATAIN
FREQBL_W[11] => FREQ_WORD_B[11].DATAIN
FREQBL_W[12] => FREQ_WORD_B[12].DATAIN
FREQBL_W[13] => FREQ_WORD_B[13].DATAIN
FREQBL_W[14] => FREQ_WORD_B[14].DATAIN
FREQBL_W[15] => FREQ_WORD_B[15].DATAIN
PHASEA_IN[0] => Selector9.IN4
PHASEA_IN[0] => LessThan4.IN10
PHASEA_IN[0] => Add5.IN20
PHASEA_IN[0] => Add8.IN10
PHASEA_IN[1] => Selector8.IN4
PHASEA_IN[1] => LessThan4.IN9
PHASEA_IN[1] => Add5.IN19
PHASEA_IN[1] => Add8.IN9
PHASEA_IN[2] => Selector7.IN4
PHASEA_IN[2] => LessThan4.IN8
PHASEA_IN[2] => Add5.IN18
PHASEA_IN[2] => Add8.IN8
PHASEA_IN[3] => Selector6.IN4
PHASEA_IN[3] => LessThan4.IN7
PHASEA_IN[3] => Add5.IN17
PHASEA_IN[3] => Add8.IN7
PHASEA_IN[4] => Selector5.IN4
PHASEA_IN[4] => LessThan4.IN6
PHASEA_IN[4] => Add5.IN16
PHASEA_IN[4] => Add8.IN6
PHASEA_IN[5] => Selector4.IN4
PHASEA_IN[5] => LessThan4.IN5
PHASEA_IN[5] => Add5.IN15
PHASEA_IN[5] => Add8.IN5
PHASEA_IN[6] => Selector3.IN4
PHASEA_IN[6] => LessThan4.IN4
PHASEA_IN[6] => Add5.IN14
PHASEA_IN[6] => Add8.IN4
PHASEA_IN[7] => Selector2.IN4
PHASEA_IN[7] => LessThan4.IN3
PHASEA_IN[7] => Add5.IN13
PHASEA_IN[7] => Add8.IN3
PHASEA_IN[8] => Selector1.IN4
PHASEA_IN[8] => LessThan4.IN2
PHASEA_IN[8] => Add5.IN12
PHASEA_IN[8] => Add8.IN2
PHASEA_IN[9] => Selector0.IN4
PHASEA_IN[9] => LessThan4.IN1
PHASEA_IN[9] => Add5.IN11
PHASEA_IN[9] => Add8.IN1
PHASEA_IN[10] => ~NO_FANOUT~
PHASEA_IN[11] => ~NO_FANOUT~
PHASEA_IN[12] => ~NO_FANOUT~
PHASEA_IN[13] => ~NO_FANOUT~
PHASEA_IN[14] => ~NO_FANOUT~
PHASEA_IN[15] => ~NO_FANOUT~
PHASEB_IN[0] => LessThan4.IN20
PHASEB_IN[0] => Selector19.IN4
PHASEB_IN[0] => Add8.IN20
PHASEB_IN[0] => Add5.IN10
PHASEB_IN[1] => LessThan4.IN19
PHASEB_IN[1] => Selector18.IN4
PHASEB_IN[1] => Add8.IN19
PHASEB_IN[1] => Add5.IN9
PHASEB_IN[2] => LessThan4.IN18
PHASEB_IN[2] => Selector17.IN4
PHASEB_IN[2] => Add8.IN18
PHASEB_IN[2] => Add5.IN8
PHASEB_IN[3] => LessThan4.IN17
PHASEB_IN[3] => Selector16.IN4
PHASEB_IN[3] => Add8.IN17
PHASEB_IN[3] => Add5.IN7
PHASEB_IN[4] => LessThan4.IN16
PHASEB_IN[4] => Selector15.IN4
PHASEB_IN[4] => Add8.IN16
PHASEB_IN[4] => Add5.IN6
PHASEB_IN[5] => LessThan4.IN15
PHASEB_IN[5] => Selector14.IN4
PHASEB_IN[5] => Add8.IN15
PHASEB_IN[5] => Add5.IN5
PHASEB_IN[6] => LessThan4.IN14
PHASEB_IN[6] => Selector13.IN4
PHASEB_IN[6] => Add8.IN14
PHASEB_IN[6] => Add5.IN4
PHASEB_IN[7] => LessThan4.IN13
PHASEB_IN[7] => Selector12.IN4
PHASEB_IN[7] => Add8.IN13
PHASEB_IN[7] => Add5.IN3
PHASEB_IN[8] => LessThan4.IN12
PHASEB_IN[8] => Selector11.IN4
PHASEB_IN[8] => Add8.IN12
PHASEB_IN[8] => Add5.IN2
PHASEB_IN[9] => LessThan4.IN11
PHASEB_IN[9] => Selector10.IN4
PHASEB_IN[9] => Add8.IN11
PHASEB_IN[9] => Add5.IN1
PHASEB_IN[10] => ~NO_FANOUT~
PHASEB_IN[11] => ~NO_FANOUT~
PHASEB_IN[12] => ~NO_FANOUT~
PHASEB_IN[13] => ~NO_FANOUT~
PHASEB_IN[14] => ~NO_FANOUT~
PHASEB_IN[15] => ~NO_FANOUT~
CS => always4.IN0
WR_EN => always4.IN1
ADDR[0] => Equal1.IN31
ADDR[0] => Equal2.IN31
ADDR[1] => Equal1.IN30
ADDR[1] => Equal2.IN30
ADDR[2] => Equal1.IN29
ADDR[2] => Equal2.IN29
ADDR[3] => Equal1.IN28
ADDR[3] => Equal2.IN28
ADDR[4] => Equal1.IN27
ADDR[4] => Equal2.IN27
ADDR[5] => Equal1.IN26
ADDR[5] => Equal2.IN26
ADDR[6] => Equal1.IN25
ADDR[6] => Equal2.IN25
ADDR[7] => Equal1.IN24
ADDR[7] => Equal2.IN24
ADDR[8] => Equal1.IN23
ADDR[8] => Equal2.IN23
ADDR[9] => Equal1.IN22
ADDR[9] => Equal2.IN22
ADDR[10] => Equal1.IN21
ADDR[10] => Equal2.IN21
ADDR[11] => Equal1.IN20
ADDR[11] => Equal2.IN20
ADDR[12] => Equal1.IN19
ADDR[12] => Equal2.IN19
ADDR[13] => Equal1.IN18
ADDR[13] => Equal2.IN18
ADDR[14] => Equal1.IN17
ADDR[14] => Equal2.IN17
ADDR[15] => Equal1.IN16
ADDR[15] => Equal2.IN16
COUT_A_FINAL[0] <= COUT_A_FINAL[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_A_FINAL[1] <= COUT_A_FINAL[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_A_FINAL[2] <= COUT_A_FINAL[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_A_FINAL[3] <= COUT_A_FINAL[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_A_FINAL[4] <= COUT_A_FINAL[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_A_FINAL[5] <= COUT_A_FINAL[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_A_FINAL[6] <= COUT_A_FINAL[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_A_FINAL[7] <= COUT_A_FINAL[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_A_FINAL[8] <= COUT_A_FINAL[8]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_A_FINAL[9] <= COUT_A_FINAL[9]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_B_FINAL[0] <= COUT_B_FINAL[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_B_FINAL[1] <= COUT_B_FINAL[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_B_FINAL[2] <= COUT_B_FINAL[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_B_FINAL[3] <= COUT_B_FINAL[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_B_FINAL[4] <= COUT_B_FINAL[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_B_FINAL[5] <= COUT_B_FINAL[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_B_FINAL[6] <= COUT_B_FINAL[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_B_FINAL[7] <= COUT_B_FINAL[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_B_FINAL[8] <= COUT_B_FINAL[8]~reg0.DB_MAX_OUTPUT_PORT_TYPE
COUT_B_FINAL[9] <= COUT_B_FINAL[9]~reg0.DB_MAX_OUTPUT_PORT_TYPE
FREQ_OUT_A_FINAL <= FREQ_OUT_A.DB_MAX_OUTPUT_PORT_TYPE
FREQ_OUT_B_FINAL <= FREQ_OUT_B_FINAL.DB_MAX_OUTPUT_PORT_TYPE


|TOP|MYPLL:inst6
inclk0 => sub_wire1[0].IN1
c0 <= altpll:altpll_component.clk


|TOP|MYPLL:inst6|altpll:altpll_component
inclk[0] => MYPLL_altpll1:auto_generated.inclk[0]
inclk[1] => MYPLL_altpll1:auto_generated.inclk[1]
fbin => ~NO_FANOUT~
pllena => ~NO_FANOUT~
clkswitch => ~NO_FANOUT~
areset => ~NO_FANOUT~
pfdena => ~NO_FANOUT~
clkena[0] => ~NO_FANOUT~
clkena[1] => ~NO_FANOUT~
clkena[2] => ~NO_FANOUT~
clkena[3] => ~NO_FANOUT~
clkena[4] => ~NO_FANOUT~
clkena[5] => ~NO_FANOUT~
extclkena[0] => ~NO_FANOUT~
extclkena[1] => ~NO_FANOUT~
extclkena[2] => ~NO_FANOUT~
extclkena[3] => ~NO_FANOUT~
scanclk => ~NO_FANOUT~
scanclkena => ~NO_FANOUT~
scanaclr => ~NO_FANOUT~
scanread => ~NO_FANOUT~
scanwrite => ~NO_FANOUT~
scandata => ~NO_FANOUT~
phasecounterselect[0] => ~NO_FANOUT~
phasecounterselect[1] => ~NO_FANOUT~
phasecounterselect[2] => ~NO_FANOUT~
phasecounterselect[3] => ~NO_FANOUT~
phaseupdown => ~NO_FANOUT~
phasestep => ~NO_FANOUT~
configupdate => ~NO_FANOUT~
fbmimicbidir <> <GND>
clk[0] <= clk[0].DB_MAX_OUTPUT_PORT_TYPE
clk[1] <= clk[1].DB_MAX_OUTPUT_PORT_TYPE
clk[2] <= clk[2].DB_MAX_OUTPUT_PORT_TYPE
clk[3] <= clk[3].DB_MAX_OUTPUT_PORT_TYPE
clk[4] <= clk[4].DB_MAX_OUTPUT_PORT_TYPE
extclk[0] <= <GND>
extclk[1] <= <GND>
extclk[2] <= <GND>
extclk[3] <= <GND>
clkbad[0] <= <GND>
clkbad[1] <= <GND>
enable1 <= <GND>
enable0 <= <GND>
activeclock <= <GND>
clkloss <= <GND>
locked <= <GND>
scandataout <= <GND>
scandone <= <GND>
sclkout0 <= <GND>
sclkout1 <= <GND>
phasedone <= <GND>
vcooverrange <= <GND>
vcounderrange <= <GND>
fbout <= <GND>
fref <= <GND>
icdrclk <= <GND>


|TOP|MYPLL:inst6|altpll:altpll_component|MYPLL_altpll1:auto_generated
clk[0] <= pll1.CLK
clk[1] <= pll1.CLK1
clk[2] <= pll1.CLK2
clk[3] <= pll1.CLK3
clk[4] <= pll1.CLK4
inclk[0] => pll1.CLK
inclk[1] => pll1.CLK1


|TOP|MASTER_CTRL:inst3
CS => always0.IN0
WR_EN => always0.IN1
ADDR[0] => Equal0.IN0
ADDR[1] => Equal0.IN15
ADDR[2] => Equal0.IN14
ADDR[3] => Equal0.IN13
ADDR[4] => Equal0.IN12
ADDR[5] => Equal0.IN11
ADDR[6] => Equal0.IN10
ADDR[7] => Equal0.IN9
ADDR[8] => Equal0.IN8
ADDR[9] => Equal0.IN7
ADDR[10] => Equal0.IN6
ADDR[11] => Equal0.IN5
ADDR[12] => Equal0.IN4
ADDR[13] => Equal0.IN3
ADDR[14] => Equal0.IN2
ADDR[15] => Equal0.IN1
DATA[0] => CTRL_DATA[0]$latch.DATAIN
DATA[1] => CTRL_DATA[1]$latch.DATAIN
DATA[2] => CTRL_DATA[2]$latch.DATAIN
DATA[3] => CTRL_DATA[3]$latch.DATAIN
DATA[4] => CTRL_DATA[4]$latch.DATAIN
DATA[5] => CTRL_DATA[5]$latch.DATAIN
DATA[6] => CTRL_DATA[6]$latch.DATAIN
DATA[7] => CTRL_DATA[7]$latch.DATAIN
DATA[8] => CTRL_DATA[8]$latch.DATAIN
DATA[9] => CTRL_DATA[9]$latch.DATAIN
DATA[10] => CTRL_DATA[10]$latch.DATAIN
DATA[11] => CTRL_DATA[11]$latch.DATAIN
DATA[12] => CTRL_DATA[12]$latch.DATAIN
DATA[13] => CTRL_DATA[13]$latch.DATAIN
DATA[14] => CTRL_DATA[14]$latch.DATAIN
DATA[15] => CTRL_DATA[15]$latch.DATAIN
CTRL_DATA[0] <= CTRL_DATA[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[1] <= CTRL_DATA[1]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[2] <= CTRL_DATA[2]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[3] <= CTRL_DATA[3]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[4] <= CTRL_DATA[4]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[5] <= CTRL_DATA[5]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[6] <= CTRL_DATA[6]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[7] <= CTRL_DATA[7]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[8] <= CTRL_DATA[8]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[9] <= CTRL_DATA[9]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[10] <= CTRL_DATA[10]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[11] <= CTRL_DATA[11]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[12] <= CTRL_DATA[12]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[13] <= CTRL_DATA[13]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[14] <= CTRL_DATA[14]$latch.DB_MAX_OUTPUT_PORT_TYPE
CTRL_DATA[15] <= CTRL_DATA[15]$latch.DB_MAX_OUTPUT_PORT_TYPE


|TOP|fmc_control:inst
clk => rd_data_reg[0].CLK
clk => rd_data_reg[1].CLK
clk => rd_data_reg[2].CLK
clk => rd_data_reg[3].CLK
clk => rd_data_reg[4].CLK
clk => rd_data_reg[5].CLK
clk => rd_data_reg[6].CLK
clk => rd_data_reg[7].CLK
clk => rd_data_reg[8].CLK
clk => rd_data_reg[9].CLK
clk => rd_data_reg[10].CLK
clk => rd_data_reg[11].CLK
clk => rd_data_reg[12].CLK
clk => rd_data_reg[13].CLK
clk => rd_data_reg[14].CLK
clk => rd_data_reg[15].CLK
clk => read_data_15__reg[0].CLK
clk => read_data_15__reg[1].CLK
clk => read_data_15__reg[2].CLK
clk => read_data_15__reg[3].CLK
clk => read_data_15__reg[4].CLK
clk => read_data_15__reg[5].CLK
clk => read_data_15__reg[6].CLK
clk => read_data_15__reg[7].CLK
clk => read_data_15__reg[8].CLK
clk => read_data_15__reg[9].CLK
clk => read_data_15__reg[10].CLK
clk => read_data_15__reg[11].CLK
clk => read_data_15__reg[12].CLK
clk => read_data_15__reg[13].CLK
clk => read_data_15__reg[14].CLK
clk => read_data_15__reg[15].CLK
clk => read_data_14__reg[0].CLK
clk => read_data_14__reg[1].CLK
clk => read_data_14__reg[2].CLK
clk => read_data_14__reg[3].CLK
clk => read_data_14__reg[4].CLK
clk => read_data_14__reg[5].CLK
clk => read_data_14__reg[6].CLK
clk => read_data_14__reg[7].CLK
clk => read_data_14__reg[8].CLK
clk => read_data_14__reg[9].CLK
clk => read_data_14__reg[10].CLK
clk => read_data_14__reg[11].CLK
clk => read_data_14__reg[12].CLK
clk => read_data_14__reg[13].CLK
clk => read_data_14__reg[14].CLK
clk => read_data_14__reg[15].CLK
clk => read_data_13__reg[0].CLK
clk => read_data_13__reg[1].CLK
clk => read_data_13__reg[2].CLK
clk => read_data_13__reg[3].CLK
clk => read_data_13__reg[4].CLK
clk => read_data_13__reg[5].CLK
clk => read_data_13__reg[6].CLK
clk => read_data_13__reg[7].CLK
clk => read_data_13__reg[8].CLK
clk => read_data_13__reg[9].CLK
clk => read_data_13__reg[10].CLK
clk => read_data_13__reg[11].CLK
clk => read_data_13__reg[12].CLK
clk => read_data_13__reg[13].CLK
clk => read_data_13__reg[14].CLK
clk => read_data_13__reg[15].CLK
clk => read_data_12__reg[0].CLK
clk => read_data_12__reg[1].CLK
clk => read_data_12__reg[2].CLK
clk => read_data_12__reg[3].CLK
clk => read_data_12__reg[4].CLK
clk => read_data_12__reg[5].CLK
clk => read_data_12__reg[6].CLK
clk => read_data_12__reg[7].CLK
clk => read_data_12__reg[8].CLK
clk => read_data_12__reg[9].CLK
clk => read_data_12__reg[10].CLK
clk => read_data_12__reg[11].CLK
clk => read_data_12__reg[12].CLK
clk => read_data_12__reg[13].CLK
clk => read_data_12__reg[14].CLK
clk => read_data_12__reg[15].CLK
clk => read_data_11__reg[0].CLK
clk => read_data_11__reg[1].CLK
clk => read_data_11__reg[2].CLK
clk => read_data_11__reg[3].CLK
clk => read_data_11__reg[4].CLK
clk => read_data_11__reg[5].CLK
clk => read_data_11__reg[6].CLK
clk => read_data_11__reg[7].CLK
clk => read_data_11__reg[8].CLK
clk => read_data_11__reg[9].CLK
clk => read_data_11__reg[10].CLK
clk => read_data_11__reg[11].CLK
clk => read_data_11__reg[12].CLK
clk => read_data_11__reg[13].CLK
clk => read_data_11__reg[14].CLK
clk => read_data_11__reg[15].CLK
clk => read_data_10__reg[0].CLK
clk => read_data_10__reg[1].CLK
clk => read_data_10__reg[2].CLK
clk => read_data_10__reg[3].CLK
clk => read_data_10__reg[4].CLK
clk => read_data_10__reg[5].CLK
clk => read_data_10__reg[6].CLK
clk => read_data_10__reg[7].CLK
clk => read_data_10__reg[8].CLK
clk => read_data_10__reg[9].CLK
clk => read_data_10__reg[10].CLK
clk => read_data_10__reg[11].CLK
clk => read_data_10__reg[12].CLK
clk => read_data_10__reg[13].CLK
clk => read_data_10__reg[14].CLK
clk => read_data_10__reg[15].CLK
clk => read_data_9__reg[0].CLK
clk => read_data_9__reg[1].CLK
clk => read_data_9__reg[2].CLK
clk => read_data_9__reg[3].CLK
clk => read_data_9__reg[4].CLK
clk => read_data_9__reg[5].CLK
clk => read_data_9__reg[6].CLK
clk => read_data_9__reg[7].CLK
clk => read_data_9__reg[8].CLK
clk => read_data_9__reg[9].CLK
clk => read_data_9__reg[10].CLK
clk => read_data_9__reg[11].CLK
clk => read_data_9__reg[12].CLK
clk => read_data_9__reg[13].CLK
clk => read_data_9__reg[14].CLK
clk => read_data_9__reg[15].CLK
clk => read_data_8__reg[0].CLK
clk => read_data_8__reg[1].CLK
clk => read_data_8__reg[2].CLK
clk => read_data_8__reg[3].CLK
clk => read_data_8__reg[4].CLK
clk => read_data_8__reg[5].CLK
clk => read_data_8__reg[6].CLK
clk => read_data_8__reg[7].CLK
clk => read_data_8__reg[8].CLK
clk => read_data_8__reg[9].CLK
clk => read_data_8__reg[10].CLK
clk => read_data_8__reg[11].CLK
clk => read_data_8__reg[12].CLK
clk => read_data_8__reg[13].CLK
clk => read_data_8__reg[14].CLK
clk => read_data_8__reg[15].CLK
clk => read_data_7__reg[0].CLK
clk => read_data_7__reg[1].CLK
clk => read_data_7__reg[2].CLK
clk => read_data_7__reg[3].CLK
clk => read_data_7__reg[4].CLK
clk => read_data_7__reg[5].CLK
clk => read_data_7__reg[6].CLK
clk => read_data_7__reg[7].CLK
clk => read_data_7__reg[8].CLK
clk => read_data_7__reg[9].CLK
clk => read_data_7__reg[10].CLK
clk => read_data_7__reg[11].CLK
clk => read_data_7__reg[12].CLK
clk => read_data_7__reg[13].CLK
clk => read_data_7__reg[14].CLK
clk => read_data_7__reg[15].CLK
clk => read_data_6__reg[0].CLK
clk => read_data_6__reg[1].CLK
clk => read_data_6__reg[2].CLK
clk => read_data_6__reg[3].CLK
clk => read_data_6__reg[4].CLK
clk => read_data_6__reg[5].CLK
clk => read_data_6__reg[6].CLK
clk => read_data_6__reg[7].CLK
clk => read_data_6__reg[8].CLK
clk => read_data_6__reg[9].CLK
clk => read_data_6__reg[10].CLK
clk => read_data_6__reg[11].CLK
clk => read_data_6__reg[12].CLK
clk => read_data_6__reg[13].CLK
clk => read_data_6__reg[14].CLK
clk => read_data_6__reg[15].CLK
clk => read_data_5__reg[0].CLK
clk => read_data_5__reg[1].CLK
clk => read_data_5__reg[2].CLK
clk => read_data_5__reg[3].CLK
clk => read_data_5__reg[4].CLK
clk => read_data_5__reg[5].CLK
clk => read_data_5__reg[6].CLK
clk => read_data_5__reg[7].CLK
clk => read_data_5__reg[8].CLK
clk => read_data_5__reg[9].CLK
clk => read_data_5__reg[10].CLK
clk => read_data_5__reg[11].CLK
clk => read_data_5__reg[12].CLK
clk => read_data_5__reg[13].CLK
clk => read_data_5__reg[14].CLK
clk => read_data_5__reg[15].CLK
clk => read_data_4__reg[0].CLK
clk => read_data_4__reg[1].CLK
clk => read_data_4__reg[2].CLK
clk => read_data_4__reg[3].CLK
clk => read_data_4__reg[4].CLK
clk => read_data_4__reg[5].CLK
clk => read_data_4__reg[6].CLK
clk => read_data_4__reg[7].CLK
clk => read_data_4__reg[8].CLK
clk => read_data_4__reg[9].CLK
clk => read_data_4__reg[10].CLK
clk => read_data_4__reg[11].CLK
clk => read_data_4__reg[12].CLK
clk => read_data_4__reg[13].CLK
clk => read_data_4__reg[14].CLK
clk => read_data_4__reg[15].CLK
clk => read_data_3__reg[0].CLK
clk => read_data_3__reg[1].CLK
clk => read_data_3__reg[2].CLK
clk => read_data_3__reg[3].CLK
clk => read_data_3__reg[4].CLK
clk => read_data_3__reg[5].CLK
clk => read_data_3__reg[6].CLK
clk => read_data_3__reg[7].CLK
clk => read_data_3__reg[8].CLK
clk => read_data_3__reg[9].CLK
clk => read_data_3__reg[10].CLK
clk => read_data_3__reg[11].CLK
clk => read_data_3__reg[12].CLK
clk => read_data_3__reg[13].CLK
clk => read_data_3__reg[14].CLK
clk => read_data_3__reg[15].CLK
clk => read_data_2__reg[0].CLK
clk => read_data_2__reg[1].CLK
clk => read_data_2__reg[2].CLK
clk => read_data_2__reg[3].CLK
clk => read_data_2__reg[4].CLK
clk => read_data_2__reg[5].CLK
clk => read_data_2__reg[6].CLK
clk => read_data_2__reg[7].CLK
clk => read_data_2__reg[8].CLK
clk => read_data_2__reg[9].CLK
clk => read_data_2__reg[10].CLK
clk => read_data_2__reg[11].CLK
clk => read_data_2__reg[12].CLK
clk => read_data_2__reg[13].CLK
clk => read_data_2__reg[14].CLK
clk => read_data_2__reg[15].CLK
clk => read_data_1__reg[0].CLK
clk => read_data_1__reg[1].CLK
clk => read_data_1__reg[2].CLK
clk => read_data_1__reg[3].CLK
clk => read_data_1__reg[4].CLK
clk => read_data_1__reg[5].CLK
clk => read_data_1__reg[6].CLK
clk => read_data_1__reg[7].CLK
clk => read_data_1__reg[8].CLK
clk => read_data_1__reg[9].CLK
clk => read_data_1__reg[10].CLK
clk => read_data_1__reg[11].CLK
clk => read_data_1__reg[12].CLK
clk => read_data_1__reg[13].CLK
clk => read_data_1__reg[14].CLK
clk => read_data_1__reg[15].CLK
clk => read_data_0__reg[0].CLK
clk => read_data_0__reg[1].CLK
clk => read_data_0__reg[2].CLK
clk => read_data_0__reg[3].CLK
clk => read_data_0__reg[4].CLK
clk => read_data_0__reg[5].CLK
clk => read_data_0__reg[6].CLK
clk => read_data_0__reg[7].CLK
clk => read_data_0__reg[8].CLK
clk => read_data_0__reg[9].CLK
clk => read_data_0__reg[10].CLK
clk => read_data_0__reg[11].CLK
clk => read_data_0__reg[12].CLK
clk => read_data_0__reg[13].CLK
clk => read_data_0__reg[14].CLK
clk => read_data_0__reg[15].CLK
rst => read_data_15__reg[0].ACLR
rst => read_data_15__reg[1].ACLR
rst => read_data_15__reg[2].ACLR
rst => read_data_15__reg[3].ACLR
rst => read_data_15__reg[4].ACLR
rst => read_data_15__reg[5].ACLR
rst => read_data_15__reg[6].ACLR
rst => read_data_15__reg[7].ACLR
rst => read_data_15__reg[8].ACLR
rst => read_data_15__reg[9].ACLR
rst => read_data_15__reg[10].ACLR
rst => read_data_15__reg[11].ACLR
rst => read_data_15__reg[12].ACLR
rst => read_data_15__reg[13].ACLR
rst => read_data_15__reg[14].ACLR
rst => read_data_15__reg[15].ACLR
rst => read_data_14__reg[0].ACLR
rst => read_data_14__reg[1].ACLR
rst => read_data_14__reg[2].ACLR
rst => read_data_14__reg[3].ACLR
rst => read_data_14__reg[4].ACLR
rst => read_data_14__reg[5].ACLR
rst => read_data_14__reg[6].ACLR
rst => read_data_14__reg[7].ACLR
rst => read_data_14__reg[8].ACLR
rst => read_data_14__reg[9].ACLR
rst => read_data_14__reg[10].ACLR
rst => read_data_14__reg[11].ACLR
rst => read_data_14__reg[12].ACLR
rst => read_data_14__reg[13].ACLR
rst => read_data_14__reg[14].ACLR
rst => read_data_14__reg[15].ACLR
rst => read_data_13__reg[0].ACLR
rst => read_data_13__reg[1].ACLR
rst => read_data_13__reg[2].ACLR
rst => read_data_13__reg[3].ACLR
rst => read_data_13__reg[4].ACLR
rst => read_data_13__reg[5].ACLR
rst => read_data_13__reg[6].ACLR
rst => read_data_13__reg[7].ACLR
rst => read_data_13__reg[8].ACLR
rst => read_data_13__reg[9].ACLR
rst => read_data_13__reg[10].ACLR
rst => read_data_13__reg[11].ACLR
rst => read_data_13__reg[12].ACLR
rst => read_data_13__reg[13].ACLR
rst => read_data_13__reg[14].ACLR
rst => read_data_13__reg[15].ACLR
rst => read_data_12__reg[0].ACLR
rst => read_data_12__reg[1].ACLR
rst => read_data_12__reg[2].ACLR
rst => read_data_12__reg[3].ACLR
rst => read_data_12__reg[4].ACLR
rst => read_data_12__reg[5].ACLR
rst => read_data_12__reg[6].ACLR
rst => read_data_12__reg[7].ACLR
rst => read_data_12__reg[8].ACLR
rst => read_data_12__reg[9].ACLR
rst => read_data_12__reg[10].ACLR
rst => read_data_12__reg[11].ACLR
rst => read_data_12__reg[12].ACLR
rst => read_data_12__reg[13].ACLR
rst => read_data_12__reg[14].ACLR
rst => read_data_12__reg[15].ACLR
rst => read_data_11__reg[0].ACLR
rst => read_data_11__reg[1].ACLR
rst => read_data_11__reg[2].ACLR
rst => read_data_11__reg[3].ACLR
rst => read_data_11__reg[4].ACLR
rst => read_data_11__reg[5].ACLR
rst => read_data_11__reg[6].ACLR
rst => read_data_11__reg[7].ACLR
rst => read_data_11__reg[8].ACLR
rst => read_data_11__reg[9].ACLR
rst => read_data_11__reg[10].ACLR
rst => read_data_11__reg[11].ACLR
rst => read_data_11__reg[12].ACLR
rst => read_data_11__reg[13].ACLR
rst => read_data_11__reg[14].ACLR
rst => read_data_11__reg[15].ACLR
rst => read_data_10__reg[0].ACLR
rst => read_data_10__reg[1].ACLR
rst => read_data_10__reg[2].ACLR
rst => read_data_10__reg[3].ACLR
rst => read_data_10__reg[4].ACLR
rst => read_data_10__reg[5].ACLR
rst => read_data_10__reg[6].ACLR
rst => read_data_10__reg[7].ACLR
rst => read_data_10__reg[8].ACLR
rst => read_data_10__reg[9].ACLR
rst => read_data_10__reg[10].ACLR
rst => read_data_10__reg[11].ACLR
rst => read_data_10__reg[12].ACLR
rst => read_data_10__reg[13].ACLR
rst => read_data_10__reg[14].ACLR
rst => read_data_10__reg[15].ACLR
rst => read_data_9__reg[0].ACLR
rst => read_data_9__reg[1].ACLR
rst => read_data_9__reg[2].ACLR
rst => read_data_9__reg[3].ACLR
rst => read_data_9__reg[4].ACLR
rst => read_data_9__reg[5].ACLR
rst => read_data_9__reg[6].ACLR
rst => read_data_9__reg[7].ACLR
rst => read_data_9__reg[8].ACLR
rst => read_data_9__reg[9].ACLR
rst => read_data_9__reg[10].ACLR
rst => read_data_9__reg[11].ACLR
rst => read_data_9__reg[12].ACLR
rst => read_data_9__reg[13].ACLR
rst => read_data_9__reg[14].ACLR
rst => read_data_9__reg[15].ACLR
rst => read_data_8__reg[0].ACLR
rst => read_data_8__reg[1].ACLR
rst => read_data_8__reg[2].ACLR
rst => read_data_8__reg[3].ACLR
rst => read_data_8__reg[4].ACLR
rst => read_data_8__reg[5].ACLR
rst => read_data_8__reg[6].ACLR
rst => read_data_8__reg[7].ACLR
rst => read_data_8__reg[8].ACLR
rst => read_data_8__reg[9].ACLR
rst => read_data_8__reg[10].ACLR
rst => read_data_8__reg[11].ACLR
rst => read_data_8__reg[12].ACLR
rst => read_data_8__reg[13].ACLR
rst => read_data_8__reg[14].ACLR
rst => read_data_8__reg[15].ACLR
rst => read_data_7__reg[0].ACLR
rst => read_data_7__reg[1].ACLR
rst => read_data_7__reg[2].ACLR
rst => read_data_7__reg[3].ACLR
rst => read_data_7__reg[4].ACLR
rst => read_data_7__reg[5].ACLR
rst => read_data_7__reg[6].ACLR
rst => read_data_7__reg[7].ACLR
rst => read_data_7__reg[8].ACLR
rst => read_data_7__reg[9].ACLR
rst => read_data_7__reg[10].ACLR
rst => read_data_7__reg[11].ACLR
rst => read_data_7__reg[12].ACLR
rst => read_data_7__reg[13].ACLR
rst => read_data_7__reg[14].ACLR
rst => read_data_7__reg[15].ACLR
rst => read_data_6__reg[0].ACLR
rst => read_data_6__reg[1].ACLR
rst => read_data_6__reg[2].ACLR
rst => read_data_6__reg[3].ACLR
rst => read_data_6__reg[4].ACLR
rst => read_data_6__reg[5].ACLR
rst => read_data_6__reg[6].ACLR
rst => read_data_6__reg[7].ACLR
rst => read_data_6__reg[8].ACLR
rst => read_data_6__reg[9].ACLR
rst => read_data_6__reg[10].ACLR
rst => read_data_6__reg[11].ACLR
rst => read_data_6__reg[12].ACLR
rst => read_data_6__reg[13].ACLR
rst => read_data_6__reg[14].ACLR
rst => read_data_6__reg[15].ACLR
rst => read_data_5__reg[0].ACLR
rst => read_data_5__reg[1].ACLR
rst => read_data_5__reg[2].ACLR
rst => read_data_5__reg[3].ACLR
rst => read_data_5__reg[4].ACLR
rst => read_data_5__reg[5].ACLR
rst => read_data_5__reg[6].ACLR
rst => read_data_5__reg[7].ACLR
rst => read_data_5__reg[8].ACLR
rst => read_data_5__reg[9].ACLR
rst => read_data_5__reg[10].ACLR
rst => read_data_5__reg[11].ACLR
rst => read_data_5__reg[12].ACLR
rst => read_data_5__reg[13].ACLR
rst => read_data_5__reg[14].ACLR
rst => read_data_5__reg[15].ACLR
rst => read_data_4__reg[0].ACLR
rst => read_data_4__reg[1].ACLR
rst => read_data_4__reg[2].ACLR
rst => read_data_4__reg[3].ACLR
rst => read_data_4__reg[4].ACLR
rst => read_data_4__reg[5].ACLR
rst => read_data_4__reg[6].ACLR
rst => read_data_4__reg[7].ACLR
rst => read_data_4__reg[8].ACLR
rst => read_data_4__reg[9].ACLR
rst => read_data_4__reg[10].ACLR
rst => read_data_4__reg[11].ACLR
rst => read_data_4__reg[12].ACLR
rst => read_data_4__reg[13].ACLR
rst => read_data_4__reg[14].ACLR
rst => read_data_4__reg[15].ACLR
rst => read_data_3__reg[0].ACLR
rst => read_data_3__reg[1].ACLR
rst => read_data_3__reg[2].ACLR
rst => read_data_3__reg[3].ACLR
rst => read_data_3__reg[4].ACLR
rst => read_data_3__reg[5].ACLR
rst => read_data_3__reg[6].ACLR
rst => read_data_3__reg[7].ACLR
rst => read_data_3__reg[8].ACLR
rst => read_data_3__reg[9].ACLR
rst => read_data_3__reg[10].ACLR
rst => read_data_3__reg[11].ACLR
rst => read_data_3__reg[12].ACLR
rst => read_data_3__reg[13].ACLR
rst => read_data_3__reg[14].ACLR
rst => read_data_3__reg[15].ACLR
rst => read_data_2__reg[0].ACLR
rst => read_data_2__reg[1].ACLR
rst => read_data_2__reg[2].ACLR
rst => read_data_2__reg[3].ACLR
rst => read_data_2__reg[4].ACLR
rst => read_data_2__reg[5].ACLR
rst => read_data_2__reg[6].ACLR
rst => read_data_2__reg[7].ACLR
rst => read_data_2__reg[8].ACLR
rst => read_data_2__reg[9].ACLR
rst => read_data_2__reg[10].ACLR
rst => read_data_2__reg[11].ACLR
rst => read_data_2__reg[12].ACLR
rst => read_data_2__reg[13].ACLR
rst => read_data_2__reg[14].ACLR
rst => read_data_2__reg[15].ACLR
rst => read_data_1__reg[0].ACLR
rst => read_data_1__reg[1].ACLR
rst => read_data_1__reg[2].ACLR
rst => read_data_1__reg[3].ACLR
rst => read_data_1__reg[4].ACLR
rst => read_data_1__reg[5].ACLR
rst => read_data_1__reg[6].ACLR
rst => read_data_1__reg[7].ACLR
rst => read_data_1__reg[8].ACLR
rst => read_data_1__reg[9].ACLR
rst => read_data_1__reg[10].ACLR
rst => read_data_1__reg[11].ACLR
rst => read_data_1__reg[12].ACLR
rst => read_data_1__reg[13].ACLR
rst => read_data_1__reg[14].ACLR
rst => read_data_1__reg[15].ACLR
rst => read_data_0__reg[0].ACLR
rst => read_data_0__reg[1].ACLR
rst => read_data_0__reg[2].ACLR
rst => read_data_0__reg[3].ACLR
rst => read_data_0__reg[4].ACLR
rst => read_data_0__reg[5].ACLR
rst => read_data_0__reg[6].ACLR
rst => read_data_0__reg[7].ACLR
rst => read_data_0__reg[8].ACLR
rst => read_data_0__reg[9].ACLR
rst => read_data_0__reg[10].ACLR
rst => read_data_0__reg[11].ACLR
rst => read_data_0__reg[12].ACLR
rst => read_data_0__reg[13].ACLR
rst => read_data_0__reg[14].ACLR
rst => read_data_0__reg[15].ACLR
rst => rd_data_reg[0].ENA
rst => rd_data_reg[15].ENA
rst => rd_data_reg[14].ENA
rst => rd_data_reg[13].ENA
rst => rd_data_reg[12].ENA
rst => rd_data_reg[11].ENA
rst => rd_data_reg[10].ENA
rst => rd_data_reg[9].ENA
rst => rd_data_reg[8].ENA
rst => rd_data_reg[7].ENA
rst => rd_data_reg[6].ENA
rst => rd_data_reg[5].ENA
rst => rd_data_reg[4].ENA
rst => rd_data_reg[3].ENA
rst => rd_data_reg[2].ENA
rst => rd_data_reg[1].ENA
fpga_nl_nadv => fmc_wr_en.IN1
fpga_nl_nadv => fmc_rd_en.IN1
fpga_nl_nadv => addr.IN0
fpga_cs_ne1 => fmc_rd_en.IN0
fpga_cs_ne1 => fmc_wr_en.IN0
fpga_cs_ne1 => addr.IN1
fpga_wr_nwe => fmc_wr_en.IN1
fpga_rd_noe => fmc_rd_en.IN1
fpga_db[0] <> fpga_db[0]
fpga_db[1] <> fpga_db[1]
fpga_db[2] <> fpga_db[2]
fpga_db[3] <> fpga_db[3]
fpga_db[4] <> fpga_db[4]
fpga_db[5] <> fpga_db[5]
fpga_db[6] <> fpga_db[6]
fpga_db[7] <> fpga_db[7]
fpga_db[8] <> fpga_db[8]
fpga_db[9] <> fpga_db[9]
fpga_db[10] <> fpga_db[10]
fpga_db[11] <> fpga_db[11]
fpga_db[12] <> fpga_db[12]
fpga_db[13] <> fpga_db[13]
fpga_db[14] <> fpga_db[14]
fpga_db[15] <> fpga_db[15]
write_data_0_[0] => Selector15.IN17
write_data_0_[1] => Selector14.IN17
write_data_0_[2] => Selector13.IN17
write_data_0_[3] => Selector12.IN17
write_data_0_[4] => Selector11.IN17
write_data_0_[5] => Selector10.IN17
write_data_0_[6] => Selector9.IN17
write_data_0_[7] => Selector8.IN17
write_data_0_[8] => Selector7.IN17
write_data_0_[9] => Selector6.IN17
write_data_0_[10] => Selector5.IN17
write_data_0_[11] => Selector4.IN17
write_data_0_[12] => Selector3.IN17
write_data_0_[13] => Selector2.IN17
write_data_0_[14] => Selector1.IN17
write_data_0_[15] => Selector0.IN17
write_data_1_[0] => Selector15.IN18
write_data_1_[1] => Selector14.IN18
write_data_1_[2] => Selector13.IN18
write_data_1_[3] => Selector12.IN18
write_data_1_[4] => Selector11.IN18
write_data_1_[5] => Selector10.IN18
write_data_1_[6] => Selector9.IN18
write_data_1_[7] => Selector8.IN18
write_data_1_[8] => Selector7.IN18
write_data_1_[9] => Selector6.IN18
write_data_1_[10] => Selector5.IN18
write_data_1_[11] => Selector4.IN18
write_data_1_[12] => Selector3.IN18
write_data_1_[13] => Selector2.IN18
write_data_1_[14] => Selector1.IN18
write_data_1_[15] => Selector0.IN18
write_data_2_[0] => Selector15.IN19
write_data_2_[1] => Selector14.IN19
write_data_2_[2] => Selector13.IN19
write_data_2_[3] => Selector12.IN19
write_data_2_[4] => Selector11.IN19
write_data_2_[5] => Selector10.IN19
write_data_2_[6] => Selector9.IN19
write_data_2_[7] => Selector8.IN19
write_data_2_[8] => Selector7.IN19
write_data_2_[9] => Selector6.IN19
write_data_2_[10] => Selector5.IN19
write_data_2_[11] => Selector4.IN19
write_data_2_[12] => Selector3.IN19
write_data_2_[13] => Selector2.IN19
write_data_2_[14] => Selector1.IN19
write_data_2_[15] => Selector0.IN19
write_data_3_[0] => Selector15.IN20
write_data_3_[1] => Selector14.IN20
write_data_3_[2] => Selector13.IN20
write_data_3_[3] => Selector12.IN20
write_data_3_[4] => Selector11.IN20
write_data_3_[5] => Selector10.IN20
write_data_3_[6] => Selector9.IN20
write_data_3_[7] => Selector8.IN20
write_data_3_[8] => Selector7.IN20
write_data_3_[9] => Selector6.IN20
write_data_3_[10] => Selector5.IN20
write_data_3_[11] => Selector4.IN20
write_data_3_[12] => Selector3.IN20
write_data_3_[13] => Selector2.IN20
write_data_3_[14] => Selector1.IN20
write_data_3_[15] => Selector0.IN20
write_data_4_[0] => Selector15.IN21
write_data_4_[1] => Selector14.IN21
write_data_4_[2] => Selector13.IN21
write_data_4_[3] => Selector12.IN21
write_data_4_[4] => Selector11.IN21
write_data_4_[5] => Selector10.IN21
write_data_4_[6] => Selector9.IN21
write_data_4_[7] => Selector8.IN21
write_data_4_[8] => Selector7.IN21
write_data_4_[9] => Selector6.IN21
write_data_4_[10] => Selector5.IN21
write_data_4_[11] => Selector4.IN21
write_data_4_[12] => Selector3.IN21
write_data_4_[13] => Selector2.IN21
write_data_4_[14] => Selector1.IN21
write_data_4_[15] => Selector0.IN21
write_data_5_[0] => Selector15.IN22
write_data_5_[1] => Selector14.IN22
write_data_5_[2] => Selector13.IN22
write_data_5_[3] => Selector12.IN22
write_data_5_[4] => Selector11.IN22
write_data_5_[5] => Selector10.IN22
write_data_5_[6] => Selector9.IN22
write_data_5_[7] => Selector8.IN22
write_data_5_[8] => Selector7.IN22
write_data_5_[9] => Selector6.IN22
write_data_5_[10] => Selector5.IN22
write_data_5_[11] => Selector4.IN22
write_data_5_[12] => Selector3.IN22
write_data_5_[13] => Selector2.IN22
write_data_5_[14] => Selector1.IN22
write_data_5_[15] => Selector0.IN22
write_data_6_[0] => Selector15.IN23
write_data_6_[1] => Selector14.IN23
write_data_6_[2] => Selector13.IN23
write_data_6_[3] => Selector12.IN23
write_data_6_[4] => Selector11.IN23
write_data_6_[5] => Selector10.IN23
write_data_6_[6] => Selector9.IN23
write_data_6_[7] => Selector8.IN23
write_data_6_[8] => Selector7.IN23
write_data_6_[9] => Selector6.IN23
write_data_6_[10] => Selector5.IN23
write_data_6_[11] => Selector4.IN23
write_data_6_[12] => Selector3.IN23
write_data_6_[13] => Selector2.IN23
write_data_6_[14] => Selector1.IN23
write_data_6_[15] => Selector0.IN23
write_data_7_[0] => Selector15.IN24
write_data_7_[1] => Selector14.IN24
write_data_7_[2] => Selector13.IN24
write_data_7_[3] => Selector12.IN24
write_data_7_[4] => Selector11.IN24
write_data_7_[5] => Selector10.IN24
write_data_7_[6] => Selector9.IN24
write_data_7_[7] => Selector8.IN24
write_data_7_[8] => Selector7.IN24
write_data_7_[9] => Selector6.IN24
write_data_7_[10] => Selector5.IN24
write_data_7_[11] => Selector4.IN24
write_data_7_[12] => Selector3.IN24
write_data_7_[13] => Selector2.IN24
write_data_7_[14] => Selector1.IN24
write_data_7_[15] => Selector0.IN24
write_data_8_[0] => Selector15.IN25
write_data_8_[1] => Selector14.IN25
write_data_8_[2] => Selector13.IN25
write_data_8_[3] => Selector12.IN25
write_data_8_[4] => Selector11.IN25
write_data_8_[5] => Selector10.IN25
write_data_8_[6] => Selector9.IN25
write_data_8_[7] => Selector8.IN25
write_data_8_[8] => Selector7.IN25
write_data_8_[9] => Selector6.IN25
write_data_8_[10] => Selector5.IN25
write_data_8_[11] => Selector4.IN25
write_data_8_[12] => Selector3.IN25
write_data_8_[13] => Selector2.IN25
write_data_8_[14] => Selector1.IN25
write_data_8_[15] => Selector0.IN25
write_data_9_[0] => Selector15.IN26
write_data_9_[1] => Selector14.IN26
write_data_9_[2] => Selector13.IN26
write_data_9_[3] => Selector12.IN26
write_data_9_[4] => Selector11.IN26
write_data_9_[5] => Selector10.IN26
write_data_9_[6] => Selector9.IN26
write_data_9_[7] => Selector8.IN26
write_data_9_[8] => Selector7.IN26
write_data_9_[9] => Selector6.IN26
write_data_9_[10] => Selector5.IN26
write_data_9_[11] => Selector4.IN26
write_data_9_[12] => Selector3.IN26
write_data_9_[13] => Selector2.IN26
write_data_9_[14] => Selector1.IN26
write_data_9_[15] => Selector0.IN26
write_data_10_[0] => Selector15.IN27
write_data_10_[1] => Selector14.IN27
write_data_10_[2] => Selector13.IN27
write_data_10_[3] => Selector12.IN27
write_data_10_[4] => Selector11.IN27
write_data_10_[5] => Selector10.IN27
write_data_10_[6] => Selector9.IN27
write_data_10_[7] => Selector8.IN27
write_data_10_[8] => Selector7.IN27
write_data_10_[9] => Selector6.IN27
write_data_10_[10] => Selector5.IN27
write_data_10_[11] => Selector4.IN27
write_data_10_[12] => Selector3.IN27
write_data_10_[13] => Selector2.IN27
write_data_10_[14] => Selector1.IN27
write_data_10_[15] => Selector0.IN27
write_data_11_[0] => Selector15.IN28
write_data_11_[1] => Selector14.IN28
write_data_11_[2] => Selector13.IN28
write_data_11_[3] => Selector12.IN28
write_data_11_[4] => Selector11.IN28
write_data_11_[5] => Selector10.IN28
write_data_11_[6] => Selector9.IN28
write_data_11_[7] => Selector8.IN28
write_data_11_[8] => Selector7.IN28
write_data_11_[9] => Selector6.IN28
write_data_11_[10] => Selector5.IN28
write_data_11_[11] => Selector4.IN28
write_data_11_[12] => Selector3.IN28
write_data_11_[13] => Selector2.IN28
write_data_11_[14] => Selector1.IN28
write_data_11_[15] => Selector0.IN28
write_data_12_[0] => Selector15.IN29
write_data_12_[1] => Selector14.IN29
write_data_12_[2] => Selector13.IN29
write_data_12_[3] => Selector12.IN29
write_data_12_[4] => Selector11.IN29
write_data_12_[5] => Selector10.IN29
write_data_12_[6] => Selector9.IN29
write_data_12_[7] => Selector8.IN29
write_data_12_[8] => Selector7.IN29
write_data_12_[9] => Selector6.IN29
write_data_12_[10] => Selector5.IN29
write_data_12_[11] => Selector4.IN29
write_data_12_[12] => Selector3.IN29
write_data_12_[13] => Selector2.IN29
write_data_12_[14] => Selector1.IN29
write_data_12_[15] => Selector0.IN29
write_data_13_[0] => Selector15.IN30
write_data_13_[1] => Selector14.IN30
write_data_13_[2] => Selector13.IN30
write_data_13_[3] => Selector12.IN30
write_data_13_[4] => Selector11.IN30
write_data_13_[5] => Selector10.IN30
write_data_13_[6] => Selector9.IN30
write_data_13_[7] => Selector8.IN30
write_data_13_[8] => Selector7.IN30
write_data_13_[9] => Selector6.IN30
write_data_13_[10] => Selector5.IN30
write_data_13_[11] => Selector4.IN30
write_data_13_[12] => Selector3.IN30
write_data_13_[13] => Selector2.IN30
write_data_13_[14] => Selector1.IN30
write_data_13_[15] => Selector0.IN30
write_data_14_[0] => Selector15.IN31
write_data_14_[1] => Selector14.IN31
write_data_14_[2] => Selector13.IN31
write_data_14_[3] => Selector12.IN31
write_data_14_[4] => Selector11.IN31
write_data_14_[5] => Selector10.IN31
write_data_14_[6] => Selector9.IN31
write_data_14_[7] => Selector8.IN31
write_data_14_[8] => Selector7.IN31
write_data_14_[9] => Selector6.IN31
write_data_14_[10] => Selector5.IN31
write_data_14_[11] => Selector4.IN31
write_data_14_[12] => Selector3.IN31
write_data_14_[13] => Selector2.IN31
write_data_14_[14] => Selector1.IN31
write_data_14_[15] => Selector0.IN31
write_data_15_[0] => Selector15.IN32
write_data_15_[1] => Selector14.IN32
write_data_15_[2] => Selector13.IN32
write_data_15_[3] => Selector12.IN32
write_data_15_[4] => Selector11.IN32
write_data_15_[5] => Selector10.IN32
write_data_15_[6] => Selector9.IN32
write_data_15_[7] => Selector8.IN32
write_data_15_[8] => Selector7.IN32
write_data_15_[9] => Selector6.IN32
write_data_15_[10] => Selector5.IN32
write_data_15_[11] => Selector4.IN32
write_data_15_[12] => Selector3.IN32
write_data_15_[13] => Selector2.IN32
write_data_15_[14] => Selector1.IN32
write_data_15_[15] => Selector0.IN32
read_data_0_[0] <= read_data_0__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[1] <= read_data_0__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[2] <= read_data_0__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[3] <= read_data_0__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[4] <= read_data_0__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[5] <= read_data_0__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[6] <= read_data_0__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[7] <= read_data_0__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[8] <= read_data_0__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[9] <= read_data_0__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[10] <= read_data_0__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[11] <= read_data_0__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[12] <= read_data_0__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[13] <= read_data_0__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[14] <= read_data_0__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_0_[15] <= read_data_0__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[0] <= read_data_1__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[1] <= read_data_1__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[2] <= read_data_1__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[3] <= read_data_1__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[4] <= read_data_1__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[5] <= read_data_1__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[6] <= read_data_1__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[7] <= read_data_1__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[8] <= read_data_1__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[9] <= read_data_1__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[10] <= read_data_1__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[11] <= read_data_1__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[12] <= read_data_1__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[13] <= read_data_1__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[14] <= read_data_1__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_1_[15] <= read_data_1__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[0] <= read_data_2__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[1] <= read_data_2__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[2] <= read_data_2__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[3] <= read_data_2__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[4] <= read_data_2__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[5] <= read_data_2__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[6] <= read_data_2__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[7] <= read_data_2__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[8] <= read_data_2__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[9] <= read_data_2__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[10] <= read_data_2__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[11] <= read_data_2__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[12] <= read_data_2__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[13] <= read_data_2__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[14] <= read_data_2__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_2_[15] <= read_data_2__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[0] <= read_data_3__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[1] <= read_data_3__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[2] <= read_data_3__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[3] <= read_data_3__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[4] <= read_data_3__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[5] <= read_data_3__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[6] <= read_data_3__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[7] <= read_data_3__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[8] <= read_data_3__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[9] <= read_data_3__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[10] <= read_data_3__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[11] <= read_data_3__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[12] <= read_data_3__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[13] <= read_data_3__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[14] <= read_data_3__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_3_[15] <= read_data_3__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[0] <= read_data_4__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[1] <= read_data_4__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[2] <= read_data_4__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[3] <= read_data_4__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[4] <= read_data_4__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[5] <= read_data_4__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[6] <= read_data_4__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[7] <= read_data_4__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[8] <= read_data_4__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[9] <= read_data_4__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[10] <= read_data_4__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[11] <= read_data_4__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[12] <= read_data_4__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[13] <= read_data_4__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[14] <= read_data_4__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_4_[15] <= read_data_4__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[0] <= read_data_5__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[1] <= read_data_5__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[2] <= read_data_5__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[3] <= read_data_5__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[4] <= read_data_5__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[5] <= read_data_5__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[6] <= read_data_5__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[7] <= read_data_5__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[8] <= read_data_5__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[9] <= read_data_5__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[10] <= read_data_5__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[11] <= read_data_5__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[12] <= read_data_5__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[13] <= read_data_5__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[14] <= read_data_5__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_5_[15] <= read_data_5__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[0] <= read_data_6__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[1] <= read_data_6__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[2] <= read_data_6__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[3] <= read_data_6__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[4] <= read_data_6__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[5] <= read_data_6__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[6] <= read_data_6__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[7] <= read_data_6__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[8] <= read_data_6__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[9] <= read_data_6__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[10] <= read_data_6__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[11] <= read_data_6__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[12] <= read_data_6__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[13] <= read_data_6__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[14] <= read_data_6__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_6_[15] <= read_data_6__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[0] <= read_data_7__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[1] <= read_data_7__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[2] <= read_data_7__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[3] <= read_data_7__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[4] <= read_data_7__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[5] <= read_data_7__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[6] <= read_data_7__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[7] <= read_data_7__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[8] <= read_data_7__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[9] <= read_data_7__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[10] <= read_data_7__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[11] <= read_data_7__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[12] <= read_data_7__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[13] <= read_data_7__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[14] <= read_data_7__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_7_[15] <= read_data_7__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[0] <= read_data_8__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[1] <= read_data_8__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[2] <= read_data_8__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[3] <= read_data_8__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[4] <= read_data_8__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[5] <= read_data_8__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[6] <= read_data_8__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[7] <= read_data_8__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[8] <= read_data_8__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[9] <= read_data_8__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[10] <= read_data_8__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[11] <= read_data_8__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[12] <= read_data_8__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[13] <= read_data_8__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[14] <= read_data_8__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_8_[15] <= read_data_8__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[0] <= read_data_9__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[1] <= read_data_9__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[2] <= read_data_9__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[3] <= read_data_9__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[4] <= read_data_9__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[5] <= read_data_9__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[6] <= read_data_9__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[7] <= read_data_9__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[8] <= read_data_9__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[9] <= read_data_9__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[10] <= read_data_9__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[11] <= read_data_9__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[12] <= read_data_9__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[13] <= read_data_9__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[14] <= read_data_9__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_9_[15] <= read_data_9__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[0] <= read_data_10__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[1] <= read_data_10__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[2] <= read_data_10__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[3] <= read_data_10__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[4] <= read_data_10__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[5] <= read_data_10__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[6] <= read_data_10__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[7] <= read_data_10__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[8] <= read_data_10__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[9] <= read_data_10__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[10] <= read_data_10__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[11] <= read_data_10__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[12] <= read_data_10__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[13] <= read_data_10__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[14] <= read_data_10__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_10_[15] <= read_data_10__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[0] <= read_data_11__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[1] <= read_data_11__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[2] <= read_data_11__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[3] <= read_data_11__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[4] <= read_data_11__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[5] <= read_data_11__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[6] <= read_data_11__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[7] <= read_data_11__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[8] <= read_data_11__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[9] <= read_data_11__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[10] <= read_data_11__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[11] <= read_data_11__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[12] <= read_data_11__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[13] <= read_data_11__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[14] <= read_data_11__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_11_[15] <= read_data_11__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[0] <= read_data_12__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[1] <= read_data_12__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[2] <= read_data_12__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[3] <= read_data_12__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[4] <= read_data_12__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[5] <= read_data_12__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[6] <= read_data_12__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[7] <= read_data_12__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[8] <= read_data_12__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[9] <= read_data_12__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[10] <= read_data_12__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[11] <= read_data_12__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[12] <= read_data_12__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[13] <= read_data_12__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[14] <= read_data_12__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_12_[15] <= read_data_12__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[0] <= read_data_13__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[1] <= read_data_13__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[2] <= read_data_13__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[3] <= read_data_13__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[4] <= read_data_13__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[5] <= read_data_13__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[6] <= read_data_13__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[7] <= read_data_13__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[8] <= read_data_13__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[9] <= read_data_13__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[10] <= read_data_13__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[11] <= read_data_13__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[12] <= read_data_13__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[13] <= read_data_13__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[14] <= read_data_13__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_13_[15] <= read_data_13__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[0] <= read_data_14__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[1] <= read_data_14__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[2] <= read_data_14__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[3] <= read_data_14__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[4] <= read_data_14__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[5] <= read_data_14__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[6] <= read_data_14__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[7] <= read_data_14__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[8] <= read_data_14__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[9] <= read_data_14__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[10] <= read_data_14__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[11] <= read_data_14__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[12] <= read_data_14__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[13] <= read_data_14__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[14] <= read_data_14__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_14_[15] <= read_data_14__reg[15].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[0] <= read_data_15__reg[0].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[1] <= read_data_15__reg[1].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[2] <= read_data_15__reg[2].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[3] <= read_data_15__reg[3].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[4] <= read_data_15__reg[4].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[5] <= read_data_15__reg[5].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[6] <= read_data_15__reg[6].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[7] <= read_data_15__reg[7].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[8] <= read_data_15__reg[8].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[9] <= read_data_15__reg[9].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[10] <= read_data_15__reg[10].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[11] <= read_data_15__reg[11].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[12] <= read_data_15__reg[12].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[13] <= read_data_15__reg[13].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[14] <= read_data_15__reg[14].DB_MAX_OUTPUT_PORT_TYPE
read_data_15_[15] <= read_data_15__reg[15].DB_MAX_OUTPUT_PORT_TYPE
addr[0] <= addr[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[1] <= addr[1]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[2] <= addr[2]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[3] <= addr[3]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[4] <= addr[4]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[5] <= addr[5]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[6] <= addr[6]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[7] <= addr[7]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[8] <= addr[8]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[9] <= addr[9]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[10] <= addr[10]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[11] <= addr[11]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[12] <= addr[12]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[13] <= addr[13]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[14] <= addr[14]$latch.DB_MAX_OUTPUT_PORT_TYPE
addr[15] <= addr[15]$latch.DB_MAX_OUTPUT_PORT_TYPE
fmc_wr_en <= fmc_wr_en.DB_MAX_OUTPUT_PORT_TYPE
fmc_rd_en <= fmc_rd_en.DB_MAX_OUTPUT_PORT_TYPE


|TOP|AD_FREQ_MEASURE:inst21
CS => ~NO_FANOUT~
RD => ~NO_FANOUT~
AD1_FREQ_DATA[0] => AD1_FREQ_DATA_L[0]~reg0.DATAIN
AD1_FREQ_DATA[1] => AD1_FREQ_DATA_L[1]~reg0.DATAIN
AD1_FREQ_DATA[2] => AD1_FREQ_DATA_L[2]~reg0.DATAIN
AD1_FREQ_DATA[3] => AD1_FREQ_DATA_L[3]~reg0.DATAIN
AD1_FREQ_DATA[4] => AD1_FREQ_DATA_L[4]~reg0.DATAIN
AD1_FREQ_DATA[5] => AD1_FREQ_DATA_L[5]~reg0.DATAIN
AD1_FREQ_DATA[6] => AD1_FREQ_DATA_L[6]~reg0.DATAIN
AD1_FREQ_DATA[7] => AD1_FREQ_DATA_L[7]~reg0.DATAIN
AD1_FREQ_DATA[8] => AD1_FREQ_DATA_L[8]~reg0.DATAIN
AD1_FREQ_DATA[9] => AD1_FREQ_DATA_L[9]~reg0.DATAIN
AD1_FREQ_DATA[10] => AD1_FREQ_DATA_L[10]~reg0.DATAIN
AD1_FREQ_DATA[11] => AD1_FREQ_DATA_L[11]~reg0.DATAIN
AD1_FREQ_DATA[12] => AD1_FREQ_DATA_L[12]~reg0.DATAIN
AD1_FREQ_DATA[13] => AD1_FREQ_DATA_L[13]~reg0.DATAIN
AD1_FREQ_DATA[14] => AD1_FREQ_DATA_L[14]~reg0.DATAIN
AD1_FREQ_DATA[15] => AD1_FREQ_DATA_L[15]~reg0.DATAIN
AD1_FREQ_DATA[16] => AD1_FREQ_DATA_H[0]~reg0.DATAIN
AD1_FREQ_DATA[17] => AD1_FREQ_DATA_H[1]~reg0.DATAIN
AD1_FREQ_DATA[18] => AD1_FREQ_DATA_H[2]~reg0.DATAIN
AD1_FREQ_DATA[19] => AD1_FREQ_DATA_H[3]~reg0.DATAIN
AD1_FREQ_DATA[20] => AD1_FREQ_DATA_H[4]~reg0.DATAIN
AD1_FREQ_DATA[21] => AD1_FREQ_DATA_H[5]~reg0.DATAIN
AD1_FREQ_DATA[22] => AD1_FREQ_DATA_H[6]~reg0.DATAIN
AD1_FREQ_DATA[23] => AD1_FREQ_DATA_H[7]~reg0.DATAIN
AD1_FREQ_DATA[24] => AD1_FREQ_DATA_H[8]~reg0.DATAIN
AD1_FREQ_DATA[25] => AD1_FREQ_DATA_H[9]~reg0.DATAIN
AD1_FREQ_DATA[26] => AD1_FREQ_DATA_H[10]~reg0.DATAIN
AD1_FREQ_DATA[27] => AD1_FREQ_DATA_H[11]~reg0.DATAIN
AD1_FREQ_DATA[28] => AD1_FREQ_DATA_H[12]~reg0.DATAIN
AD1_FREQ_DATA[29] => AD1_FREQ_DATA_H[13]~reg0.DATAIN
AD1_FREQ_DATA[30] => AD1_FREQ_DATA_H[14]~reg0.DATAIN
AD1_FREQ_DATA[31] => AD1_FREQ_DATA_H[15]~reg0.DATAIN
AD2_FREQ_DATA[0] => AD2_FREQ_DATA_L[0]~reg0.DATAIN
AD2_FREQ_DATA[1] => AD2_FREQ_DATA_L[1]~reg0.DATAIN
AD2_FREQ_DATA[2] => AD2_FREQ_DATA_L[2]~reg0.DATAIN
AD2_FREQ_DATA[3] => AD2_FREQ_DATA_L[3]~reg0.DATAIN
AD2_FREQ_DATA[4] => AD2_FREQ_DATA_L[4]~reg0.DATAIN
AD2_FREQ_DATA[5] => AD2_FREQ_DATA_L[5]~reg0.DATAIN
AD2_FREQ_DATA[6] => AD2_FREQ_DATA_L[6]~reg0.DATAIN
AD2_FREQ_DATA[7] => AD2_FREQ_DATA_L[7]~reg0.DATAIN
AD2_FREQ_DATA[8] => AD2_FREQ_DATA_L[8]~reg0.DATAIN
AD2_FREQ_DATA[9] => AD2_FREQ_DATA_L[9]~reg0.DATAIN
AD2_FREQ_DATA[10] => AD2_FREQ_DATA_L[10]~reg0.DATAIN
AD2_FREQ_DATA[11] => AD2_FREQ_DATA_L[11]~reg0.DATAIN
AD2_FREQ_DATA[12] => AD2_FREQ_DATA_L[12]~reg0.DATAIN
AD2_FREQ_DATA[13] => AD2_FREQ_DATA_L[13]~reg0.DATAIN
AD2_FREQ_DATA[14] => AD2_FREQ_DATA_L[14]~reg0.DATAIN
AD2_FREQ_DATA[15] => AD2_FREQ_DATA_L[15]~reg0.DATAIN
AD2_FREQ_DATA[16] => AD2_FREQ_DATA_H[0]~reg0.DATAIN
AD2_FREQ_DATA[17] => AD2_FREQ_DATA_H[1]~reg0.DATAIN
AD2_FREQ_DATA[18] => AD2_FREQ_DATA_H[2]~reg0.DATAIN
AD2_FREQ_DATA[19] => AD2_FREQ_DATA_H[3]~reg0.DATAIN
AD2_FREQ_DATA[20] => AD2_FREQ_DATA_H[4]~reg0.DATAIN
AD2_FREQ_DATA[21] => AD2_FREQ_DATA_H[5]~reg0.DATAIN
AD2_FREQ_DATA[22] => AD2_FREQ_DATA_H[6]~reg0.DATAIN
AD2_FREQ_DATA[23] => AD2_FREQ_DATA_H[7]~reg0.DATAIN
AD2_FREQ_DATA[24] => AD2_FREQ_DATA_H[8]~reg0.DATAIN
AD2_FREQ_DATA[25] => AD2_FREQ_DATA_H[9]~reg0.DATAIN
AD2_FREQ_DATA[26] => AD2_FREQ_DATA_H[10]~reg0.DATAIN
AD2_FREQ_DATA[27] => AD2_FREQ_DATA_H[11]~reg0.DATAIN
AD2_FREQ_DATA[28] => AD2_FREQ_DATA_H[12]~reg0.DATAIN
AD2_FREQ_DATA[29] => AD2_FREQ_DATA_H[13]~reg0.DATAIN
AD2_FREQ_DATA[30] => AD2_FREQ_DATA_H[14]~reg0.DATAIN
AD2_FREQ_DATA[31] => AD2_FREQ_DATA_H[15]~reg0.DATAIN
BASE1_FREQ_DATA[0] => BASE1_FREQ_DATA_L[0]~reg0.DATAIN
BASE1_FREQ_DATA[1] => BASE1_FREQ_DATA_L[1]~reg0.DATAIN
BASE1_FREQ_DATA[2] => BASE1_FREQ_DATA_L[2]~reg0.DATAIN
BASE1_FREQ_DATA[3] => BASE1_FREQ_DATA_L[3]~reg0.DATAIN
BASE1_FREQ_DATA[4] => BASE1_FREQ_DATA_L[4]~reg0.DATAIN
BASE1_FREQ_DATA[5] => BASE1_FREQ_DATA_L[5]~reg0.DATAIN
BASE1_FREQ_DATA[6] => BASE1_FREQ_DATA_L[6]~reg0.DATAIN
BASE1_FREQ_DATA[7] => BASE1_FREQ_DATA_L[7]~reg0.DATAIN
BASE1_FREQ_DATA[8] => BASE1_FREQ_DATA_L[8]~reg0.DATAIN
BASE1_FREQ_DATA[9] => BASE1_FREQ_DATA_L[9]~reg0.DATAIN
BASE1_FREQ_DATA[10] => BASE1_FREQ_DATA_L[10]~reg0.DATAIN
BASE1_FREQ_DATA[11] => BASE1_FREQ_DATA_L[11]~reg0.DATAIN
BASE1_FREQ_DATA[12] => BASE1_FREQ_DATA_L[12]~reg0.DATAIN
BASE1_FREQ_DATA[13] => BASE1_FREQ_DATA_L[13]~reg0.DATAIN
BASE1_FREQ_DATA[14] => BASE1_FREQ_DATA_L[14]~reg0.DATAIN
BASE1_FREQ_DATA[15] => BASE1_FREQ_DATA_L[15]~reg0.DATAIN
BASE1_FREQ_DATA[16] => BASE1_FREQ_DATA_H[0]~reg0.DATAIN
BASE1_FREQ_DATA[17] => BASE1_FREQ_DATA_H[1]~reg0.DATAIN
BASE1_FREQ_DATA[18] => BASE1_FREQ_DATA_H[2]~reg0.DATAIN
BASE1_FREQ_DATA[19] => BASE1_FREQ_DATA_H[3]~reg0.DATAIN
BASE1_FREQ_DATA[20] => BASE1_FREQ_DATA_H[4]~reg0.DATAIN
BASE1_FREQ_DATA[21] => BASE1_FREQ_DATA_H[5]~reg0.DATAIN
BASE1_FREQ_DATA[22] => BASE1_FREQ_DATA_H[6]~reg0.DATAIN
BASE1_FREQ_DATA[23] => BASE1_FREQ_DATA_H[7]~reg0.DATAIN
BASE1_FREQ_DATA[24] => BASE1_FREQ_DATA_H[8]~reg0.DATAIN
BASE1_FREQ_DATA[25] => BASE1_FREQ_DATA_H[9]~reg0.DATAIN
BASE1_FREQ_DATA[26] => BASE1_FREQ_DATA_H[10]~reg0.DATAIN
BASE1_FREQ_DATA[27] => BASE1_FREQ_DATA_H[11]~reg0.DATAIN
BASE1_FREQ_DATA[28] => BASE1_FREQ_DATA_H[12]~reg0.DATAIN
BASE1_FREQ_DATA[29] => BASE1_FREQ_DATA_H[13]~reg0.DATAIN
BASE1_FREQ_DATA[30] => BASE1_FREQ_DATA_H[14]~reg0.DATAIN
BASE1_FREQ_DATA[31] => BASE1_FREQ_DATA_H[15]~reg0.DATAIN
BASE2_FREQ_DATA[0] => BASE2_FREQ_DATA_L[0]~reg0.DATAIN
BASE2_FREQ_DATA[1] => BASE2_FREQ_DATA_L[1]~reg0.DATAIN
BASE2_FREQ_DATA[2] => BASE2_FREQ_DATA_L[2]~reg0.DATAIN
BASE2_FREQ_DATA[3] => BASE2_FREQ_DATA_L[3]~reg0.DATAIN
BASE2_FREQ_DATA[4] => BASE2_FREQ_DATA_L[4]~reg0.DATAIN
BASE2_FREQ_DATA[5] => BASE2_FREQ_DATA_L[5]~reg0.DATAIN
BASE2_FREQ_DATA[6] => BASE2_FREQ_DATA_L[6]~reg0.DATAIN
BASE2_FREQ_DATA[7] => BASE2_FREQ_DATA_L[7]~reg0.DATAIN
BASE2_FREQ_DATA[8] => BASE2_FREQ_DATA_L[8]~reg0.DATAIN
BASE2_FREQ_DATA[9] => BASE2_FREQ_DATA_L[9]~reg0.DATAIN
BASE2_FREQ_DATA[10] => BASE2_FREQ_DATA_L[10]~reg0.DATAIN
BASE2_FREQ_DATA[11] => BASE2_FREQ_DATA_L[11]~reg0.DATAIN
BASE2_FREQ_DATA[12] => BASE2_FREQ_DATA_L[12]~reg0.DATAIN
BASE2_FREQ_DATA[13] => BASE2_FREQ_DATA_L[13]~reg0.DATAIN
BASE2_FREQ_DATA[14] => BASE2_FREQ_DATA_L[14]~reg0.DATAIN
BASE2_FREQ_DATA[15] => BASE2_FREQ_DATA_L[15]~reg0.DATAIN
BASE2_FREQ_DATA[16] => BASE2_FREQ_DATA_H[0]~reg0.DATAIN
BASE2_FREQ_DATA[17] => BASE2_FREQ_DATA_H[1]~reg0.DATAIN
BASE2_FREQ_DATA[18] => BASE2_FREQ_DATA_H[2]~reg0.DATAIN
BASE2_FREQ_DATA[19] => BASE2_FREQ_DATA_H[3]~reg0.DATAIN
BASE2_FREQ_DATA[20] => BASE2_FREQ_DATA_H[4]~reg0.DATAIN
BASE2_FREQ_DATA[21] => BASE2_FREQ_DATA_H[5]~reg0.DATAIN
BASE2_FREQ_DATA[22] => BASE2_FREQ_DATA_H[6]~reg0.DATAIN
BASE2_FREQ_DATA[23] => BASE2_FREQ_DATA_H[7]~reg0.DATAIN
BASE2_FREQ_DATA[24] => BASE2_FREQ_DATA_H[8]~reg0.DATAIN
BASE2_FREQ_DATA[25] => BASE2_FREQ_DATA_H[9]~reg0.DATAIN
BASE2_FREQ_DATA[26] => BASE2_FREQ_DATA_H[10]~reg0.DATAIN
BASE2_FREQ_DATA[27] => BASE2_FREQ_DATA_H[11]~reg0.DATAIN
BASE2_FREQ_DATA[28] => BASE2_FREQ_DATA_H[12]~reg0.DATAIN
BASE2_FREQ_DATA[29] => BASE2_FREQ_DATA_H[13]~reg0.DATAIN
BASE2_FREQ_DATA[30] => BASE2_FREQ_DATA_H[14]~reg0.DATAIN
BASE2_FREQ_DATA[31] => BASE2_FREQ_DATA_H[15]~reg0.DATAIN
ADDR[0] => ~NO_FANOUT~
ADDR[1] => ~NO_FANOUT~
ADDR[2] => ~NO_FANOUT~
ADDR[3] => ~NO_FANOUT~
ADDR[4] => ~NO_FANOUT~
ADDR[5] => ~NO_FANOUT~
ADDR[6] => ~NO_FANOUT~
ADDR[7] => ~NO_FANOUT~
ADDR[8] => ~NO_FANOUT~
ADDR[9] => ~NO_FANOUT~
ADDR[10] => ~NO_FANOUT~
ADDR[11] => ~NO_FANOUT~
ADDR[12] => ~NO_FANOUT~
ADDR[13] => ~NO_FANOUT~
ADDR[14] => ~NO_FANOUT~
ADDR[15] => ~NO_FANOUT~
ad1end => AD1_FREQ_DATA_L[0]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[1]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[2]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[3]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[4]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[5]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[6]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[7]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[8]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[9]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[10]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[11]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[12]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[13]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[14]~reg0.CLK
ad1end => AD1_FREQ_DATA_L[15]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[0]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[1]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[2]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[3]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[4]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[5]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[6]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[7]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[8]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[9]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[10]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[11]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[12]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[13]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[14]~reg0.CLK
ad1end => AD1_FREQ_DATA_H[15]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[0]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[1]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[2]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[3]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[4]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[5]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[6]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[7]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[8]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[9]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[10]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[11]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[12]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[13]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[14]~reg0.CLK
ad1end => BASE1_FREQ_DATA_L[15]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[0]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[1]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[2]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[3]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[4]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[5]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[6]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[7]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[8]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[9]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[10]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[11]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[12]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[13]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[14]~reg0.CLK
ad1end => BASE1_FREQ_DATA_H[15]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[0]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[1]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[2]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[3]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[4]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[5]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[6]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[7]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[8]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[9]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[10]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[11]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[12]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[13]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[14]~reg0.CLK
ad2end => AD2_FREQ_DATA_L[15]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[0]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[1]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[2]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[3]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[4]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[5]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[6]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[7]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[8]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[9]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[10]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[11]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[12]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[13]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[14]~reg0.CLK
ad2end => AD2_FREQ_DATA_H[15]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[0]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[1]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[2]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[3]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[4]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[5]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[6]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[7]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[8]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[9]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[10]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[11]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[12]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[13]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[14]~reg0.CLK
ad2end => BASE2_FREQ_DATA_L[15]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[0]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[1]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[2]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[3]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[4]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[5]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[6]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[7]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[8]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[9]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[10]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[11]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[12]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[13]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[14]~reg0.CLK
ad2end => BASE2_FREQ_DATA_H[15]~reg0.CLK
AD1_FREQ_DATA_H[0] <= AD1_FREQ_DATA_H[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[1] <= AD1_FREQ_DATA_H[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[2] <= AD1_FREQ_DATA_H[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[3] <= AD1_FREQ_DATA_H[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[4] <= AD1_FREQ_DATA_H[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[5] <= AD1_FREQ_DATA_H[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[6] <= AD1_FREQ_DATA_H[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[7] <= AD1_FREQ_DATA_H[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[8] <= AD1_FREQ_DATA_H[8]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[9] <= AD1_FREQ_DATA_H[9]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[10] <= AD1_FREQ_DATA_H[10]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[11] <= AD1_FREQ_DATA_H[11]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[12] <= AD1_FREQ_DATA_H[12]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[13] <= AD1_FREQ_DATA_H[13]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[14] <= AD1_FREQ_DATA_H[14]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_H[15] <= AD1_FREQ_DATA_H[15]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[0] <= AD1_FREQ_DATA_L[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[1] <= AD1_FREQ_DATA_L[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[2] <= AD1_FREQ_DATA_L[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[3] <= AD1_FREQ_DATA_L[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[4] <= AD1_FREQ_DATA_L[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[5] <= AD1_FREQ_DATA_L[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[6] <= AD1_FREQ_DATA_L[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[7] <= AD1_FREQ_DATA_L[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[8] <= AD1_FREQ_DATA_L[8]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[9] <= AD1_FREQ_DATA_L[9]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[10] <= AD1_FREQ_DATA_L[10]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[11] <= AD1_FREQ_DATA_L[11]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[12] <= AD1_FREQ_DATA_L[12]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[13] <= AD1_FREQ_DATA_L[13]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[14] <= AD1_FREQ_DATA_L[14]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD1_FREQ_DATA_L[15] <= AD1_FREQ_DATA_L[15]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[0] <= AD2_FREQ_DATA_H[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[1] <= AD2_FREQ_DATA_H[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[2] <= AD2_FREQ_DATA_H[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[3] <= AD2_FREQ_DATA_H[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[4] <= AD2_FREQ_DATA_H[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[5] <= AD2_FREQ_DATA_H[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[6] <= AD2_FREQ_DATA_H[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[7] <= AD2_FREQ_DATA_H[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[8] <= AD2_FREQ_DATA_H[8]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[9] <= AD2_FREQ_DATA_H[9]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[10] <= AD2_FREQ_DATA_H[10]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[11] <= AD2_FREQ_DATA_H[11]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[12] <= AD2_FREQ_DATA_H[12]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[13] <= AD2_FREQ_DATA_H[13]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[14] <= AD2_FREQ_DATA_H[14]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_H[15] <= AD2_FREQ_DATA_H[15]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[0] <= AD2_FREQ_DATA_L[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[1] <= AD2_FREQ_DATA_L[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[2] <= AD2_FREQ_DATA_L[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[3] <= AD2_FREQ_DATA_L[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[4] <= AD2_FREQ_DATA_L[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[5] <= AD2_FREQ_DATA_L[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[6] <= AD2_FREQ_DATA_L[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[7] <= AD2_FREQ_DATA_L[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[8] <= AD2_FREQ_DATA_L[8]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[9] <= AD2_FREQ_DATA_L[9]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[10] <= AD2_FREQ_DATA_L[10]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[11] <= AD2_FREQ_DATA_L[11]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[12] <= AD2_FREQ_DATA_L[12]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[13] <= AD2_FREQ_DATA_L[13]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[14] <= AD2_FREQ_DATA_L[14]~reg0.DB_MAX_OUTPUT_PORT_TYPE
AD2_FREQ_DATA_L[15] <= AD2_FREQ_DATA_L[15]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[0] <= BASE1_FREQ_DATA_H[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[1] <= BASE1_FREQ_DATA_H[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[2] <= BASE1_FREQ_DATA_H[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[3] <= BASE1_FREQ_DATA_H[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[4] <= BASE1_FREQ_DATA_H[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[5] <= BASE1_FREQ_DATA_H[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[6] <= BASE1_FREQ_DATA_H[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[7] <= BASE1_FREQ_DATA_H[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[8] <= BASE1_FREQ_DATA_H[8]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[9] <= BASE1_FREQ_DATA_H[9]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[10] <= BASE1_FREQ_DATA_H[10]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[11] <= BASE1_FREQ_DATA_H[11]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[12] <= BASE1_FREQ_DATA_H[12]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[13] <= BASE1_FREQ_DATA_H[13]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[14] <= BASE1_FREQ_DATA_H[14]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_H[15] <= BASE1_FREQ_DATA_H[15]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[0] <= BASE1_FREQ_DATA_L[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[1] <= BASE1_FREQ_DATA_L[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[2] <= BASE1_FREQ_DATA_L[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[3] <= BASE1_FREQ_DATA_L[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[4] <= BASE1_FREQ_DATA_L[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[5] <= BASE1_FREQ_DATA_L[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[6] <= BASE1_FREQ_DATA_L[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[7] <= BASE1_FREQ_DATA_L[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[8] <= BASE1_FREQ_DATA_L[8]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[9] <= BASE1_FREQ_DATA_L[9]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[10] <= BASE1_FREQ_DATA_L[10]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[11] <= BASE1_FREQ_DATA_L[11]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[12] <= BASE1_FREQ_DATA_L[12]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[13] <= BASE1_FREQ_DATA_L[13]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[14] <= BASE1_FREQ_DATA_L[14]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE1_FREQ_DATA_L[15] <= BASE1_FREQ_DATA_L[15]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[0] <= BASE2_FREQ_DATA_H[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[1] <= BASE2_FREQ_DATA_H[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[2] <= BASE2_FREQ_DATA_H[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[3] <= BASE2_FREQ_DATA_H[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[4] <= BASE2_FREQ_DATA_H[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[5] <= BASE2_FREQ_DATA_H[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[6] <= BASE2_FREQ_DATA_H[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[7] <= BASE2_FREQ_DATA_H[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[8] <= BASE2_FREQ_DATA_H[8]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[9] <= BASE2_FREQ_DATA_H[9]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[10] <= BASE2_FREQ_DATA_H[10]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[11] <= BASE2_FREQ_DATA_H[11]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[12] <= BASE2_FREQ_DATA_H[12]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[13] <= BASE2_FREQ_DATA_H[13]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[14] <= BASE2_FREQ_DATA_H[14]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_H[15] <= BASE2_FREQ_DATA_H[15]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[0] <= BASE2_FREQ_DATA_L[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[1] <= BASE2_FREQ_DATA_L[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[2] <= BASE2_FREQ_DATA_L[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[3] <= BASE2_FREQ_DATA_L[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[4] <= BASE2_FREQ_DATA_L[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[5] <= BASE2_FREQ_DATA_L[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[6] <= BASE2_FREQ_DATA_L[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[7] <= BASE2_FREQ_DATA_L[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[8] <= BASE2_FREQ_DATA_L[8]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[9] <= BASE2_FREQ_DATA_L[9]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[10] <= BASE2_FREQ_DATA_L[10]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[11] <= BASE2_FREQ_DATA_L[11]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[12] <= BASE2_FREQ_DATA_L[12]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[13] <= BASE2_FREQ_DATA_L[13]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[14] <= BASE2_FREQ_DATA_L[14]~reg0.DB_MAX_OUTPUT_PORT_TYPE
BASE2_FREQ_DATA_L[15] <= BASE2_FREQ_DATA_L[15]~reg0.DB_MAX_OUTPUT_PORT_TYPE


|TOP|gate_generator:inst22
en => en_sync0.DATAIN
da_clk => gate~reg0.CLK
da_clk => gate_da1.CLK
s_clk => gate_da0.CLK
s_clk => counter[0].CLK
s_clk => counter[1].CLK
s_clk => counter[2].CLK
s_clk => counter[3].CLK
s_clk => counter[4].CLK
s_clk => counter[5].CLK
s_clk => counter[6].CLK
s_clk => counter[7].CLK
s_clk => counter[8].CLK
s_clk => counter[9].CLK
s_clk => counter[10].CLK
s_clk => counter[11].CLK
s_clk => counter[12].CLK
s_clk => counter[13].CLK
s_clk => counter[14].CLK
s_clk => counter[15].CLK
s_clk => counter[16].CLK
s_clk => counter[17].CLK
s_clk => counter[18].CLK
s_clk => counter[19].CLK
s_clk => counter[20].CLK
s_clk => counter[21].CLK
s_clk => counter[22].CLK
s_clk => counter[23].CLK
s_clk => counter[24].CLK
s_clk => counter[25].CLK
s_clk => counter[26].CLK
s_clk => counter[27].CLK
s_clk => en_sync1.CLK
s_clk => en_sync0.CLK
gate <= gate~reg0.DB_MAX_OUTPUT_PORT_TYPE


|TOP|gate_generator:inst23
en => en_sync0.DATAIN
da_clk => gate~reg0.CLK
da_clk => gate_da1.CLK
s_clk => gate_da0.CLK
s_clk => counter[0].CLK
s_clk => counter[1].CLK
s_clk => counter[2].CLK
s_clk => counter[3].CLK
s_clk => counter[4].CLK
s_clk => counter[5].CLK
s_clk => counter[6].CLK
s_clk => counter[7].CLK
s_clk => counter[8].CLK
s_clk => counter[9].CLK
s_clk => counter[10].CLK
s_clk => counter[11].CLK
s_clk => counter[12].CLK
s_clk => counter[13].CLK
s_clk => counter[14].CLK
s_clk => counter[15].CLK
s_clk => counter[16].CLK
s_clk => counter[17].CLK
s_clk => counter[18].CLK
s_clk => counter[19].CLK
s_clk => counter[20].CLK
s_clk => counter[21].CLK
s_clk => counter[22].CLK
s_clk => counter[23].CLK
s_clk => counter[24].CLK
s_clk => counter[25].CLK
s_clk => counter[26].CLK
s_clk => counter[27].CLK
s_clk => en_sync1.CLK
s_clk => en_sync0.CLK
gate <= gate~reg0.DB_MAX_OUTPUT_PORT_TYPE


|TOP|CNT32:u_AD1_CNT32
CLR => Q1[0].ACLR
CLR => Q1[1].ACLR
CLR => Q1[2].ACLR
CLR => Q1[3].ACLR
CLR => Q1[4].ACLR
CLR => Q1[5].ACLR
CLR => Q1[6].ACLR
CLR => Q1[7].ACLR
CLR => Q1[8].ACLR
CLR => Q1[9].ACLR
CLR => Q1[10].ACLR
CLR => Q1[11].ACLR
CLR => Q1[12].ACLR
CLR => Q1[13].ACLR
CLR => Q1[14].ACLR
CLR => Q1[15].ACLR
CLR => Q1[16].ACLR
CLR => Q1[17].ACLR
CLR => Q1[18].ACLR
CLR => Q1[19].ACLR
CLR => Q1[20].ACLR
CLR => Q1[21].ACLR
CLR => Q1[22].ACLR
CLR => Q1[23].ACLR
CLR => Q1[24].ACLR
CLR => Q1[25].ACLR
CLR => Q1[26].ACLR
CLR => Q1[27].ACLR
CLR => Q1[28].ACLR
CLR => Q1[29].ACLR
CLR => Q1[30].ACLR
CLR => Q1[31].ACLR
CLR => Q1BASE[0].ACLR
CLR => Q1BASE[1].ACLR
CLR => Q1BASE[2].ACLR
CLR => Q1BASE[3].ACLR
CLR => Q1BASE[4].ACLR
CLR => Q1BASE[5].ACLR
CLR => Q1BASE[6].ACLR
CLR => Q1BASE[7].ACLR
CLR => Q1BASE[8].ACLR
CLR => Q1BASE[9].ACLR
CLR => Q1BASE[10].ACLR
CLR => Q1BASE[11].ACLR
CLR => Q1BASE[12].ACLR
CLR => Q1BASE[13].ACLR
CLR => Q1BASE[14].ACLR
CLR => Q1BASE[15].ACLR
CLR => Q1BASE[16].ACLR
CLR => Q1BASE[17].ACLR
CLR => Q1BASE[18].ACLR
CLR => Q1BASE[19].ACLR
CLR => Q1BASE[20].ACLR
CLR => Q1BASE[21].ACLR
CLR => Q1BASE[22].ACLR
CLR => Q1BASE[23].ACLR
CLR => Q1BASE[24].ACLR
CLR => Q1BASE[25].ACLR
CLR => Q1BASE[26].ACLR
CLR => Q1BASE[27].ACLR
CLR => Q1BASE[28].ACLR
CLR => Q1BASE[29].ACLR
CLR => Q1BASE[30].ACLR
CLR => Q1BASE[31].ACLR
CLK => Q1[0].CLK
CLK => Q1[1].CLK
CLK => Q1[2].CLK
CLK => Q1[3].CLK
CLK => Q1[4].CLK
CLK => Q1[5].CLK
CLK => Q1[6].CLK
CLK => Q1[7].CLK
CLK => Q1[8].CLK
CLK => Q1[9].CLK
CLK => Q1[10].CLK
CLK => Q1[11].CLK
CLK => Q1[12].CLK
CLK => Q1[13].CLK
CLK => Q1[14].CLK
CLK => Q1[15].CLK
CLK => Q1[16].CLK
CLK => Q1[17].CLK
CLK => Q1[18].CLK
CLK => Q1[19].CLK
CLK => Q1[20].CLK
CLK => Q1[21].CLK
CLK => Q1[22].CLK
CLK => Q1[23].CLK
CLK => Q1[24].CLK
CLK => Q1[25].CLK
CLK => Q1[26].CLK
CLK => Q1[27].CLK
CLK => Q1[28].CLK
CLK => Q1[29].CLK
CLK => Q1[30].CLK
CLK => Q1[31].CLK
CLKBASE => Q1BASE[0].CLK
CLKBASE => Q1BASE[1].CLK
CLKBASE => Q1BASE[2].CLK
CLKBASE => Q1BASE[3].CLK
CLKBASE => Q1BASE[4].CLK
CLKBASE => Q1BASE[5].CLK
CLKBASE => Q1BASE[6].CLK
CLKBASE => Q1BASE[7].CLK
CLKBASE => Q1BASE[8].CLK
CLKBASE => Q1BASE[9].CLK
CLKBASE => Q1BASE[10].CLK
CLKBASE => Q1BASE[11].CLK
CLKBASE => Q1BASE[12].CLK
CLKBASE => Q1BASE[13].CLK
CLKBASE => Q1BASE[14].CLK
CLKBASE => Q1BASE[15].CLK
CLKBASE => Q1BASE[16].CLK
CLKBASE => Q1BASE[17].CLK
CLKBASE => Q1BASE[18].CLK
CLKBASE => Q1BASE[19].CLK
CLKBASE => Q1BASE[20].CLK
CLKBASE => Q1BASE[21].CLK
CLKBASE => Q1BASE[22].CLK
CLKBASE => Q1BASE[23].CLK
CLKBASE => Q1BASE[24].CLK
CLKBASE => Q1BASE[25].CLK
CLKBASE => Q1BASE[26].CLK
CLKBASE => Q1BASE[27].CLK
CLKBASE => Q1BASE[28].CLK
CLKBASE => Q1BASE[29].CLK
CLKBASE => Q1BASE[30].CLK
CLKBASE => Q1BASE[31].CLK
CLKEN => Q1[31].ENA
CLKEN => Q1[30].ENA
CLKEN => Q1[29].ENA
CLKEN => Q1[28].ENA
CLKEN => Q1[27].ENA
CLKEN => Q1[26].ENA
CLKEN => Q1[25].ENA
CLKEN => Q1[24].ENA
CLKEN => Q1[23].ENA
CLKEN => Q1[22].ENA
CLKEN => Q1[21].ENA
CLKEN => Q1[20].ENA
CLKEN => Q1[19].ENA
CLKEN => Q1[18].ENA
CLKEN => Q1[17].ENA
CLKEN => Q1[16].ENA
CLKEN => Q1[15].ENA
CLKEN => Q1[14].ENA
CLKEN => Q1[13].ENA
CLKEN => Q1[12].ENA
CLKEN => Q1[11].ENA
CLKEN => Q1[10].ENA
CLKEN => Q1[9].ENA
CLKEN => Q1[8].ENA
CLKEN => Q1[7].ENA
CLKEN => Q1[6].ENA
CLKEN => Q1[5].ENA
CLKEN => Q1[4].ENA
CLKEN => Q1[3].ENA
CLKEN => Q1[2].ENA
CLKEN => Q1[1].ENA
CLKEN => Q1[0].ENA
CLKBASEEN => Q1BASE[0].ENA
CLKBASEEN => Q1BASE[31].ENA
CLKBASEEN => Q1BASE[30].ENA
CLKBASEEN => Q1BASE[29].ENA
CLKBASEEN => Q1BASE[28].ENA
CLKBASEEN => Q1BASE[27].ENA
CLKBASEEN => Q1BASE[26].ENA
CLKBASEEN => Q1BASE[25].ENA
CLKBASEEN => Q1BASE[24].ENA
CLKBASEEN => Q1BASE[23].ENA
CLKBASEEN => Q1BASE[22].ENA
CLKBASEEN => Q1BASE[21].ENA
CLKBASEEN => Q1BASE[20].ENA
CLKBASEEN => Q1BASE[19].ENA
CLKBASEEN => Q1BASE[18].ENA
CLKBASEEN => Q1BASE[17].ENA
CLKBASEEN => Q1BASE[16].ENA
CLKBASEEN => Q1BASE[15].ENA
CLKBASEEN => Q1BASE[14].ENA
CLKBASEEN => Q1BASE[13].ENA
CLKBASEEN => Q1BASE[12].ENA
CLKBASEEN => Q1BASE[11].ENA
CLKBASEEN => Q1BASE[10].ENA
CLKBASEEN => Q1BASE[9].ENA
CLKBASEEN => Q1BASE[8].ENA
CLKBASEEN => Q1BASE[7].ENA
CLKBASEEN => Q1BASE[6].ENA
CLKBASEEN => Q1BASE[5].ENA
CLKBASEEN => Q1BASE[4].ENA
CLKBASEEN => Q1BASE[3].ENA
CLKBASEEN => Q1BASE[2].ENA
CLKBASEEN => Q1BASE[1].ENA
Q[0] <= Q1[0].DB_MAX_OUTPUT_PORT_TYPE
Q[1] <= Q1[1].DB_MAX_OUTPUT_PORT_TYPE
Q[2] <= Q1[2].DB_MAX_OUTPUT_PORT_TYPE
Q[3] <= Q1[3].DB_MAX_OUTPUT_PORT_TYPE
Q[4] <= Q1[4].DB_MAX_OUTPUT_PORT_TYPE
Q[5] <= Q1[5].DB_MAX_OUTPUT_PORT_TYPE
Q[6] <= Q1[6].DB_MAX_OUTPUT_PORT_TYPE
Q[7] <= Q1[7].DB_MAX_OUTPUT_PORT_TYPE
Q[8] <= Q1[8].DB_MAX_OUTPUT_PORT_TYPE
Q[9] <= Q1[9].DB_MAX_OUTPUT_PORT_TYPE
Q[10] <= Q1[10].DB_MAX_OUTPUT_PORT_TYPE
Q[11] <= Q1[11].DB_MAX_OUTPUT_PORT_TYPE
Q[12] <= Q1[12].DB_MAX_OUTPUT_PORT_TYPE
Q[13] <= Q1[13].DB_MAX_OUTPUT_PORT_TYPE
Q[14] <= Q1[14].DB_MAX_OUTPUT_PORT_TYPE
Q[15] <= Q1[15].DB_MAX_OUTPUT_PORT_TYPE
Q[16] <= Q1[16].DB_MAX_OUTPUT_PORT_TYPE
Q[17] <= Q1[17].DB_MAX_OUTPUT_PORT_TYPE
Q[18] <= Q1[18].DB_MAX_OUTPUT_PORT_TYPE
Q[19] <= Q1[19].DB_MAX_OUTPUT_PORT_TYPE
Q[20] <= Q1[20].DB_MAX_OUTPUT_PORT_TYPE
Q[21] <= Q1[21].DB_MAX_OUTPUT_PORT_TYPE
Q[22] <= Q1[22].DB_MAX_OUTPUT_PORT_TYPE
Q[23] <= Q1[23].DB_MAX_OUTPUT_PORT_TYPE
Q[24] <= Q1[24].DB_MAX_OUTPUT_PORT_TYPE
Q[25] <= Q1[25].DB_MAX_OUTPUT_PORT_TYPE
Q[26] <= Q1[26].DB_MAX_OUTPUT_PORT_TYPE
Q[27] <= Q1[27].DB_MAX_OUTPUT_PORT_TYPE
Q[28] <= Q1[28].DB_MAX_OUTPUT_PORT_TYPE
Q[29] <= Q1[29].DB_MAX_OUTPUT_PORT_TYPE
Q[30] <= Q1[30].DB_MAX_OUTPUT_PORT_TYPE
Q[31] <= Q1[31].DB_MAX_OUTPUT_PORT_TYPE
QBASE[0] <= Q1BASE[0].DB_MAX_OUTPUT_PORT_TYPE
QBASE[1] <= Q1BASE[1].DB_MAX_OUTPUT_PORT_TYPE
QBASE[2] <= Q1BASE[2].DB_MAX_OUTPUT_PORT_TYPE
QBASE[3] <= Q1BASE[3].DB_MAX_OUTPUT_PORT_TYPE
QBASE[4] <= Q1BASE[4].DB_MAX_OUTPUT_PORT_TYPE
QBASE[5] <= Q1BASE[5].DB_MAX_OUTPUT_PORT_TYPE
QBASE[6] <= Q1BASE[6].DB_MAX_OUTPUT_PORT_TYPE
QBASE[7] <= Q1BASE[7].DB_MAX_OUTPUT_PORT_TYPE
QBASE[8] <= Q1BASE[8].DB_MAX_OUTPUT_PORT_TYPE
QBASE[9] <= Q1BASE[9].DB_MAX_OUTPUT_PORT_TYPE
QBASE[10] <= Q1BASE[10].DB_MAX_OUTPUT_PORT_TYPE
QBASE[11] <= Q1BASE[11].DB_MAX_OUTPUT_PORT_TYPE
QBASE[12] <= Q1BASE[12].DB_MAX_OUTPUT_PORT_TYPE
QBASE[13] <= Q1BASE[13].DB_MAX_OUTPUT_PORT_TYPE
QBASE[14] <= Q1BASE[14].DB_MAX_OUTPUT_PORT_TYPE
QBASE[15] <= Q1BASE[15].DB_MAX_OUTPUT_PORT_TYPE
QBASE[16] <= Q1BASE[16].DB_MAX_OUTPUT_PORT_TYPE
QBASE[17] <= Q1BASE[17].DB_MAX_OUTPUT_PORT_TYPE
QBASE[18] <= Q1BASE[18].DB_MAX_OUTPUT_PORT_TYPE
QBASE[19] <= Q1BASE[19].DB_MAX_OUTPUT_PORT_TYPE
QBASE[20] <= Q1BASE[20].DB_MAX_OUTPUT_PORT_TYPE
QBASE[21] <= Q1BASE[21].DB_MAX_OUTPUT_PORT_TYPE
QBASE[22] <= Q1BASE[22].DB_MAX_OUTPUT_PORT_TYPE
QBASE[23] <= Q1BASE[23].DB_MAX_OUTPUT_PORT_TYPE
QBASE[24] <= Q1BASE[24].DB_MAX_OUTPUT_PORT_TYPE
QBASE[25] <= Q1BASE[25].DB_MAX_OUTPUT_PORT_TYPE
QBASE[26] <= Q1BASE[26].DB_MAX_OUTPUT_PORT_TYPE
QBASE[27] <= Q1BASE[27].DB_MAX_OUTPUT_PORT_TYPE
QBASE[28] <= Q1BASE[28].DB_MAX_OUTPUT_PORT_TYPE
QBASE[29] <= Q1BASE[29].DB_MAX_OUTPUT_PORT_TYPE
QBASE[30] <= Q1BASE[30].DB_MAX_OUTPUT_PORT_TYPE
QBASE[31] <= Q1BASE[31].DB_MAX_OUTPUT_PORT_TYPE


|TOP|CNT32:u_AD2_CNT32
CLR => Q1[0].ACLR
CLR => Q1[1].ACLR
CLR => Q1[2].ACLR
CLR => Q1[3].ACLR
CLR => Q1[4].ACLR
CLR => Q1[5].ACLR
CLR => Q1[6].ACLR
CLR => Q1[7].ACLR
CLR => Q1[8].ACLR
CLR => Q1[9].ACLR
CLR => Q1[10].ACLR
CLR => Q1[11].ACLR
CLR => Q1[12].ACLR
CLR => Q1[13].ACLR
CLR => Q1[14].ACLR
CLR => Q1[15].ACLR
CLR => Q1[16].ACLR
CLR => Q1[17].ACLR
CLR => Q1[18].ACLR
CLR => Q1[19].ACLR
CLR => Q1[20].ACLR
CLR => Q1[21].ACLR
CLR => Q1[22].ACLR
CLR => Q1[23].ACLR
CLR => Q1[24].ACLR
CLR => Q1[25].ACLR
CLR => Q1[26].ACLR
CLR => Q1[27].ACLR
CLR => Q1[28].ACLR
CLR => Q1[29].ACLR
CLR => Q1[30].ACLR
CLR => Q1[31].ACLR
CLR => Q1BASE[0].ACLR
CLR => Q1BASE[1].ACLR
CLR => Q1BASE[2].ACLR
CLR => Q1BASE[3].ACLR
CLR => Q1BASE[4].ACLR
CLR => Q1BASE[5].ACLR
CLR => Q1BASE[6].ACLR
CLR => Q1BASE[7].ACLR
CLR => Q1BASE[8].ACLR
CLR => Q1BASE[9].ACLR
CLR => Q1BASE[10].ACLR
CLR => Q1BASE[11].ACLR
CLR => Q1BASE[12].ACLR
CLR => Q1BASE[13].ACLR
CLR => Q1BASE[14].ACLR
CLR => Q1BASE[15].ACLR
CLR => Q1BASE[16].ACLR
CLR => Q1BASE[17].ACLR
CLR => Q1BASE[18].ACLR
CLR => Q1BASE[19].ACLR
CLR => Q1BASE[20].ACLR
CLR => Q1BASE[21].ACLR
CLR => Q1BASE[22].ACLR
CLR => Q1BASE[23].ACLR
CLR => Q1BASE[24].ACLR
CLR => Q1BASE[25].ACLR
CLR => Q1BASE[26].ACLR
CLR => Q1BASE[27].ACLR
CLR => Q1BASE[28].ACLR
CLR => Q1BASE[29].ACLR
CLR => Q1BASE[30].ACLR
CLR => Q1BASE[31].ACLR
CLK => Q1[0].CLK
CLK => Q1[1].CLK
CLK => Q1[2].CLK
CLK => Q1[3].CLK
CLK => Q1[4].CLK
CLK => Q1[5].CLK
CLK => Q1[6].CLK
CLK => Q1[7].CLK
CLK => Q1[8].CLK
CLK => Q1[9].CLK
CLK => Q1[10].CLK
CLK => Q1[11].CLK
CLK => Q1[12].CLK
CLK => Q1[13].CLK
CLK => Q1[14].CLK
CLK => Q1[15].CLK
CLK => Q1[16].CLK
CLK => Q1[17].CLK
CLK => Q1[18].CLK
CLK => Q1[19].CLK
CLK => Q1[20].CLK
CLK => Q1[21].CLK
CLK => Q1[22].CLK
CLK => Q1[23].CLK
CLK => Q1[24].CLK
CLK => Q1[25].CLK
CLK => Q1[26].CLK
CLK => Q1[27].CLK
CLK => Q1[28].CLK
CLK => Q1[29].CLK
CLK => Q1[30].CLK
CLK => Q1[31].CLK
CLKBASE => Q1BASE[0].CLK
CLKBASE => Q1BASE[1].CLK
CLKBASE => Q1BASE[2].CLK
CLKBASE => Q1BASE[3].CLK
CLKBASE => Q1BASE[4].CLK
CLKBASE => Q1BASE[5].CLK
CLKBASE => Q1BASE[6].CLK
CLKBASE => Q1BASE[7].CLK
CLKBASE => Q1BASE[8].CLK
CLKBASE => Q1BASE[9].CLK
CLKBASE => Q1BASE[10].CLK
CLKBASE => Q1BASE[11].CLK
CLKBASE => Q1BASE[12].CLK
CLKBASE => Q1BASE[13].CLK
CLKBASE => Q1BASE[14].CLK
CLKBASE => Q1BASE[15].CLK
CLKBASE => Q1BASE[16].CLK
CLKBASE => Q1BASE[17].CLK
CLKBASE => Q1BASE[18].CLK
CLKBASE => Q1BASE[19].CLK
CLKBASE => Q1BASE[20].CLK
CLKBASE => Q1BASE[21].CLK
CLKBASE => Q1BASE[22].CLK
CLKBASE => Q1BASE[23].CLK
CLKBASE => Q1BASE[24].CLK
CLKBASE => Q1BASE[25].CLK
CLKBASE => Q1BASE[26].CLK
CLKBASE => Q1BASE[27].CLK
CLKBASE => Q1BASE[28].CLK
CLKBASE => Q1BASE[29].CLK
CLKBASE => Q1BASE[30].CLK
CLKBASE => Q1BASE[31].CLK
CLKEN => Q1[31].ENA
CLKEN => Q1[30].ENA
CLKEN => Q1[29].ENA
CLKEN => Q1[28].ENA
CLKEN => Q1[27].ENA
CLKEN => Q1[26].ENA
CLKEN => Q1[25].ENA
CLKEN => Q1[24].ENA
CLKEN => Q1[23].ENA
CLKEN => Q1[22].ENA
CLKEN => Q1[21].ENA
CLKEN => Q1[20].ENA
CLKEN => Q1[19].ENA
CLKEN => Q1[18].ENA
CLKEN => Q1[17].ENA
CLKEN => Q1[16].ENA
CLKEN => Q1[15].ENA
CLKEN => Q1[14].ENA
CLKEN => Q1[13].ENA
CLKEN => Q1[12].ENA
CLKEN => Q1[11].ENA
CLKEN => Q1[10].ENA
CLKEN => Q1[9].ENA
CLKEN => Q1[8].ENA
CLKEN => Q1[7].ENA
CLKEN => Q1[6].ENA
CLKEN => Q1[5].ENA
CLKEN => Q1[4].ENA
CLKEN => Q1[3].ENA
CLKEN => Q1[2].ENA
CLKEN => Q1[1].ENA
CLKEN => Q1[0].ENA
CLKBASEEN => Q1BASE[0].ENA
CLKBASEEN => Q1BASE[31].ENA
CLKBASEEN => Q1BASE[30].ENA
CLKBASEEN => Q1BASE[29].ENA
CLKBASEEN => Q1BASE[28].ENA
CLKBASEEN => Q1BASE[27].ENA
CLKBASEEN => Q1BASE[26].ENA
CLKBASEEN => Q1BASE[25].ENA
CLKBASEEN => Q1BASE[24].ENA
CLKBASEEN => Q1BASE[23].ENA
CLKBASEEN => Q1BASE[22].ENA
CLKBASEEN => Q1BASE[21].ENA
CLKBASEEN => Q1BASE[20].ENA
CLKBASEEN => Q1BASE[19].ENA
CLKBASEEN => Q1BASE[18].ENA
CLKBASEEN => Q1BASE[17].ENA
CLKBASEEN => Q1BASE[16].ENA
CLKBASEEN => Q1BASE[15].ENA
CLKBASEEN => Q1BASE[14].ENA
CLKBASEEN => Q1BASE[13].ENA
CLKBASEEN => Q1BASE[12].ENA
CLKBASEEN => Q1BASE[11].ENA
CLKBASEEN => Q1BASE[10].ENA
CLKBASEEN => Q1BASE[9].ENA
CLKBASEEN => Q1BASE[8].ENA
CLKBASEEN => Q1BASE[7].ENA
CLKBASEEN => Q1BASE[6].ENA
CLKBASEEN => Q1BASE[5].ENA
CLKBASEEN => Q1BASE[4].ENA
CLKBASEEN => Q1BASE[3].ENA
CLKBASEEN => Q1BASE[2].ENA
CLKBASEEN => Q1BASE[1].ENA
Q[0] <= Q1[0].DB_MAX_OUTPUT_PORT_TYPE
Q[1] <= Q1[1].DB_MAX_OUTPUT_PORT_TYPE
Q[2] <= Q1[2].DB_MAX_OUTPUT_PORT_TYPE
Q[3] <= Q1[3].DB_MAX_OUTPUT_PORT_TYPE
Q[4] <= Q1[4].DB_MAX_OUTPUT_PORT_TYPE
Q[5] <= Q1[5].DB_MAX_OUTPUT_PORT_TYPE
Q[6] <= Q1[6].DB_MAX_OUTPUT_PORT_TYPE
Q[7] <= Q1[7].DB_MAX_OUTPUT_PORT_TYPE
Q[8] <= Q1[8].DB_MAX_OUTPUT_PORT_TYPE
Q[9] <= Q1[9].DB_MAX_OUTPUT_PORT_TYPE
Q[10] <= Q1[10].DB_MAX_OUTPUT_PORT_TYPE
Q[11] <= Q1[11].DB_MAX_OUTPUT_PORT_TYPE
Q[12] <= Q1[12].DB_MAX_OUTPUT_PORT_TYPE
Q[13] <= Q1[13].DB_MAX_OUTPUT_PORT_TYPE
Q[14] <= Q1[14].DB_MAX_OUTPUT_PORT_TYPE
Q[15] <= Q1[15].DB_MAX_OUTPUT_PORT_TYPE
Q[16] <= Q1[16].DB_MAX_OUTPUT_PORT_TYPE
Q[17] <= Q1[17].DB_MAX_OUTPUT_PORT_TYPE
Q[18] <= Q1[18].DB_MAX_OUTPUT_PORT_TYPE
Q[19] <= Q1[19].DB_MAX_OUTPUT_PORT_TYPE
Q[20] <= Q1[20].DB_MAX_OUTPUT_PORT_TYPE
Q[21] <= Q1[21].DB_MAX_OUTPUT_PORT_TYPE
Q[22] <= Q1[22].DB_MAX_OUTPUT_PORT_TYPE
Q[23] <= Q1[23].DB_MAX_OUTPUT_PORT_TYPE
Q[24] <= Q1[24].DB_MAX_OUTPUT_PORT_TYPE
Q[25] <= Q1[25].DB_MAX_OUTPUT_PORT_TYPE
Q[26] <= Q1[26].DB_MAX_OUTPUT_PORT_TYPE
Q[27] <= Q1[27].DB_MAX_OUTPUT_PORT_TYPE
Q[28] <= Q1[28].DB_MAX_OUTPUT_PORT_TYPE
Q[29] <= Q1[29].DB_MAX_OUTPUT_PORT_TYPE
Q[30] <= Q1[30].DB_MAX_OUTPUT_PORT_TYPE
Q[31] <= Q1[31].DB_MAX_OUTPUT_PORT_TYPE
QBASE[0] <= Q1BASE[0].DB_MAX_OUTPUT_PORT_TYPE
QBASE[1] <= Q1BASE[1].DB_MAX_OUTPUT_PORT_TYPE
QBASE[2] <= Q1BASE[2].DB_MAX_OUTPUT_PORT_TYPE
QBASE[3] <= Q1BASE[3].DB_MAX_OUTPUT_PORT_TYPE
QBASE[4] <= Q1BASE[4].DB_MAX_OUTPUT_PORT_TYPE
QBASE[5] <= Q1BASE[5].DB_MAX_OUTPUT_PORT_TYPE
QBASE[6] <= Q1BASE[6].DB_MAX_OUTPUT_PORT_TYPE
QBASE[7] <= Q1BASE[7].DB_MAX_OUTPUT_PORT_TYPE
QBASE[8] <= Q1BASE[8].DB_MAX_OUTPUT_PORT_TYPE
QBASE[9] <= Q1BASE[9].DB_MAX_OUTPUT_PORT_TYPE
QBASE[10] <= Q1BASE[10].DB_MAX_OUTPUT_PORT_TYPE
QBASE[11] <= Q1BASE[11].DB_MAX_OUTPUT_PORT_TYPE
QBASE[12] <= Q1BASE[12].DB_MAX_OUTPUT_PORT_TYPE
QBASE[13] <= Q1BASE[13].DB_MAX_OUTPUT_PORT_TYPE
QBASE[14] <= Q1BASE[14].DB_MAX_OUTPUT_PORT_TYPE
QBASE[15] <= Q1BASE[15].DB_MAX_OUTPUT_PORT_TYPE
QBASE[16] <= Q1BASE[16].DB_MAX_OUTPUT_PORT_TYPE
QBASE[17] <= Q1BASE[17].DB_MAX_OUTPUT_PORT_TYPE
QBASE[18] <= Q1BASE[18].DB_MAX_OUTPUT_PORT_TYPE
QBASE[19] <= Q1BASE[19].DB_MAX_OUTPUT_PORT_TYPE
QBASE[20] <= Q1BASE[20].DB_MAX_OUTPUT_PORT_TYPE
QBASE[21] <= Q1BASE[21].DB_MAX_OUTPUT_PORT_TYPE
QBASE[22] <= Q1BASE[22].DB_MAX_OUTPUT_PORT_TYPE
QBASE[23] <= Q1BASE[23].DB_MAX_OUTPUT_PORT_TYPE
QBASE[24] <= Q1BASE[24].DB_MAX_OUTPUT_PORT_TYPE
QBASE[25] <= Q1BASE[25].DB_MAX_OUTPUT_PORT_TYPE
QBASE[26] <= Q1BASE[26].DB_MAX_OUTPUT_PORT_TYPE
QBASE[27] <= Q1BASE[27].DB_MAX_OUTPUT_PORT_TYPE
QBASE[28] <= Q1BASE[28].DB_MAX_OUTPUT_PORT_TYPE
QBASE[29] <= Q1BASE[29].DB_MAX_OUTPUT_PORT_TYPE
QBASE[30] <= Q1BASE[30].DB_MAX_OUTPUT_PORT_TYPE
QBASE[31] <= Q1BASE[31].DB_MAX_OUTPUT_PORT_TYPE


|TOP|test:inst2
old_data[0] => new_data[0].DATAIN
old_data[1] => new_data[1].DATAIN
old_data[2] => new_data[2].DATAIN
old_data[3] => new_data[3].DATAIN
old_data[4] => new_data[4].DATAIN
old_data[5] => new_data[5].DATAIN
old_data[6] => new_data[6].DATAIN
old_data[7] => new_data[7].DATAIN
old_data[8] => new_data[8].DATAIN
old_data[9] => new_data[9].DATAIN
old_data[10] => new_data[10].DATAIN
old_data[11] => new_data[11].DATAIN
old_data[12] => new_data[12].DATAIN
old_data[13] => new_data[13].DATAIN
old_data[14] => new_data[14].DATAIN
old_data[15] => new_data[15].DATAIN
new_data[0] <= old_data[0].DB_MAX_OUTPUT_PORT_TYPE
new_data[1] <= old_data[1].DB_MAX_OUTPUT_PORT_TYPE
new_data[2] <= old_data[2].DB_MAX_OUTPUT_PORT_TYPE
new_data[3] <= old_data[3].DB_MAX_OUTPUT_PORT_TYPE
new_data[4] <= old_data[4].DB_MAX_OUTPUT_PORT_TYPE
new_data[5] <= old_data[5].DB_MAX_OUTPUT_PORT_TYPE
new_data[6] <= old_data[6].DB_MAX_OUTPUT_PORT_TYPE
new_data[7] <= old_data[7].DB_MAX_OUTPUT_PORT_TYPE
new_data[8] <= old_data[8].DB_MAX_OUTPUT_PORT_TYPE
new_data[9] <= old_data[9].DB_MAX_OUTPUT_PORT_TYPE
new_data[10] <= old_data[10].DB_MAX_OUTPUT_PORT_TYPE
new_data[11] <= old_data[11].DB_MAX_OUTPUT_PORT_TYPE
new_data[12] <= old_data[12].DB_MAX_OUTPUT_PORT_TYPE
new_data[13] <= old_data[13].DB_MAX_OUTPUT_PORT_TYPE
new_data[14] <= old_data[14].DB_MAX_OUTPUT_PORT_TYPE
new_data[15] <= old_data[15].DB_MAX_OUTPUT_PORT_TYPE


|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL
CS => always0.IN0
RD_EN => always0.IN1
AD1_FLAG => AD1_FLAG_SHOW[0]$latch.DATAIN
AD2_FLAG => AD2_FLAG_SHOW[0]$latch.DATAIN
AD1_FIFO_DATA_IN[0] => AD1_FIFO_DATA_OUT[11]$latch.DATAIN
AD1_FIFO_DATA_IN[1] => AD1_FIFO_DATA_OUT[10]$latch.DATAIN
AD1_FIFO_DATA_IN[2] => AD1_FIFO_DATA_OUT[9]$latch.DATAIN
AD1_FIFO_DATA_IN[3] => AD1_FIFO_DATA_OUT[8]$latch.DATAIN
AD1_FIFO_DATA_IN[4] => AD1_FIFO_DATA_OUT[7]$latch.DATAIN
AD1_FIFO_DATA_IN[5] => AD1_FIFO_DATA_OUT[6]$latch.DATAIN
AD1_FIFO_DATA_IN[6] => AD1_FIFO_DATA_OUT[5]$latch.DATAIN
AD1_FIFO_DATA_IN[7] => AD1_FIFO_DATA_OUT[4]$latch.DATAIN
AD1_FIFO_DATA_IN[8] => AD1_FIFO_DATA_OUT[3]$latch.DATAIN
AD1_FIFO_DATA_IN[9] => AD1_FIFO_DATA_OUT[2]$latch.DATAIN
AD1_FIFO_DATA_IN[10] => AD1_FIFO_DATA_OUT[1]$latch.DATAIN
AD1_FIFO_DATA_IN[11] => AD1_FIFO_DATA_OUT[0]$latch.DATAIN
AD2_FIFO_DATA_IN[0] => AD2_FIFO_DATA_OUT[11]$latch.DATAIN
AD2_FIFO_DATA_IN[1] => AD2_FIFO_DATA_OUT[10]$latch.DATAIN
AD2_FIFO_DATA_IN[2] => AD2_FIFO_DATA_OUT[9]$latch.DATAIN
AD2_FIFO_DATA_IN[3] => AD2_FIFO_DATA_OUT[8]$latch.DATAIN
AD2_FIFO_DATA_IN[4] => AD2_FIFO_DATA_OUT[7]$latch.DATAIN
AD2_FIFO_DATA_IN[5] => AD2_FIFO_DATA_OUT[6]$latch.DATAIN
AD2_FIFO_DATA_IN[6] => AD2_FIFO_DATA_OUT[5]$latch.DATAIN
AD2_FIFO_DATA_IN[7] => AD2_FIFO_DATA_OUT[4]$latch.DATAIN
AD2_FIFO_DATA_IN[8] => AD2_FIFO_DATA_OUT[3]$latch.DATAIN
AD2_FIFO_DATA_IN[9] => AD2_FIFO_DATA_OUT[2]$latch.DATAIN
AD2_FIFO_DATA_IN[10] => AD2_FIFO_DATA_OUT[1]$latch.DATAIN
AD2_FIFO_DATA_IN[11] => AD2_FIFO_DATA_OUT[0]$latch.DATAIN
ADDR[0] => Equal0.IN31
ADDR[0] => Equal1.IN31
ADDR[0] => Equal2.IN31
ADDR[0] => Equal3.IN31
ADDR[1] => Equal0.IN30
ADDR[1] => Equal1.IN30
ADDR[1] => Equal2.IN30
ADDR[1] => Equal3.IN30
ADDR[2] => Equal0.IN29
ADDR[2] => Equal1.IN29
ADDR[2] => Equal2.IN29
ADDR[2] => Equal3.IN29
ADDR[3] => Equal0.IN28
ADDR[3] => Equal1.IN28
ADDR[3] => Equal2.IN28
ADDR[3] => Equal3.IN28
ADDR[4] => Equal0.IN27
ADDR[4] => Equal1.IN27
ADDR[4] => Equal2.IN27
ADDR[4] => Equal3.IN27
ADDR[5] => Equal0.IN26
ADDR[5] => Equal1.IN26
ADDR[5] => Equal2.IN26
ADDR[5] => Equal3.IN26
ADDR[6] => Equal0.IN25
ADDR[6] => Equal1.IN25
ADDR[6] => Equal2.IN25
ADDR[6] => Equal3.IN25
ADDR[7] => Equal0.IN24
ADDR[7] => Equal1.IN24
ADDR[7] => Equal2.IN24
ADDR[7] => Equal3.IN24
ADDR[8] => Equal0.IN23
ADDR[8] => Equal1.IN23
ADDR[8] => Equal2.IN23
ADDR[8] => Equal3.IN23
ADDR[9] => Equal0.IN22
ADDR[9] => Equal1.IN22
ADDR[9] => Equal2.IN22
ADDR[9] => Equal3.IN22
ADDR[10] => Equal0.IN21
ADDR[10] => Equal1.IN21
ADDR[10] => Equal2.IN21
ADDR[10] => Equal3.IN21
ADDR[11] => Equal0.IN20
ADDR[11] => Equal1.IN20
ADDR[11] => Equal2.IN20
ADDR[11] => Equal3.IN20
ADDR[12] => Equal0.IN19
ADDR[12] => Equal1.IN19
ADDR[12] => Equal2.IN19
ADDR[12] => Equal3.IN19
ADDR[13] => Equal0.IN18
ADDR[13] => Equal1.IN18
ADDR[13] => Equal2.IN18
ADDR[13] => Equal3.IN18
ADDR[14] => Equal0.IN17
ADDR[14] => Equal1.IN17
ADDR[14] => Equal2.IN17
ADDR[14] => Equal3.IN17
ADDR[15] => Equal0.IN16
ADDR[15] => Equal1.IN16
ADDR[15] => Equal2.IN16
ADDR[15] => Equal3.IN16
AD1_FLAG_SHOW[0] <= AD1_FLAG_SHOW[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_FLAG_SHOW[1] <= <GND>
AD1_FLAG_SHOW[2] <= <GND>
AD1_FLAG_SHOW[3] <= <GND>
AD1_FLAG_SHOW[4] <= <GND>
AD1_FLAG_SHOW[5] <= <GND>
AD1_FLAG_SHOW[6] <= <GND>
AD1_FLAG_SHOW[7] <= <GND>
AD1_FLAG_SHOW[8] <= <GND>
AD1_FLAG_SHOW[9] <= <GND>
AD1_FLAG_SHOW[10] <= <GND>
AD1_FLAG_SHOW[11] <= <GND>
AD1_FLAG_SHOW[12] <= <GND>
AD1_FLAG_SHOW[13] <= <GND>
AD1_FLAG_SHOW[14] <= <GND>
AD1_FLAG_SHOW[15] <= <GND>
AD2_FLAG_SHOW[0] <= AD2_FLAG_SHOW[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_FLAG_SHOW[1] <= <GND>
AD2_FLAG_SHOW[2] <= <GND>
AD2_FLAG_SHOW[3] <= <GND>
AD2_FLAG_SHOW[4] <= <GND>
AD2_FLAG_SHOW[5] <= <GND>
AD2_FLAG_SHOW[6] <= <GND>
AD2_FLAG_SHOW[7] <= <GND>
AD2_FLAG_SHOW[8] <= <GND>
AD2_FLAG_SHOW[9] <= <GND>
AD2_FLAG_SHOW[10] <= <GND>
AD2_FLAG_SHOW[11] <= <GND>
AD2_FLAG_SHOW[12] <= <GND>
AD2_FLAG_SHOW[13] <= <GND>
AD2_FLAG_SHOW[14] <= <GND>
AD2_FLAG_SHOW[15] <= <GND>
AD1_FIFO_DATA_OUT[0] <= AD1_FIFO_DATA_OUT[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_FIFO_DATA_OUT[1] <= AD1_FIFO_DATA_OUT[1]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_FIFO_DATA_OUT[2] <= AD1_FIFO_DATA_OUT[2]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_FIFO_DATA_OUT[3] <= AD1_FIFO_DATA_OUT[3]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_FIFO_DATA_OUT[4] <= AD1_FIFO_DATA_OUT[4]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_FIFO_DATA_OUT[5] <= AD1_FIFO_DATA_OUT[5]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_FIFO_DATA_OUT[6] <= AD1_FIFO_DATA_OUT[6]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_FIFO_DATA_OUT[7] <= AD1_FIFO_DATA_OUT[7]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_FIFO_DATA_OUT[8] <= AD1_FIFO_DATA_OUT[8]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_FIFO_DATA_OUT[9] <= AD1_FIFO_DATA_OUT[9]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_FIFO_DATA_OUT[10] <= AD1_FIFO_DATA_OUT[10]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_FIFO_DATA_OUT[11] <= AD1_FIFO_DATA_OUT[11]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_FIFO_DATA_OUT[12] <= <GND>
AD1_FIFO_DATA_OUT[13] <= <GND>
AD1_FIFO_DATA_OUT[14] <= <GND>
AD1_FIFO_DATA_OUT[15] <= <GND>
AD2_FIFO_DATA_OUT[0] <= AD2_FIFO_DATA_OUT[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_FIFO_DATA_OUT[1] <= AD2_FIFO_DATA_OUT[1]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_FIFO_DATA_OUT[2] <= AD2_FIFO_DATA_OUT[2]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_FIFO_DATA_OUT[3] <= AD2_FIFO_DATA_OUT[3]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_FIFO_DATA_OUT[4] <= AD2_FIFO_DATA_OUT[4]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_FIFO_DATA_OUT[5] <= AD2_FIFO_DATA_OUT[5]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_FIFO_DATA_OUT[6] <= AD2_FIFO_DATA_OUT[6]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_FIFO_DATA_OUT[7] <= AD2_FIFO_DATA_OUT[7]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_FIFO_DATA_OUT[8] <= AD2_FIFO_DATA_OUT[8]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_FIFO_DATA_OUT[9] <= AD2_FIFO_DATA_OUT[9]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_FIFO_DATA_OUT[10] <= AD2_FIFO_DATA_OUT[10]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_FIFO_DATA_OUT[11] <= AD2_FIFO_DATA_OUT[11]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_FIFO_DATA_OUT[12] <= <GND>
AD2_FIFO_DATA_OUT[13] <= <GND>
AD2_FIFO_DATA_OUT[14] <= <GND>
AD2_FIFO_DATA_OUT[15] <= <GND>


|TOP|TYFIFO:u_AD1_FIFO
data[0] => data[0].IN1
data[1] => data[1].IN1
data[2] => data[2].IN1
data[3] => data[3].IN1
data[4] => data[4].IN1
data[5] => data[5].IN1
data[6] => data[6].IN1
data[7] => data[7].IN1
data[8] => data[8].IN1
data[9] => data[9].IN1
data[10] => data[10].IN1
data[11] => data[11].IN1
rdclk => rdclk.IN1
rdreq => rdreq.IN1
wrclk => wrclk.IN1
wrreq => wrreq.IN1
q[0] <= dcfifo:dcfifo_component.q
q[1] <= dcfifo:dcfifo_component.q
q[2] <= dcfifo:dcfifo_component.q
q[3] <= dcfifo:dcfifo_component.q
q[4] <= dcfifo:dcfifo_component.q
q[5] <= dcfifo:dcfifo_component.q
q[6] <= dcfifo:dcfifo_component.q
q[7] <= dcfifo:dcfifo_component.q
q[8] <= dcfifo:dcfifo_component.q
q[9] <= dcfifo:dcfifo_component.q
q[10] <= dcfifo:dcfifo_component.q
q[11] <= dcfifo:dcfifo_component.q
wrfull <= dcfifo:dcfifo_component.wrfull


|TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component
data[0] => dcfifo_vve1:auto_generated.data[0]
data[1] => dcfifo_vve1:auto_generated.data[1]
data[2] => dcfifo_vve1:auto_generated.data[2]
data[3] => dcfifo_vve1:auto_generated.data[3]
data[4] => dcfifo_vve1:auto_generated.data[4]
data[5] => dcfifo_vve1:auto_generated.data[5]
data[6] => dcfifo_vve1:auto_generated.data[6]
data[7] => dcfifo_vve1:auto_generated.data[7]
data[8] => dcfifo_vve1:auto_generated.data[8]
data[9] => dcfifo_vve1:auto_generated.data[9]
data[10] => dcfifo_vve1:auto_generated.data[10]
data[11] => dcfifo_vve1:auto_generated.data[11]
q[0] <= dcfifo_vve1:auto_generated.q[0]
q[1] <= dcfifo_vve1:auto_generated.q[1]
q[2] <= dcfifo_vve1:auto_generated.q[2]
q[3] <= dcfifo_vve1:auto_generated.q[3]
q[4] <= dcfifo_vve1:auto_generated.q[4]
q[5] <= dcfifo_vve1:auto_generated.q[5]
q[6] <= dcfifo_vve1:auto_generated.q[6]
q[7] <= dcfifo_vve1:auto_generated.q[7]
q[8] <= dcfifo_vve1:auto_generated.q[8]
q[9] <= dcfifo_vve1:auto_generated.q[9]
q[10] <= dcfifo_vve1:auto_generated.q[10]
q[11] <= dcfifo_vve1:auto_generated.q[11]
eccstatus[0] <= <GND>
eccstatus[1] <= <GND>
rdclk => dcfifo_vve1:auto_generated.rdclk
rdreq => dcfifo_vve1:auto_generated.rdreq
wrclk => dcfifo_vve1:auto_generated.wrclk
wrreq => dcfifo_vve1:auto_generated.wrreq
aclr => ~NO_FANOUT~
rdempty <= <UNC>
rdfull <= <UNC>
wrempty <= <GND>
wrfull <= dcfifo_vve1:auto_generated.wrfull
rdusedw[0] <= <UNC>
rdusedw[1] <= <UNC>
rdusedw[2] <= <UNC>
rdusedw[3] <= <UNC>
rdusedw[4] <= <UNC>
rdusedw[5] <= <UNC>
rdusedw[6] <= <UNC>
rdusedw[7] <= <UNC>
rdusedw[8] <= <UNC>
rdusedw[9] <= <UNC>
wrusedw[0] <= <GND>
wrusedw[1] <= <GND>
wrusedw[2] <= <GND>
wrusedw[3] <= <GND>
wrusedw[4] <= <GND>
wrusedw[5] <= <GND>
wrusedw[6] <= <GND>
wrusedw[7] <= <GND>
wrusedw[8] <= <GND>
wrusedw[9] <= <GND>


|TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated
data[0] => altsyncram_ce41:fifo_ram.data_a[0]
data[1] => altsyncram_ce41:fifo_ram.data_a[1]
data[2] => altsyncram_ce41:fifo_ram.data_a[2]
data[3] => altsyncram_ce41:fifo_ram.data_a[3]
data[4] => altsyncram_ce41:fifo_ram.data_a[4]
data[5] => altsyncram_ce41:fifo_ram.data_a[5]
data[6] => altsyncram_ce41:fifo_ram.data_a[6]
data[7] => altsyncram_ce41:fifo_ram.data_a[7]
data[8] => altsyncram_ce41:fifo_ram.data_a[8]
data[9] => altsyncram_ce41:fifo_ram.data_a[9]
data[10] => altsyncram_ce41:fifo_ram.data_a[10]
data[11] => altsyncram_ce41:fifo_ram.data_a[11]
q[0] <= altsyncram_ce41:fifo_ram.q_b[0]
q[1] <= altsyncram_ce41:fifo_ram.q_b[1]
q[2] <= altsyncram_ce41:fifo_ram.q_b[2]
q[3] <= altsyncram_ce41:fifo_ram.q_b[3]
q[4] <= altsyncram_ce41:fifo_ram.q_b[4]
q[5] <= altsyncram_ce41:fifo_ram.q_b[5]
q[6] <= altsyncram_ce41:fifo_ram.q_b[6]
q[7] <= altsyncram_ce41:fifo_ram.q_b[7]
q[8] <= altsyncram_ce41:fifo_ram.q_b[8]
q[9] <= altsyncram_ce41:fifo_ram.q_b[9]
q[10] <= altsyncram_ce41:fifo_ram.q_b[10]
q[11] <= altsyncram_ce41:fifo_ram.q_b[11]
rdclk => a_graycounter_4p6:rdptr_g1p.clock
rdclk => altsyncram_ce41:fifo_ram.clock1
rdclk => alt_synch_pipe_qal:rs_dgwp.clock
rdclk => rdptr_g[10].CLK
rdclk => rdptr_g[9].CLK
rdclk => rdptr_g[8].CLK
rdclk => rdptr_g[7].CLK
rdclk => rdptr_g[6].CLK
rdclk => rdptr_g[5].CLK
rdclk => rdptr_g[4].CLK
rdclk => rdptr_g[3].CLK
rdclk => rdptr_g[2].CLK
rdclk => rdptr_g[1].CLK
rdclk => rdptr_g[0].CLK
rdreq => valid_rdreq.IN0
wrclk => a_graycounter_07c:wrptr_g1p.clock
wrclk => altsyncram_ce41:fifo_ram.clock0
wrclk => alt_synch_pipe_ral:ws_dgrp.clock
wrclk => delayed_wrptr_g[10].CLK
wrclk => delayed_wrptr_g[9].CLK
wrclk => delayed_wrptr_g[8].CLK
wrclk => delayed_wrptr_g[7].CLK
wrclk => delayed_wrptr_g[6].CLK
wrclk => delayed_wrptr_g[5].CLK
wrclk => delayed_wrptr_g[4].CLK
wrclk => delayed_wrptr_g[3].CLK
wrclk => delayed_wrptr_g[2].CLK
wrclk => delayed_wrptr_g[1].CLK
wrclk => delayed_wrptr_g[0].CLK
wrclk => wrptr_g[10].CLK
wrclk => wrptr_g[9].CLK
wrclk => wrptr_g[8].CLK
wrclk => wrptr_g[7].CLK
wrclk => wrptr_g[6].CLK
wrclk => wrptr_g[5].CLK
wrclk => wrptr_g[4].CLK
wrclk => wrptr_g[3].CLK
wrclk => wrptr_g[2].CLK
wrclk => wrptr_g[1].CLK
wrclk => wrptr_g[0].CLK
wrfull <= int_wrfull.DB_MAX_OUTPUT_PORT_TYPE
wrreq => valid_wrreq.IN0


|TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_4p6:rdptr_g1p
clock => counter5a0.CLK
clock => counter5a1.CLK
clock => counter5a2.CLK
clock => counter5a3.CLK
clock => counter5a4.CLK
clock => counter5a5.CLK
clock => counter5a6.CLK
clock => counter5a7.CLK
clock => counter5a8.CLK
clock => counter5a9.CLK
clock => counter5a10.CLK
clock => parity6.CLK
clock => sub_parity7a[2].CLK
clock => sub_parity7a[1].CLK
clock => sub_parity7a[0].CLK
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => cntr_cout[0].IN0
cnt_en => parity_cout.IN1
q[0] <= counter5a0.DB_MAX_OUTPUT_PORT_TYPE
q[1] <= counter5a1.DB_MAX_OUTPUT_PORT_TYPE
q[2] <= counter5a2.DB_MAX_OUTPUT_PORT_TYPE
q[3] <= counter5a3.DB_MAX_OUTPUT_PORT_TYPE
q[4] <= counter5a4.DB_MAX_OUTPUT_PORT_TYPE
q[5] <= counter5a5.DB_MAX_OUTPUT_PORT_TYPE
q[6] <= counter5a6.DB_MAX_OUTPUT_PORT_TYPE
q[7] <= counter5a7.DB_MAX_OUTPUT_PORT_TYPE
q[8] <= counter5a8.DB_MAX_OUTPUT_PORT_TYPE
q[9] <= counter5a9.DB_MAX_OUTPUT_PORT_TYPE
q[10] <= counter5a10.DB_MAX_OUTPUT_PORT_TYPE


|TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_07c:wrptr_g1p
clock => counter8a0.CLK
clock => counter8a1.CLK
clock => counter8a2.CLK
clock => counter8a3.CLK
clock => counter8a4.CLK
clock => counter8a5.CLK
clock => counter8a6.CLK
clock => counter8a7.CLK
clock => counter8a8.CLK
clock => counter8a9.CLK
clock => counter8a10.CLK
clock => parity9.CLK
clock => sub_parity10a[2].CLK
clock => sub_parity10a[1].CLK
clock => sub_parity10a[0].CLK
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => cntr_cout[0].IN0
cnt_en => parity_cout.IN1
q[0] <= counter8a0.DB_MAX_OUTPUT_PORT_TYPE
q[1] <= counter8a1.DB_MAX_OUTPUT_PORT_TYPE
q[2] <= counter8a2.DB_MAX_OUTPUT_PORT_TYPE
q[3] <= counter8a3.DB_MAX_OUTPUT_PORT_TYPE
q[4] <= counter8a4.DB_MAX_OUTPUT_PORT_TYPE
q[5] <= counter8a5.DB_MAX_OUTPUT_PORT_TYPE
q[6] <= counter8a6.DB_MAX_OUTPUT_PORT_TYPE
q[7] <= counter8a7.DB_MAX_OUTPUT_PORT_TYPE
q[8] <= counter8a8.DB_MAX_OUTPUT_PORT_TYPE
q[9] <= counter8a9.DB_MAX_OUTPUT_PORT_TYPE
q[10] <= counter8a10.DB_MAX_OUTPUT_PORT_TYPE


|TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|altsyncram_ce41:fifo_ram
address_a[0] => ram_block11a0.PORTAADDR
address_a[0] => ram_block11a1.PORTAADDR
address_a[0] => ram_block11a2.PORTAADDR
address_a[0] => ram_block11a3.PORTAADDR
address_a[0] => ram_block11a4.PORTAADDR
address_a[0] => ram_block11a5.PORTAADDR
address_a[0] => ram_block11a6.PORTAADDR
address_a[0] => ram_block11a7.PORTAADDR
address_a[0] => ram_block11a8.PORTAADDR
address_a[0] => ram_block11a9.PORTAADDR
address_a[0] => ram_block11a10.PORTAADDR
address_a[0] => ram_block11a11.PORTAADDR
address_a[1] => ram_block11a0.PORTAADDR1
address_a[1] => ram_block11a1.PORTAADDR1
address_a[1] => ram_block11a2.PORTAADDR1
address_a[1] => ram_block11a3.PORTAADDR1
address_a[1] => ram_block11a4.PORTAADDR1
address_a[1] => ram_block11a5.PORTAADDR1
address_a[1] => ram_block11a6.PORTAADDR1
address_a[1] => ram_block11a7.PORTAADDR1
address_a[1] => ram_block11a8.PORTAADDR1
address_a[1] => ram_block11a9.PORTAADDR1
address_a[1] => ram_block11a10.PORTAADDR1
address_a[1] => ram_block11a11.PORTAADDR1
address_a[2] => ram_block11a0.PORTAADDR2
address_a[2] => ram_block11a1.PORTAADDR2
address_a[2] => ram_block11a2.PORTAADDR2
address_a[2] => ram_block11a3.PORTAADDR2
address_a[2] => ram_block11a4.PORTAADDR2
address_a[2] => ram_block11a5.PORTAADDR2
address_a[2] => ram_block11a6.PORTAADDR2
address_a[2] => ram_block11a7.PORTAADDR2
address_a[2] => ram_block11a8.PORTAADDR2
address_a[2] => ram_block11a9.PORTAADDR2
address_a[2] => ram_block11a10.PORTAADDR2
address_a[2] => ram_block11a11.PORTAADDR2
address_a[3] => ram_block11a0.PORTAADDR3
address_a[3] => ram_block11a1.PORTAADDR3
address_a[3] => ram_block11a2.PORTAADDR3
address_a[3] => ram_block11a3.PORTAADDR3
address_a[3] => ram_block11a4.PORTAADDR3
address_a[3] => ram_block11a5.PORTAADDR3
address_a[3] => ram_block11a6.PORTAADDR3
address_a[3] => ram_block11a7.PORTAADDR3
address_a[3] => ram_block11a8.PORTAADDR3
address_a[3] => ram_block11a9.PORTAADDR3
address_a[3] => ram_block11a10.PORTAADDR3
address_a[3] => ram_block11a11.PORTAADDR3
address_a[4] => ram_block11a0.PORTAADDR4
address_a[4] => ram_block11a1.PORTAADDR4
address_a[4] => ram_block11a2.PORTAADDR4
address_a[4] => ram_block11a3.PORTAADDR4
address_a[4] => ram_block11a4.PORTAADDR4
address_a[4] => ram_block11a5.PORTAADDR4
address_a[4] => ram_block11a6.PORTAADDR4
address_a[4] => ram_block11a7.PORTAADDR4
address_a[4] => ram_block11a8.PORTAADDR4
address_a[4] => ram_block11a9.PORTAADDR4
address_a[4] => ram_block11a10.PORTAADDR4
address_a[4] => ram_block11a11.PORTAADDR4
address_a[5] => ram_block11a0.PORTAADDR5
address_a[5] => ram_block11a1.PORTAADDR5
address_a[5] => ram_block11a2.PORTAADDR5
address_a[5] => ram_block11a3.PORTAADDR5
address_a[5] => ram_block11a4.PORTAADDR5
address_a[5] => ram_block11a5.PORTAADDR5
address_a[5] => ram_block11a6.PORTAADDR5
address_a[5] => ram_block11a7.PORTAADDR5
address_a[5] => ram_block11a8.PORTAADDR5
address_a[5] => ram_block11a9.PORTAADDR5
address_a[5] => ram_block11a10.PORTAADDR5
address_a[5] => ram_block11a11.PORTAADDR5
address_a[6] => ram_block11a0.PORTAADDR6
address_a[6] => ram_block11a1.PORTAADDR6
address_a[6] => ram_block11a2.PORTAADDR6
address_a[6] => ram_block11a3.PORTAADDR6
address_a[6] => ram_block11a4.PORTAADDR6
address_a[6] => ram_block11a5.PORTAADDR6
address_a[6] => ram_block11a6.PORTAADDR6
address_a[6] => ram_block11a7.PORTAADDR6
address_a[6] => ram_block11a8.PORTAADDR6
address_a[6] => ram_block11a9.PORTAADDR6
address_a[6] => ram_block11a10.PORTAADDR6
address_a[6] => ram_block11a11.PORTAADDR6
address_a[7] => ram_block11a0.PORTAADDR7
address_a[7] => ram_block11a1.PORTAADDR7
address_a[7] => ram_block11a2.PORTAADDR7
address_a[7] => ram_block11a3.PORTAADDR7
address_a[7] => ram_block11a4.PORTAADDR7
address_a[7] => ram_block11a5.PORTAADDR7
address_a[7] => ram_block11a6.PORTAADDR7
address_a[7] => ram_block11a7.PORTAADDR7
address_a[7] => ram_block11a8.PORTAADDR7
address_a[7] => ram_block11a9.PORTAADDR7
address_a[7] => ram_block11a10.PORTAADDR7
address_a[7] => ram_block11a11.PORTAADDR7
address_a[8] => ram_block11a0.PORTAADDR8
address_a[8] => ram_block11a1.PORTAADDR8
address_a[8] => ram_block11a2.PORTAADDR8
address_a[8] => ram_block11a3.PORTAADDR8
address_a[8] => ram_block11a4.PORTAADDR8
address_a[8] => ram_block11a5.PORTAADDR8
address_a[8] => ram_block11a6.PORTAADDR8
address_a[8] => ram_block11a7.PORTAADDR8
address_a[8] => ram_block11a8.PORTAADDR8
address_a[8] => ram_block11a9.PORTAADDR8
address_a[8] => ram_block11a10.PORTAADDR8
address_a[8] => ram_block11a11.PORTAADDR8
address_a[9] => ram_block11a0.PORTAADDR9
address_a[9] => ram_block11a1.PORTAADDR9
address_a[9] => ram_block11a2.PORTAADDR9
address_a[9] => ram_block11a3.PORTAADDR9
address_a[9] => ram_block11a4.PORTAADDR9
address_a[9] => ram_block11a5.PORTAADDR9
address_a[9] => ram_block11a6.PORTAADDR9
address_a[9] => ram_block11a7.PORTAADDR9
address_a[9] => ram_block11a8.PORTAADDR9
address_a[9] => ram_block11a9.PORTAADDR9
address_a[9] => ram_block11a10.PORTAADDR9
address_a[9] => ram_block11a11.PORTAADDR9
address_b[0] => ram_block11a0.PORTBADDR
address_b[0] => ram_block11a1.PORTBADDR
address_b[0] => ram_block11a2.PORTBADDR
address_b[0] => ram_block11a3.PORTBADDR
address_b[0] => ram_block11a4.PORTBADDR
address_b[0] => ram_block11a5.PORTBADDR
address_b[0] => ram_block11a6.PORTBADDR
address_b[0] => ram_block11a7.PORTBADDR
address_b[0] => ram_block11a8.PORTBADDR
address_b[0] => ram_block11a9.PORTBADDR
address_b[0] => ram_block11a10.PORTBADDR
address_b[0] => ram_block11a11.PORTBADDR
address_b[1] => ram_block11a0.PORTBADDR1
address_b[1] => ram_block11a1.PORTBADDR1
address_b[1] => ram_block11a2.PORTBADDR1
address_b[1] => ram_block11a3.PORTBADDR1
address_b[1] => ram_block11a4.PORTBADDR1
address_b[1] => ram_block11a5.PORTBADDR1
address_b[1] => ram_block11a6.PORTBADDR1
address_b[1] => ram_block11a7.PORTBADDR1
address_b[1] => ram_block11a8.PORTBADDR1
address_b[1] => ram_block11a9.PORTBADDR1
address_b[1] => ram_block11a10.PORTBADDR1
address_b[1] => ram_block11a11.PORTBADDR1
address_b[2] => ram_block11a0.PORTBADDR2
address_b[2] => ram_block11a1.PORTBADDR2
address_b[2] => ram_block11a2.PORTBADDR2
address_b[2] => ram_block11a3.PORTBADDR2
address_b[2] => ram_block11a4.PORTBADDR2
address_b[2] => ram_block11a5.PORTBADDR2
address_b[2] => ram_block11a6.PORTBADDR2
address_b[2] => ram_block11a7.PORTBADDR2
address_b[2] => ram_block11a8.PORTBADDR2
address_b[2] => ram_block11a9.PORTBADDR2
address_b[2] => ram_block11a10.PORTBADDR2
address_b[2] => ram_block11a11.PORTBADDR2
address_b[3] => ram_block11a0.PORTBADDR3
address_b[3] => ram_block11a1.PORTBADDR3
address_b[3] => ram_block11a2.PORTBADDR3
address_b[3] => ram_block11a3.PORTBADDR3
address_b[3] => ram_block11a4.PORTBADDR3
address_b[3] => ram_block11a5.PORTBADDR3
address_b[3] => ram_block11a6.PORTBADDR3
address_b[3] => ram_block11a7.PORTBADDR3
address_b[3] => ram_block11a8.PORTBADDR3
address_b[3] => ram_block11a9.PORTBADDR3
address_b[3] => ram_block11a10.PORTBADDR3
address_b[3] => ram_block11a11.PORTBADDR3
address_b[4] => ram_block11a0.PORTBADDR4
address_b[4] => ram_block11a1.PORTBADDR4
address_b[4] => ram_block11a2.PORTBADDR4
address_b[4] => ram_block11a3.PORTBADDR4
address_b[4] => ram_block11a4.PORTBADDR4
address_b[4] => ram_block11a5.PORTBADDR4
address_b[4] => ram_block11a6.PORTBADDR4
address_b[4] => ram_block11a7.PORTBADDR4
address_b[4] => ram_block11a8.PORTBADDR4
address_b[4] => ram_block11a9.PORTBADDR4
address_b[4] => ram_block11a10.PORTBADDR4
address_b[4] => ram_block11a11.PORTBADDR4
address_b[5] => ram_block11a0.PORTBADDR5
address_b[5] => ram_block11a1.PORTBADDR5
address_b[5] => ram_block11a2.PORTBADDR5
address_b[5] => ram_block11a3.PORTBADDR5
address_b[5] => ram_block11a4.PORTBADDR5
address_b[5] => ram_block11a5.PORTBADDR5
address_b[5] => ram_block11a6.PORTBADDR5
address_b[5] => ram_block11a7.PORTBADDR5
address_b[5] => ram_block11a8.PORTBADDR5
address_b[5] => ram_block11a9.PORTBADDR5
address_b[5] => ram_block11a10.PORTBADDR5
address_b[5] => ram_block11a11.PORTBADDR5
address_b[6] => ram_block11a0.PORTBADDR6
address_b[6] => ram_block11a1.PORTBADDR6
address_b[6] => ram_block11a2.PORTBADDR6
address_b[6] => ram_block11a3.PORTBADDR6
address_b[6] => ram_block11a4.PORTBADDR6
address_b[6] => ram_block11a5.PORTBADDR6
address_b[6] => ram_block11a6.PORTBADDR6
address_b[6] => ram_block11a7.PORTBADDR6
address_b[6] => ram_block11a8.PORTBADDR6
address_b[6] => ram_block11a9.PORTBADDR6
address_b[6] => ram_block11a10.PORTBADDR6
address_b[6] => ram_block11a11.PORTBADDR6
address_b[7] => ram_block11a0.PORTBADDR7
address_b[7] => ram_block11a1.PORTBADDR7
address_b[7] => ram_block11a2.PORTBADDR7
address_b[7] => ram_block11a3.PORTBADDR7
address_b[7] => ram_block11a4.PORTBADDR7
address_b[7] => ram_block11a5.PORTBADDR7
address_b[7] => ram_block11a6.PORTBADDR7
address_b[7] => ram_block11a7.PORTBADDR7
address_b[7] => ram_block11a8.PORTBADDR7
address_b[7] => ram_block11a9.PORTBADDR7
address_b[7] => ram_block11a10.PORTBADDR7
address_b[7] => ram_block11a11.PORTBADDR7
address_b[8] => ram_block11a0.PORTBADDR8
address_b[8] => ram_block11a1.PORTBADDR8
address_b[8] => ram_block11a2.PORTBADDR8
address_b[8] => ram_block11a3.PORTBADDR8
address_b[8] => ram_block11a4.PORTBADDR8
address_b[8] => ram_block11a5.PORTBADDR8
address_b[8] => ram_block11a6.PORTBADDR8
address_b[8] => ram_block11a7.PORTBADDR8
address_b[8] => ram_block11a8.PORTBADDR8
address_b[8] => ram_block11a9.PORTBADDR8
address_b[8] => ram_block11a10.PORTBADDR8
address_b[8] => ram_block11a11.PORTBADDR8
address_b[9] => ram_block11a0.PORTBADDR9
address_b[9] => ram_block11a1.PORTBADDR9
address_b[9] => ram_block11a2.PORTBADDR9
address_b[9] => ram_block11a3.PORTBADDR9
address_b[9] => ram_block11a4.PORTBADDR9
address_b[9] => ram_block11a5.PORTBADDR9
address_b[9] => ram_block11a6.PORTBADDR9
address_b[9] => ram_block11a7.PORTBADDR9
address_b[9] => ram_block11a8.PORTBADDR9
address_b[9] => ram_block11a9.PORTBADDR9
address_b[9] => ram_block11a10.PORTBADDR9
address_b[9] => ram_block11a11.PORTBADDR9
addressstall_b => ram_block11a0.PORTBADDRSTALL
addressstall_b => ram_block11a1.PORTBADDRSTALL
addressstall_b => ram_block11a2.PORTBADDRSTALL
addressstall_b => ram_block11a3.PORTBADDRSTALL
addressstall_b => ram_block11a4.PORTBADDRSTALL
addressstall_b => ram_block11a5.PORTBADDRSTALL
addressstall_b => ram_block11a6.PORTBADDRSTALL
addressstall_b => ram_block11a7.PORTBADDRSTALL
addressstall_b => ram_block11a8.PORTBADDRSTALL
addressstall_b => ram_block11a9.PORTBADDRSTALL
addressstall_b => ram_block11a10.PORTBADDRSTALL
addressstall_b => ram_block11a11.PORTBADDRSTALL
clock0 => ram_block11a0.CLK0
clock0 => ram_block11a1.CLK0
clock0 => ram_block11a2.CLK0
clock0 => ram_block11a3.CLK0
clock0 => ram_block11a4.CLK0
clock0 => ram_block11a5.CLK0
clock0 => ram_block11a6.CLK0
clock0 => ram_block11a7.CLK0
clock0 => ram_block11a8.CLK0
clock0 => ram_block11a9.CLK0
clock0 => ram_block11a10.CLK0
clock0 => ram_block11a11.CLK0
clock1 => ram_block11a0.CLK1
clock1 => ram_block11a1.CLK1
clock1 => ram_block11a2.CLK1
clock1 => ram_block11a3.CLK1
clock1 => ram_block11a4.CLK1
clock1 => ram_block11a5.CLK1
clock1 => ram_block11a6.CLK1
clock1 => ram_block11a7.CLK1
clock1 => ram_block11a8.CLK1
clock1 => ram_block11a9.CLK1
clock1 => ram_block11a10.CLK1
clock1 => ram_block11a11.CLK1
clocken1 => ram_block11a0.ENA1
clocken1 => ram_block11a1.ENA1
clocken1 => ram_block11a2.ENA1
clocken1 => ram_block11a3.ENA1
clocken1 => ram_block11a4.ENA1
clocken1 => ram_block11a5.ENA1
clocken1 => ram_block11a6.ENA1
clocken1 => ram_block11a7.ENA1
clocken1 => ram_block11a8.ENA1
clocken1 => ram_block11a9.ENA1
clocken1 => ram_block11a10.ENA1
clocken1 => ram_block11a11.ENA1
data_a[0] => ram_block11a0.PORTADATAIN
data_a[1] => ram_block11a1.PORTADATAIN
data_a[2] => ram_block11a2.PORTADATAIN
data_a[3] => ram_block11a3.PORTADATAIN
data_a[4] => ram_block11a4.PORTADATAIN
data_a[5] => ram_block11a5.PORTADATAIN
data_a[6] => ram_block11a6.PORTADATAIN
data_a[7] => ram_block11a7.PORTADATAIN
data_a[8] => ram_block11a8.PORTADATAIN
data_a[9] => ram_block11a9.PORTADATAIN
data_a[10] => ram_block11a10.PORTADATAIN
data_a[11] => ram_block11a11.PORTADATAIN
q_b[0] <= ram_block11a0.PORTBDATAOUT
q_b[1] <= ram_block11a1.PORTBDATAOUT
q_b[2] <= ram_block11a2.PORTBDATAOUT
q_b[3] <= ram_block11a3.PORTBDATAOUT
q_b[4] <= ram_block11a4.PORTBDATAOUT
q_b[5] <= ram_block11a5.PORTBDATAOUT
q_b[6] <= ram_block11a6.PORTBDATAOUT
q_b[7] <= ram_block11a7.PORTBDATAOUT
q_b[8] <= ram_block11a8.PORTBDATAOUT
q_b[9] <= ram_block11a9.PORTBDATAOUT
q_b[10] <= ram_block11a10.PORTBDATAOUT
q_b[11] <= ram_block11a11.PORTBDATAOUT
wren_a => ram_block11a0.PORTAWE
wren_a => ram_block11a0.ENA0
wren_a => ram_block11a1.PORTAWE
wren_a => ram_block11a1.ENA0
wren_a => ram_block11a2.PORTAWE
wren_a => ram_block11a2.ENA0
wren_a => ram_block11a3.PORTAWE
wren_a => ram_block11a3.ENA0
wren_a => ram_block11a4.PORTAWE
wren_a => ram_block11a4.ENA0
wren_a => ram_block11a5.PORTAWE
wren_a => ram_block11a5.ENA0
wren_a => ram_block11a6.PORTAWE
wren_a => ram_block11a6.ENA0
wren_a => ram_block11a7.PORTAWE
wren_a => ram_block11a7.ENA0
wren_a => ram_block11a8.PORTAWE
wren_a => ram_block11a8.ENA0
wren_a => ram_block11a9.PORTAWE
wren_a => ram_block11a9.ENA0
wren_a => ram_block11a10.PORTAWE
wren_a => ram_block11a10.ENA0
wren_a => ram_block11a11.PORTAWE
wren_a => ram_block11a11.ENA0


|TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp
clock => dffpipe_b09:dffpipe12.clock
d[0] => dffpipe_b09:dffpipe12.d[0]
d[1] => dffpipe_b09:dffpipe12.d[1]
d[2] => dffpipe_b09:dffpipe12.d[2]
d[3] => dffpipe_b09:dffpipe12.d[3]
d[4] => dffpipe_b09:dffpipe12.d[4]
d[5] => dffpipe_b09:dffpipe12.d[5]
d[6] => dffpipe_b09:dffpipe12.d[6]
d[7] => dffpipe_b09:dffpipe12.d[7]
d[8] => dffpipe_b09:dffpipe12.d[8]
d[9] => dffpipe_b09:dffpipe12.d[9]
d[10] => dffpipe_b09:dffpipe12.d[10]
q[0] <= dffpipe_b09:dffpipe12.q[0]
q[1] <= dffpipe_b09:dffpipe12.q[1]
q[2] <= dffpipe_b09:dffpipe12.q[2]
q[3] <= dffpipe_b09:dffpipe12.q[3]
q[4] <= dffpipe_b09:dffpipe12.q[4]
q[5] <= dffpipe_b09:dffpipe12.q[5]
q[6] <= dffpipe_b09:dffpipe12.q[6]
q[7] <= dffpipe_b09:dffpipe12.q[7]
q[8] <= dffpipe_b09:dffpipe12.q[8]
q[9] <= dffpipe_b09:dffpipe12.q[9]
q[10] <= dffpipe_b09:dffpipe12.q[10]


|TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12
clock => dffe13a[10].CLK
clock => dffe13a[9].CLK
clock => dffe13a[8].CLK
clock => dffe13a[7].CLK
clock => dffe13a[6].CLK
clock => dffe13a[5].CLK
clock => dffe13a[4].CLK
clock => dffe13a[3].CLK
clock => dffe13a[2].CLK
clock => dffe13a[1].CLK
clock => dffe13a[0].CLK
clock => dffe14a[10].CLK
clock => dffe14a[9].CLK
clock => dffe14a[8].CLK
clock => dffe14a[7].CLK
clock => dffe14a[6].CLK
clock => dffe14a[5].CLK
clock => dffe14a[4].CLK
clock => dffe14a[3].CLK
clock => dffe14a[2].CLK
clock => dffe14a[1].CLK
clock => dffe14a[0].CLK
d[0] => dffe13a[0].IN0
d[1] => dffe13a[1].IN0
d[2] => dffe13a[2].IN0
d[3] => dffe13a[3].IN0
d[4] => dffe13a[4].IN0
d[5] => dffe13a[5].IN0
d[6] => dffe13a[6].IN0
d[7] => dffe13a[7].IN0
d[8] => dffe13a[8].IN0
d[9] => dffe13a[9].IN0
d[10] => dffe13a[10].IN0
q[0] <= dffe14a[0].DB_MAX_OUTPUT_PORT_TYPE
q[1] <= dffe14a[1].DB_MAX_OUTPUT_PORT_TYPE
q[2] <= dffe14a[2].DB_MAX_OUTPUT_PORT_TYPE
q[3] <= dffe14a[3].DB_MAX_OUTPUT_PORT_TYPE
q[4] <= dffe14a[4].DB_MAX_OUTPUT_PORT_TYPE
q[5] <= dffe14a[5].DB_MAX_OUTPUT_PORT_TYPE
q[6] <= dffe14a[6].DB_MAX_OUTPUT_PORT_TYPE
q[7] <= dffe14a[7].DB_MAX_OUTPUT_PORT_TYPE
q[8] <= dffe14a[8].DB_MAX_OUTPUT_PORT_TYPE
q[9] <= dffe14a[9].DB_MAX_OUTPUT_PORT_TYPE
q[10] <= dffe14a[10].DB_MAX_OUTPUT_PORT_TYPE


|TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp
clock => dffpipe_c09:dffpipe15.clock
d[0] => dffpipe_c09:dffpipe15.d[0]
d[1] => dffpipe_c09:dffpipe15.d[1]
d[2] => dffpipe_c09:dffpipe15.d[2]
d[3] => dffpipe_c09:dffpipe15.d[3]
d[4] => dffpipe_c09:dffpipe15.d[4]
d[5] => dffpipe_c09:dffpipe15.d[5]
d[6] => dffpipe_c09:dffpipe15.d[6]
d[7] => dffpipe_c09:dffpipe15.d[7]
d[8] => dffpipe_c09:dffpipe15.d[8]
d[9] => dffpipe_c09:dffpipe15.d[9]
d[10] => dffpipe_c09:dffpipe15.d[10]
q[0] <= dffpipe_c09:dffpipe15.q[0]
q[1] <= dffpipe_c09:dffpipe15.q[1]
q[2] <= dffpipe_c09:dffpipe15.q[2]
q[3] <= dffpipe_c09:dffpipe15.q[3]
q[4] <= dffpipe_c09:dffpipe15.q[4]
q[5] <= dffpipe_c09:dffpipe15.q[5]
q[6] <= dffpipe_c09:dffpipe15.q[6]
q[7] <= dffpipe_c09:dffpipe15.q[7]
q[8] <= dffpipe_c09:dffpipe15.q[8]
q[9] <= dffpipe_c09:dffpipe15.q[9]
q[10] <= dffpipe_c09:dffpipe15.q[10]


|TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15
clock => dffe16a[10].CLK
clock => dffe16a[9].CLK
clock => dffe16a[8].CLK
clock => dffe16a[7].CLK
clock => dffe16a[6].CLK
clock => dffe16a[5].CLK
clock => dffe16a[4].CLK
clock => dffe16a[3].CLK
clock => dffe16a[2].CLK
clock => dffe16a[1].CLK
clock => dffe16a[0].CLK
clock => dffe17a[10].CLK
clock => dffe17a[9].CLK
clock => dffe17a[8].CLK
clock => dffe17a[7].CLK
clock => dffe17a[6].CLK
clock => dffe17a[5].CLK
clock => dffe17a[4].CLK
clock => dffe17a[3].CLK
clock => dffe17a[2].CLK
clock => dffe17a[1].CLK
clock => dffe17a[0].CLK
d[0] => dffe16a[0].IN0
d[1] => dffe16a[1].IN0
d[2] => dffe16a[2].IN0
d[3] => dffe16a[3].IN0
d[4] => dffe16a[4].IN0
d[5] => dffe16a[5].IN0
d[6] => dffe16a[6].IN0
d[7] => dffe16a[7].IN0
d[8] => dffe16a[8].IN0
d[9] => dffe16a[9].IN0
d[10] => dffe16a[10].IN0
q[0] <= dffe17a[0].DB_MAX_OUTPUT_PORT_TYPE
q[1] <= dffe17a[1].DB_MAX_OUTPUT_PORT_TYPE
q[2] <= dffe17a[2].DB_MAX_OUTPUT_PORT_TYPE
q[3] <= dffe17a[3].DB_MAX_OUTPUT_PORT_TYPE
q[4] <= dffe17a[4].DB_MAX_OUTPUT_PORT_TYPE
q[5] <= dffe17a[5].DB_MAX_OUTPUT_PORT_TYPE
q[6] <= dffe17a[6].DB_MAX_OUTPUT_PORT_TYPE
q[7] <= dffe17a[7].DB_MAX_OUTPUT_PORT_TYPE
q[8] <= dffe17a[8].DB_MAX_OUTPUT_PORT_TYPE
q[9] <= dffe17a[9].DB_MAX_OUTPUT_PORT_TYPE
q[10] <= dffe17a[10].DB_MAX_OUTPUT_PORT_TYPE


|TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|cmpr_o76:rdempty_eq_comp
aeb <= aeb_result_wire[0].DB_MAX_OUTPUT_PORT_TYPE
dataa[0] => data_wire[2].IN0
dataa[1] => data_wire[2].IN0
dataa[2] => data_wire[3].IN0
dataa[3] => data_wire[3].IN0
dataa[4] => data_wire[4].IN0
dataa[5] => data_wire[4].IN0
dataa[6] => data_wire[5].IN0
dataa[7] => data_wire[5].IN0
dataa[8] => data_wire[6].IN0
dataa[9] => data_wire[6].IN0
dataa[10] => data_wire[7].IN0
datab[0] => data_wire[2].IN1
datab[1] => data_wire[2].IN1
datab[2] => data_wire[3].IN1
datab[3] => data_wire[3].IN1
datab[4] => data_wire[4].IN1
datab[5] => data_wire[4].IN1
datab[6] => data_wire[5].IN1
datab[7] => data_wire[5].IN1
datab[8] => data_wire[6].IN1
datab[9] => data_wire[6].IN1
datab[10] => data_wire[7].IN1


|TOP|TYFIFO:u_AD1_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|cmpr_o76:wrfull_eq_comp
aeb <= aeb_result_wire[0].DB_MAX_OUTPUT_PORT_TYPE
dataa[0] => data_wire[2].IN0
dataa[1] => data_wire[2].IN0
dataa[2] => data_wire[3].IN0
dataa[3] => data_wire[3].IN0
dataa[4] => data_wire[4].IN0
dataa[5] => data_wire[4].IN0
dataa[6] => data_wire[5].IN0
dataa[7] => data_wire[5].IN0
dataa[8] => data_wire[6].IN0
dataa[9] => data_wire[6].IN0
dataa[10] => data_wire[7].IN0
datab[0] => data_wire[2].IN1
datab[1] => data_wire[2].IN1
datab[2] => data_wire[3].IN1
datab[3] => data_wire[3].IN1
datab[4] => data_wire[4].IN1
datab[5] => data_wire[4].IN1
datab[6] => data_wire[5].IN1
datab[7] => data_wire[5].IN1
datab[8] => data_wire[6].IN1
datab[9] => data_wire[6].IN1
datab[10] => data_wire[7].IN1


|TOP|FREQ_DEV:u_AD1_DEV
CLK => FREQ_WORD[0].CLK
CLK => FREQ_WORD[1].CLK
CLK => FREQ_WORD[2].CLK
CLK => FREQ_WORD[3].CLK
CLK => FREQ_WORD[4].CLK
CLK => FREQ_WORD[5].CLK
CLK => FREQ_WORD[6].CLK
CLK => FREQ_WORD[7].CLK
CLK => FREQ_WORD[8].CLK
CLK => FREQ_WORD[9].CLK
CLK => FREQ_WORD[10].CLK
CLK => FREQ_WORD[11].CLK
CLK => FREQ_WORD[12].CLK
CLK => FREQ_WORD[13].CLK
CLK => FREQ_WORD[14].CLK
CLK => FREQ_WORD[15].CLK
CLK => FREQ_WORD[16].CLK
CLK => FREQ_WORD[17].CLK
CLK => FREQ_WORD[18].CLK
CLK => FREQ_WORD[19].CLK
CLK => FREQ_WORD[20].CLK
CLK => FREQ_WORD[21].CLK
CLK => FREQ_WORD[22].CLK
CLK => FREQ_WORD[23].CLK
CLK => FREQ_WORD[24].CLK
CLK => FREQ_WORD[25].CLK
CLK => FREQ_WORD[26].CLK
CLK => FREQ_WORD[27].CLK
CLK => FREQ_WORD[28].CLK
CLK => FREQ_WORD[29].CLK
CLK => FREQ_WORD[30].CLK
CLK => FREQ_WORD[31].CLK
CLK => FREQ_OUT~reg0.CLK
CLK => ACC[0].CLK
CLK => ACC[1].CLK
CLK => ACC[2].CLK
CLK => ACC[3].CLK
CLK => ACC[4].CLK
CLK => ACC[5].CLK
CLK => ACC[6].CLK
CLK => ACC[7].CLK
CLK => ACC[8].CLK
CLK => ACC[9].CLK
CLK => ACC[10].CLK
CLK => ACC[11].CLK
CLK => ACC[12].CLK
CLK => ACC[13].CLK
CLK => ACC[14].CLK
CLK => ACC[15].CLK
CLK => ACC[16].CLK
CLK => ACC[17].CLK
CLK => ACC[18].CLK
CLK => ACC[19].CLK
CLK => ACC[20].CLK
CLK => ACC[21].CLK
CLK => ACC[22].CLK
CLK => ACC[23].CLK
CLK => ACC[24].CLK
CLK => ACC[25].CLK
CLK => ACC[26].CLK
CLK => ACC[27].CLK
CLK => ACC[28].CLK
CLK => ACC[29].CLK
CLK => ACC[30].CLK
CLK => ACC[31].CLK
EN => ACC[0].ENA
EN => ACC[1].ENA
EN => ACC[2].ENA
EN => ACC[3].ENA
EN => ACC[4].ENA
EN => ACC[5].ENA
EN => ACC[6].ENA
EN => ACC[7].ENA
EN => ACC[8].ENA
EN => ACC[9].ENA
EN => ACC[10].ENA
EN => ACC[11].ENA
EN => ACC[12].ENA
EN => ACC[13].ENA
EN => ACC[14].ENA
EN => ACC[15].ENA
EN => ACC[16].ENA
EN => ACC[17].ENA
EN => ACC[18].ENA
EN => ACC[19].ENA
EN => ACC[20].ENA
EN => ACC[21].ENA
EN => ACC[22].ENA
EN => ACC[23].ENA
EN => ACC[24].ENA
EN => ACC[25].ENA
EN => ACC[26].ENA
EN => ACC[27].ENA
EN => ACC[28].ENA
EN => ACC[29].ENA
EN => ACC[30].ENA
EN => ACC[31].ENA
FREQH_W[0] => FREQ_WORD[16].DATAIN
FREQH_W[1] => FREQ_WORD[17].DATAIN
FREQH_W[2] => FREQ_WORD[18].DATAIN
FREQH_W[3] => FREQ_WORD[19].DATAIN
FREQH_W[4] => FREQ_WORD[20].DATAIN
FREQH_W[5] => FREQ_WORD[21].DATAIN
FREQH_W[6] => FREQ_WORD[22].DATAIN
FREQH_W[7] => FREQ_WORD[23].DATAIN
FREQH_W[8] => FREQ_WORD[24].DATAIN
FREQH_W[9] => FREQ_WORD[25].DATAIN
FREQH_W[10] => FREQ_WORD[26].DATAIN
FREQH_W[11] => FREQ_WORD[27].DATAIN
FREQH_W[12] => FREQ_WORD[28].DATAIN
FREQH_W[13] => FREQ_WORD[29].DATAIN
FREQH_W[14] => FREQ_WORD[30].DATAIN
FREQH_W[15] => FREQ_WORD[31].DATAIN
FREQL_W[0] => FREQ_WORD[0].DATAIN
FREQL_W[1] => FREQ_WORD[1].DATAIN
FREQL_W[2] => FREQ_WORD[2].DATAIN
FREQL_W[3] => FREQ_WORD[3].DATAIN
FREQL_W[4] => FREQ_WORD[4].DATAIN
FREQL_W[5] => FREQ_WORD[5].DATAIN
FREQL_W[6] => FREQ_WORD[6].DATAIN
FREQL_W[7] => FREQ_WORD[7].DATAIN
FREQL_W[8] => FREQ_WORD[8].DATAIN
FREQL_W[9] => FREQ_WORD[9].DATAIN
FREQL_W[10] => FREQ_WORD[10].DATAIN
FREQL_W[11] => FREQ_WORD[11].DATAIN
FREQL_W[12] => FREQ_WORD[12].DATAIN
FREQL_W[13] => FREQ_WORD[13].DATAIN
FREQL_W[14] => FREQ_WORD[14].DATAIN
FREQL_W[15] => FREQ_WORD[15].DATAIN
FREQ_OUT <= FREQ_OUT~reg0.DB_MAX_OUTPUT_PORT_TYPE


|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD
CS => always0.IN0
WR_EN => always0.IN1
DATA0[0] => AD1_OUTH[0]$latch.DATAIN
DATA0[1] => AD1_OUTH[1]$latch.DATAIN
DATA0[2] => AD1_OUTH[2]$latch.DATAIN
DATA0[3] => AD1_OUTH[3]$latch.DATAIN
DATA0[4] => AD1_OUTH[4]$latch.DATAIN
DATA0[5] => AD1_OUTH[5]$latch.DATAIN
DATA0[6] => AD1_OUTH[6]$latch.DATAIN
DATA0[7] => AD1_OUTH[7]$latch.DATAIN
DATA0[8] => AD1_OUTH[8]$latch.DATAIN
DATA0[9] => AD1_OUTH[9]$latch.DATAIN
DATA0[10] => AD1_OUTH[10]$latch.DATAIN
DATA0[11] => AD1_OUTH[11]$latch.DATAIN
DATA0[12] => AD1_OUTH[12]$latch.DATAIN
DATA0[13] => AD1_OUTH[13]$latch.DATAIN
DATA0[14] => AD1_OUTH[14]$latch.DATAIN
DATA0[15] => AD1_OUTH[15]$latch.DATAIN
DATA1[0] => AD1_OUTL[0]$latch.DATAIN
DATA1[1] => AD1_OUTL[1]$latch.DATAIN
DATA1[2] => AD1_OUTL[2]$latch.DATAIN
DATA1[3] => AD1_OUTL[3]$latch.DATAIN
DATA1[4] => AD1_OUTL[4]$latch.DATAIN
DATA1[5] => AD1_OUTL[5]$latch.DATAIN
DATA1[6] => AD1_OUTL[6]$latch.DATAIN
DATA1[7] => AD1_OUTL[7]$latch.DATAIN
DATA1[8] => AD1_OUTL[8]$latch.DATAIN
DATA1[9] => AD1_OUTL[9]$latch.DATAIN
DATA1[10] => AD1_OUTL[10]$latch.DATAIN
DATA1[11] => AD1_OUTL[11]$latch.DATAIN
DATA1[12] => AD1_OUTL[12]$latch.DATAIN
DATA1[13] => AD1_OUTL[13]$latch.DATAIN
DATA1[14] => AD1_OUTL[14]$latch.DATAIN
DATA1[15] => AD1_OUTL[15]$latch.DATAIN
DATA2[0] => AD2_OUTH[0]$latch.DATAIN
DATA2[1] => AD2_OUTH[1]$latch.DATAIN
DATA2[2] => AD2_OUTH[2]$latch.DATAIN
DATA2[3] => AD2_OUTH[3]$latch.DATAIN
DATA2[4] => AD2_OUTH[4]$latch.DATAIN
DATA2[5] => AD2_OUTH[5]$latch.DATAIN
DATA2[6] => AD2_OUTH[6]$latch.DATAIN
DATA2[7] => AD2_OUTH[7]$latch.DATAIN
DATA2[8] => AD2_OUTH[8]$latch.DATAIN
DATA2[9] => AD2_OUTH[9]$latch.DATAIN
DATA2[10] => AD2_OUTH[10]$latch.DATAIN
DATA2[11] => AD2_OUTH[11]$latch.DATAIN
DATA2[12] => AD2_OUTH[12]$latch.DATAIN
DATA2[13] => AD2_OUTH[13]$latch.DATAIN
DATA2[14] => AD2_OUTH[14]$latch.DATAIN
DATA2[15] => AD2_OUTH[15]$latch.DATAIN
DATA3[0] => AD2_OUTL[0]$latch.DATAIN
DATA3[1] => AD2_OUTL[1]$latch.DATAIN
DATA3[2] => AD2_OUTL[2]$latch.DATAIN
DATA3[3] => AD2_OUTL[3]$latch.DATAIN
DATA3[4] => AD2_OUTL[4]$latch.DATAIN
DATA3[5] => AD2_OUTL[5]$latch.DATAIN
DATA3[6] => AD2_OUTL[6]$latch.DATAIN
DATA3[7] => AD2_OUTL[7]$latch.DATAIN
DATA3[8] => AD2_OUTL[8]$latch.DATAIN
DATA3[9] => AD2_OUTL[9]$latch.DATAIN
DATA3[10] => AD2_OUTL[10]$latch.DATAIN
DATA3[11] => AD2_OUTL[11]$latch.DATAIN
DATA3[12] => AD2_OUTL[12]$latch.DATAIN
DATA3[13] => AD2_OUTL[13]$latch.DATAIN
DATA3[14] => AD2_OUTL[14]$latch.DATAIN
DATA3[15] => AD2_OUTL[15]$latch.DATAIN
ADDR[0] => Equal0.IN31
ADDR[0] => Equal1.IN31
ADDR[0] => Equal2.IN31
ADDR[0] => Equal3.IN31
ADDR[1] => Equal0.IN30
ADDR[1] => Equal1.IN30
ADDR[1] => Equal2.IN30
ADDR[1] => Equal3.IN30
ADDR[2] => Equal0.IN29
ADDR[2] => Equal1.IN29
ADDR[2] => Equal2.IN29
ADDR[2] => Equal3.IN29
ADDR[3] => Equal0.IN28
ADDR[3] => Equal1.IN28
ADDR[3] => Equal2.IN28
ADDR[3] => Equal3.IN28
ADDR[4] => Equal0.IN27
ADDR[4] => Equal1.IN27
ADDR[4] => Equal2.IN27
ADDR[4] => Equal3.IN27
ADDR[5] => Equal0.IN26
ADDR[5] => Equal1.IN26
ADDR[5] => Equal2.IN26
ADDR[5] => Equal3.IN26
ADDR[6] => Equal0.IN25
ADDR[6] => Equal1.IN25
ADDR[6] => Equal2.IN25
ADDR[6] => Equal3.IN25
ADDR[7] => Equal0.IN24
ADDR[7] => Equal1.IN24
ADDR[7] => Equal2.IN24
ADDR[7] => Equal3.IN24
ADDR[8] => Equal0.IN23
ADDR[8] => Equal1.IN23
ADDR[8] => Equal2.IN23
ADDR[8] => Equal3.IN23
ADDR[9] => Equal0.IN22
ADDR[9] => Equal1.IN22
ADDR[9] => Equal2.IN22
ADDR[9] => Equal3.IN22
ADDR[10] => Equal0.IN21
ADDR[10] => Equal1.IN21
ADDR[10] => Equal2.IN21
ADDR[10] => Equal3.IN21
ADDR[11] => Equal0.IN20
ADDR[11] => Equal1.IN20
ADDR[11] => Equal2.IN20
ADDR[11] => Equal3.IN20
ADDR[12] => Equal0.IN19
ADDR[12] => Equal1.IN19
ADDR[12] => Equal2.IN19
ADDR[12] => Equal3.IN19
ADDR[13] => Equal0.IN18
ADDR[13] => Equal1.IN18
ADDR[13] => Equal2.IN18
ADDR[13] => Equal3.IN18
ADDR[14] => Equal0.IN17
ADDR[14] => Equal1.IN17
ADDR[14] => Equal2.IN17
ADDR[14] => Equal3.IN17
ADDR[15] => Equal0.IN16
ADDR[15] => Equal1.IN16
ADDR[15] => Equal2.IN16
ADDR[15] => Equal3.IN16
AD1_OUTH[0] <= AD1_OUTH[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[1] <= AD1_OUTH[1]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[2] <= AD1_OUTH[2]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[3] <= AD1_OUTH[3]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[4] <= AD1_OUTH[4]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[5] <= AD1_OUTH[5]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[6] <= AD1_OUTH[6]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[7] <= AD1_OUTH[7]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[8] <= AD1_OUTH[8]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[9] <= AD1_OUTH[9]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[10] <= AD1_OUTH[10]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[11] <= AD1_OUTH[11]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[12] <= AD1_OUTH[12]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[13] <= AD1_OUTH[13]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[14] <= AD1_OUTH[14]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTH[15] <= AD1_OUTH[15]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[0] <= AD1_OUTL[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[1] <= AD1_OUTL[1]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[2] <= AD1_OUTL[2]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[3] <= AD1_OUTL[3]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[4] <= AD1_OUTL[4]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[5] <= AD1_OUTL[5]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[6] <= AD1_OUTL[6]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[7] <= AD1_OUTL[7]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[8] <= AD1_OUTL[8]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[9] <= AD1_OUTL[9]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[10] <= AD1_OUTL[10]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[11] <= AD1_OUTL[11]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[12] <= AD1_OUTL[12]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[13] <= AD1_OUTL[13]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[14] <= AD1_OUTL[14]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD1_OUTL[15] <= AD1_OUTL[15]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[0] <= AD2_OUTH[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[1] <= AD2_OUTH[1]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[2] <= AD2_OUTH[2]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[3] <= AD2_OUTH[3]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[4] <= AD2_OUTH[4]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[5] <= AD2_OUTH[5]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[6] <= AD2_OUTH[6]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[7] <= AD2_OUTH[7]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[8] <= AD2_OUTH[8]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[9] <= AD2_OUTH[9]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[10] <= AD2_OUTH[10]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[11] <= AD2_OUTH[11]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[12] <= AD2_OUTH[12]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[13] <= AD2_OUTH[13]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[14] <= AD2_OUTH[14]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTH[15] <= AD2_OUTH[15]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[0] <= AD2_OUTL[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[1] <= AD2_OUTL[1]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[2] <= AD2_OUTL[2]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[3] <= AD2_OUTL[3]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[4] <= AD2_OUTL[4]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[5] <= AD2_OUTL[5]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[6] <= AD2_OUTL[6]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[7] <= AD2_OUTL[7]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[8] <= AD2_OUTL[8]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[9] <= AD2_OUTL[9]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[10] <= AD2_OUTL[10]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[11] <= AD2_OUTL[11]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[12] <= AD2_OUTL[12]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[13] <= AD2_OUTL[13]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[14] <= AD2_OUTL[14]$latch.DB_MAX_OUTPUT_PORT_TYPE
AD2_OUTL[15] <= AD2_OUTL[15]$latch.DB_MAX_OUTPUT_PORT_TYPE


|TOP|TYFIFO:u_AD2_FIFO
data[0] => data[0].IN1
data[1] => data[1].IN1
data[2] => data[2].IN1
data[3] => data[3].IN1
data[4] => data[4].IN1
data[5] => data[5].IN1
data[6] => data[6].IN1
data[7] => data[7].IN1
data[8] => data[8].IN1
data[9] => data[9].IN1
data[10] => data[10].IN1
data[11] => data[11].IN1
rdclk => rdclk.IN1
rdreq => rdreq.IN1
wrclk => wrclk.IN1
wrreq => wrreq.IN1
q[0] <= dcfifo:dcfifo_component.q
q[1] <= dcfifo:dcfifo_component.q
q[2] <= dcfifo:dcfifo_component.q
q[3] <= dcfifo:dcfifo_component.q
q[4] <= dcfifo:dcfifo_component.q
q[5] <= dcfifo:dcfifo_component.q
q[6] <= dcfifo:dcfifo_component.q
q[7] <= dcfifo:dcfifo_component.q
q[8] <= dcfifo:dcfifo_component.q
q[9] <= dcfifo:dcfifo_component.q
q[10] <= dcfifo:dcfifo_component.q
q[11] <= dcfifo:dcfifo_component.q
wrfull <= dcfifo:dcfifo_component.wrfull


|TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component
data[0] => dcfifo_vve1:auto_generated.data[0]
data[1] => dcfifo_vve1:auto_generated.data[1]
data[2] => dcfifo_vve1:auto_generated.data[2]
data[3] => dcfifo_vve1:auto_generated.data[3]
data[4] => dcfifo_vve1:auto_generated.data[4]
data[5] => dcfifo_vve1:auto_generated.data[5]
data[6] => dcfifo_vve1:auto_generated.data[6]
data[7] => dcfifo_vve1:auto_generated.data[7]
data[8] => dcfifo_vve1:auto_generated.data[8]
data[9] => dcfifo_vve1:auto_generated.data[9]
data[10] => dcfifo_vve1:auto_generated.data[10]
data[11] => dcfifo_vve1:auto_generated.data[11]
q[0] <= dcfifo_vve1:auto_generated.q[0]
q[1] <= dcfifo_vve1:auto_generated.q[1]
q[2] <= dcfifo_vve1:auto_generated.q[2]
q[3] <= dcfifo_vve1:auto_generated.q[3]
q[4] <= dcfifo_vve1:auto_generated.q[4]
q[5] <= dcfifo_vve1:auto_generated.q[5]
q[6] <= dcfifo_vve1:auto_generated.q[6]
q[7] <= dcfifo_vve1:auto_generated.q[7]
q[8] <= dcfifo_vve1:auto_generated.q[8]
q[9] <= dcfifo_vve1:auto_generated.q[9]
q[10] <= dcfifo_vve1:auto_generated.q[10]
q[11] <= dcfifo_vve1:auto_generated.q[11]
eccstatus[0] <= <GND>
eccstatus[1] <= <GND>
rdclk => dcfifo_vve1:auto_generated.rdclk
rdreq => dcfifo_vve1:auto_generated.rdreq
wrclk => dcfifo_vve1:auto_generated.wrclk
wrreq => dcfifo_vve1:auto_generated.wrreq
aclr => ~NO_FANOUT~
rdempty <= <UNC>
rdfull <= <UNC>
wrempty <= <GND>
wrfull <= dcfifo_vve1:auto_generated.wrfull
rdusedw[0] <= <UNC>
rdusedw[1] <= <UNC>
rdusedw[2] <= <UNC>
rdusedw[3] <= <UNC>
rdusedw[4] <= <UNC>
rdusedw[5] <= <UNC>
rdusedw[6] <= <UNC>
rdusedw[7] <= <UNC>
rdusedw[8] <= <UNC>
rdusedw[9] <= <UNC>
wrusedw[0] <= <GND>
wrusedw[1] <= <GND>
wrusedw[2] <= <GND>
wrusedw[3] <= <GND>
wrusedw[4] <= <GND>
wrusedw[5] <= <GND>
wrusedw[6] <= <GND>
wrusedw[7] <= <GND>
wrusedw[8] <= <GND>
wrusedw[9] <= <GND>


|TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated
data[0] => altsyncram_ce41:fifo_ram.data_a[0]
data[1] => altsyncram_ce41:fifo_ram.data_a[1]
data[2] => altsyncram_ce41:fifo_ram.data_a[2]
data[3] => altsyncram_ce41:fifo_ram.data_a[3]
data[4] => altsyncram_ce41:fifo_ram.data_a[4]
data[5] => altsyncram_ce41:fifo_ram.data_a[5]
data[6] => altsyncram_ce41:fifo_ram.data_a[6]
data[7] => altsyncram_ce41:fifo_ram.data_a[7]
data[8] => altsyncram_ce41:fifo_ram.data_a[8]
data[9] => altsyncram_ce41:fifo_ram.data_a[9]
data[10] => altsyncram_ce41:fifo_ram.data_a[10]
data[11] => altsyncram_ce41:fifo_ram.data_a[11]
q[0] <= altsyncram_ce41:fifo_ram.q_b[0]
q[1] <= altsyncram_ce41:fifo_ram.q_b[1]
q[2] <= altsyncram_ce41:fifo_ram.q_b[2]
q[3] <= altsyncram_ce41:fifo_ram.q_b[3]
q[4] <= altsyncram_ce41:fifo_ram.q_b[4]
q[5] <= altsyncram_ce41:fifo_ram.q_b[5]
q[6] <= altsyncram_ce41:fifo_ram.q_b[6]
q[7] <= altsyncram_ce41:fifo_ram.q_b[7]
q[8] <= altsyncram_ce41:fifo_ram.q_b[8]
q[9] <= altsyncram_ce41:fifo_ram.q_b[9]
q[10] <= altsyncram_ce41:fifo_ram.q_b[10]
q[11] <= altsyncram_ce41:fifo_ram.q_b[11]
rdclk => a_graycounter_4p6:rdptr_g1p.clock
rdclk => altsyncram_ce41:fifo_ram.clock1
rdclk => alt_synch_pipe_qal:rs_dgwp.clock
rdclk => rdptr_g[10].CLK
rdclk => rdptr_g[9].CLK
rdclk => rdptr_g[8].CLK
rdclk => rdptr_g[7].CLK
rdclk => rdptr_g[6].CLK
rdclk => rdptr_g[5].CLK
rdclk => rdptr_g[4].CLK
rdclk => rdptr_g[3].CLK
rdclk => rdptr_g[2].CLK
rdclk => rdptr_g[1].CLK
rdclk => rdptr_g[0].CLK
rdreq => valid_rdreq.IN0
wrclk => a_graycounter_07c:wrptr_g1p.clock
wrclk => altsyncram_ce41:fifo_ram.clock0
wrclk => alt_synch_pipe_ral:ws_dgrp.clock
wrclk => delayed_wrptr_g[10].CLK
wrclk => delayed_wrptr_g[9].CLK
wrclk => delayed_wrptr_g[8].CLK
wrclk => delayed_wrptr_g[7].CLK
wrclk => delayed_wrptr_g[6].CLK
wrclk => delayed_wrptr_g[5].CLK
wrclk => delayed_wrptr_g[4].CLK
wrclk => delayed_wrptr_g[3].CLK
wrclk => delayed_wrptr_g[2].CLK
wrclk => delayed_wrptr_g[1].CLK
wrclk => delayed_wrptr_g[0].CLK
wrclk => wrptr_g[10].CLK
wrclk => wrptr_g[9].CLK
wrclk => wrptr_g[8].CLK
wrclk => wrptr_g[7].CLK
wrclk => wrptr_g[6].CLK
wrclk => wrptr_g[5].CLK
wrclk => wrptr_g[4].CLK
wrclk => wrptr_g[3].CLK
wrclk => wrptr_g[2].CLK
wrclk => wrptr_g[1].CLK
wrclk => wrptr_g[0].CLK
wrfull <= int_wrfull.DB_MAX_OUTPUT_PORT_TYPE
wrreq => valid_wrreq.IN0


|TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_4p6:rdptr_g1p
clock => counter5a0.CLK
clock => counter5a1.CLK
clock => counter5a2.CLK
clock => counter5a3.CLK
clock => counter5a4.CLK
clock => counter5a5.CLK
clock => counter5a6.CLK
clock => counter5a7.CLK
clock => counter5a8.CLK
clock => counter5a9.CLK
clock => counter5a10.CLK
clock => parity6.CLK
clock => sub_parity7a[2].CLK
clock => sub_parity7a[1].CLK
clock => sub_parity7a[0].CLK
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => cntr_cout[0].IN0
cnt_en => parity_cout.IN1
q[0] <= counter5a0.DB_MAX_OUTPUT_PORT_TYPE
q[1] <= counter5a1.DB_MAX_OUTPUT_PORT_TYPE
q[2] <= counter5a2.DB_MAX_OUTPUT_PORT_TYPE
q[3] <= counter5a3.DB_MAX_OUTPUT_PORT_TYPE
q[4] <= counter5a4.DB_MAX_OUTPUT_PORT_TYPE
q[5] <= counter5a5.DB_MAX_OUTPUT_PORT_TYPE
q[6] <= counter5a6.DB_MAX_OUTPUT_PORT_TYPE
q[7] <= counter5a7.DB_MAX_OUTPUT_PORT_TYPE
q[8] <= counter5a8.DB_MAX_OUTPUT_PORT_TYPE
q[9] <= counter5a9.DB_MAX_OUTPUT_PORT_TYPE
q[10] <= counter5a10.DB_MAX_OUTPUT_PORT_TYPE


|TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|a_graycounter_07c:wrptr_g1p
clock => counter8a0.CLK
clock => counter8a1.CLK
clock => counter8a2.CLK
clock => counter8a3.CLK
clock => counter8a4.CLK
clock => counter8a5.CLK
clock => counter8a6.CLK
clock => counter8a7.CLK
clock => counter8a8.CLK
clock => counter8a9.CLK
clock => counter8a10.CLK
clock => parity9.CLK
clock => sub_parity10a[2].CLK
clock => sub_parity10a[1].CLK
clock => sub_parity10a[0].CLK
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => _.IN0
cnt_en => cntr_cout[0].IN0
cnt_en => parity_cout.IN1
q[0] <= counter8a0.DB_MAX_OUTPUT_PORT_TYPE
q[1] <= counter8a1.DB_MAX_OUTPUT_PORT_TYPE
q[2] <= counter8a2.DB_MAX_OUTPUT_PORT_TYPE
q[3] <= counter8a3.DB_MAX_OUTPUT_PORT_TYPE
q[4] <= counter8a4.DB_MAX_OUTPUT_PORT_TYPE
q[5] <= counter8a5.DB_MAX_OUTPUT_PORT_TYPE
q[6] <= counter8a6.DB_MAX_OUTPUT_PORT_TYPE
q[7] <= counter8a7.DB_MAX_OUTPUT_PORT_TYPE
q[8] <= counter8a8.DB_MAX_OUTPUT_PORT_TYPE
q[9] <= counter8a9.DB_MAX_OUTPUT_PORT_TYPE
q[10] <= counter8a10.DB_MAX_OUTPUT_PORT_TYPE


|TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|altsyncram_ce41:fifo_ram
address_a[0] => ram_block11a0.PORTAADDR
address_a[0] => ram_block11a1.PORTAADDR
address_a[0] => ram_block11a2.PORTAADDR
address_a[0] => ram_block11a3.PORTAADDR
address_a[0] => ram_block11a4.PORTAADDR
address_a[0] => ram_block11a5.PORTAADDR
address_a[0] => ram_block11a6.PORTAADDR
address_a[0] => ram_block11a7.PORTAADDR
address_a[0] => ram_block11a8.PORTAADDR
address_a[0] => ram_block11a9.PORTAADDR
address_a[0] => ram_block11a10.PORTAADDR
address_a[0] => ram_block11a11.PORTAADDR
address_a[1] => ram_block11a0.PORTAADDR1
address_a[1] => ram_block11a1.PORTAADDR1
address_a[1] => ram_block11a2.PORTAADDR1
address_a[1] => ram_block11a3.PORTAADDR1
address_a[1] => ram_block11a4.PORTAADDR1
address_a[1] => ram_block11a5.PORTAADDR1
address_a[1] => ram_block11a6.PORTAADDR1
address_a[1] => ram_block11a7.PORTAADDR1
address_a[1] => ram_block11a8.PORTAADDR1
address_a[1] => ram_block11a9.PORTAADDR1
address_a[1] => ram_block11a10.PORTAADDR1
address_a[1] => ram_block11a11.PORTAADDR1
address_a[2] => ram_block11a0.PORTAADDR2
address_a[2] => ram_block11a1.PORTAADDR2
address_a[2] => ram_block11a2.PORTAADDR2
address_a[2] => ram_block11a3.PORTAADDR2
address_a[2] => ram_block11a4.PORTAADDR2
address_a[2] => ram_block11a5.PORTAADDR2
address_a[2] => ram_block11a6.PORTAADDR2
address_a[2] => ram_block11a7.PORTAADDR2
address_a[2] => ram_block11a8.PORTAADDR2
address_a[2] => ram_block11a9.PORTAADDR2
address_a[2] => ram_block11a10.PORTAADDR2
address_a[2] => ram_block11a11.PORTAADDR2
address_a[3] => ram_block11a0.PORTAADDR3
address_a[3] => ram_block11a1.PORTAADDR3
address_a[3] => ram_block11a2.PORTAADDR3
address_a[3] => ram_block11a3.PORTAADDR3
address_a[3] => ram_block11a4.PORTAADDR3
address_a[3] => ram_block11a5.PORTAADDR3
address_a[3] => ram_block11a6.PORTAADDR3
address_a[3] => ram_block11a7.PORTAADDR3
address_a[3] => ram_block11a8.PORTAADDR3
address_a[3] => ram_block11a9.PORTAADDR3
address_a[3] => ram_block11a10.PORTAADDR3
address_a[3] => ram_block11a11.PORTAADDR3
address_a[4] => ram_block11a0.PORTAADDR4
address_a[4] => ram_block11a1.PORTAADDR4
address_a[4] => ram_block11a2.PORTAADDR4
address_a[4] => ram_block11a3.PORTAADDR4
address_a[4] => ram_block11a4.PORTAADDR4
address_a[4] => ram_block11a5.PORTAADDR4
address_a[4] => ram_block11a6.PORTAADDR4
address_a[4] => ram_block11a7.PORTAADDR4
address_a[4] => ram_block11a8.PORTAADDR4
address_a[4] => ram_block11a9.PORTAADDR4
address_a[4] => ram_block11a10.PORTAADDR4
address_a[4] => ram_block11a11.PORTAADDR4
address_a[5] => ram_block11a0.PORTAADDR5
address_a[5] => ram_block11a1.PORTAADDR5
address_a[5] => ram_block11a2.PORTAADDR5
address_a[5] => ram_block11a3.PORTAADDR5
address_a[5] => ram_block11a4.PORTAADDR5
address_a[5] => ram_block11a5.PORTAADDR5
address_a[5] => ram_block11a6.PORTAADDR5
address_a[5] => ram_block11a7.PORTAADDR5
address_a[5] => ram_block11a8.PORTAADDR5
address_a[5] => ram_block11a9.PORTAADDR5
address_a[5] => ram_block11a10.PORTAADDR5
address_a[5] => ram_block11a11.PORTAADDR5
address_a[6] => ram_block11a0.PORTAADDR6
address_a[6] => ram_block11a1.PORTAADDR6
address_a[6] => ram_block11a2.PORTAADDR6
address_a[6] => ram_block11a3.PORTAADDR6
address_a[6] => ram_block11a4.PORTAADDR6
address_a[6] => ram_block11a5.PORTAADDR6
address_a[6] => ram_block11a6.PORTAADDR6
address_a[6] => ram_block11a7.PORTAADDR6
address_a[6] => ram_block11a8.PORTAADDR6
address_a[6] => ram_block11a9.PORTAADDR6
address_a[6] => ram_block11a10.PORTAADDR6
address_a[6] => ram_block11a11.PORTAADDR6
address_a[7] => ram_block11a0.PORTAADDR7
address_a[7] => ram_block11a1.PORTAADDR7
address_a[7] => ram_block11a2.PORTAADDR7
address_a[7] => ram_block11a3.PORTAADDR7
address_a[7] => ram_block11a4.PORTAADDR7
address_a[7] => ram_block11a5.PORTAADDR7
address_a[7] => ram_block11a6.PORTAADDR7
address_a[7] => ram_block11a7.PORTAADDR7
address_a[7] => ram_block11a8.PORTAADDR7
address_a[7] => ram_block11a9.PORTAADDR7
address_a[7] => ram_block11a10.PORTAADDR7
address_a[7] => ram_block11a11.PORTAADDR7
address_a[8] => ram_block11a0.PORTAADDR8
address_a[8] => ram_block11a1.PORTAADDR8
address_a[8] => ram_block11a2.PORTAADDR8
address_a[8] => ram_block11a3.PORTAADDR8
address_a[8] => ram_block11a4.PORTAADDR8
address_a[8] => ram_block11a5.PORTAADDR8
address_a[8] => ram_block11a6.PORTAADDR8
address_a[8] => ram_block11a7.PORTAADDR8
address_a[8] => ram_block11a8.PORTAADDR8
address_a[8] => ram_block11a9.PORTAADDR8
address_a[8] => ram_block11a10.PORTAADDR8
address_a[8] => ram_block11a11.PORTAADDR8
address_a[9] => ram_block11a0.PORTAADDR9
address_a[9] => ram_block11a1.PORTAADDR9
address_a[9] => ram_block11a2.PORTAADDR9
address_a[9] => ram_block11a3.PORTAADDR9
address_a[9] => ram_block11a4.PORTAADDR9
address_a[9] => ram_block11a5.PORTAADDR9
address_a[9] => ram_block11a6.PORTAADDR9
address_a[9] => ram_block11a7.PORTAADDR9
address_a[9] => ram_block11a8.PORTAADDR9
address_a[9] => ram_block11a9.PORTAADDR9
address_a[9] => ram_block11a10.PORTAADDR9
address_a[9] => ram_block11a11.PORTAADDR9
address_b[0] => ram_block11a0.PORTBADDR
address_b[0] => ram_block11a1.PORTBADDR
address_b[0] => ram_block11a2.PORTBADDR
address_b[0] => ram_block11a3.PORTBADDR
address_b[0] => ram_block11a4.PORTBADDR
address_b[0] => ram_block11a5.PORTBADDR
address_b[0] => ram_block11a6.PORTBADDR
address_b[0] => ram_block11a7.PORTBADDR
address_b[0] => ram_block11a8.PORTBADDR
address_b[0] => ram_block11a9.PORTBADDR
address_b[0] => ram_block11a10.PORTBADDR
address_b[0] => ram_block11a11.PORTBADDR
address_b[1] => ram_block11a0.PORTBADDR1
address_b[1] => ram_block11a1.PORTBADDR1
address_b[1] => ram_block11a2.PORTBADDR1
address_b[1] => ram_block11a3.PORTBADDR1
address_b[1] => ram_block11a4.PORTBADDR1
address_b[1] => ram_block11a5.PORTBADDR1
address_b[1] => ram_block11a6.PORTBADDR1
address_b[1] => ram_block11a7.PORTBADDR1
address_b[1] => ram_block11a8.PORTBADDR1
address_b[1] => ram_block11a9.PORTBADDR1
address_b[1] => ram_block11a10.PORTBADDR1
address_b[1] => ram_block11a11.PORTBADDR1
address_b[2] => ram_block11a0.PORTBADDR2
address_b[2] => ram_block11a1.PORTBADDR2
address_b[2] => ram_block11a2.PORTBADDR2
address_b[2] => ram_block11a3.PORTBADDR2
address_b[2] => ram_block11a4.PORTBADDR2
address_b[2] => ram_block11a5.PORTBADDR2
address_b[2] => ram_block11a6.PORTBADDR2
address_b[2] => ram_block11a7.PORTBADDR2
address_b[2] => ram_block11a8.PORTBADDR2
address_b[2] => ram_block11a9.PORTBADDR2
address_b[2] => ram_block11a10.PORTBADDR2
address_b[2] => ram_block11a11.PORTBADDR2
address_b[3] => ram_block11a0.PORTBADDR3
address_b[3] => ram_block11a1.PORTBADDR3
address_b[3] => ram_block11a2.PORTBADDR3
address_b[3] => ram_block11a3.PORTBADDR3
address_b[3] => ram_block11a4.PORTBADDR3
address_b[3] => ram_block11a5.PORTBADDR3
address_b[3] => ram_block11a6.PORTBADDR3
address_b[3] => ram_block11a7.PORTBADDR3
address_b[3] => ram_block11a8.PORTBADDR3
address_b[3] => ram_block11a9.PORTBADDR3
address_b[3] => ram_block11a10.PORTBADDR3
address_b[3] => ram_block11a11.PORTBADDR3
address_b[4] => ram_block11a0.PORTBADDR4
address_b[4] => ram_block11a1.PORTBADDR4
address_b[4] => ram_block11a2.PORTBADDR4
address_b[4] => ram_block11a3.PORTBADDR4
address_b[4] => ram_block11a4.PORTBADDR4
address_b[4] => ram_block11a5.PORTBADDR4
address_b[4] => ram_block11a6.PORTBADDR4
address_b[4] => ram_block11a7.PORTBADDR4
address_b[4] => ram_block11a8.PORTBADDR4
address_b[4] => ram_block11a9.PORTBADDR4
address_b[4] => ram_block11a10.PORTBADDR4
address_b[4] => ram_block11a11.PORTBADDR4
address_b[5] => ram_block11a0.PORTBADDR5
address_b[5] => ram_block11a1.PORTBADDR5
address_b[5] => ram_block11a2.PORTBADDR5
address_b[5] => ram_block11a3.PORTBADDR5
address_b[5] => ram_block11a4.PORTBADDR5
address_b[5] => ram_block11a5.PORTBADDR5
address_b[5] => ram_block11a6.PORTBADDR5
address_b[5] => ram_block11a7.PORTBADDR5
address_b[5] => ram_block11a8.PORTBADDR5
address_b[5] => ram_block11a9.PORTBADDR5
address_b[5] => ram_block11a10.PORTBADDR5
address_b[5] => ram_block11a11.PORTBADDR5
address_b[6] => ram_block11a0.PORTBADDR6
address_b[6] => ram_block11a1.PORTBADDR6
address_b[6] => ram_block11a2.PORTBADDR6
address_b[6] => ram_block11a3.PORTBADDR6
address_b[6] => ram_block11a4.PORTBADDR6
address_b[6] => ram_block11a5.PORTBADDR6
address_b[6] => ram_block11a6.PORTBADDR6
address_b[6] => ram_block11a7.PORTBADDR6
address_b[6] => ram_block11a8.PORTBADDR6
address_b[6] => ram_block11a9.PORTBADDR6
address_b[6] => ram_block11a10.PORTBADDR6
address_b[6] => ram_block11a11.PORTBADDR6
address_b[7] => ram_block11a0.PORTBADDR7
address_b[7] => ram_block11a1.PORTBADDR7
address_b[7] => ram_block11a2.PORTBADDR7
address_b[7] => ram_block11a3.PORTBADDR7
address_b[7] => ram_block11a4.PORTBADDR7
address_b[7] => ram_block11a5.PORTBADDR7
address_b[7] => ram_block11a6.PORTBADDR7
address_b[7] => ram_block11a7.PORTBADDR7
address_b[7] => ram_block11a8.PORTBADDR7
address_b[7] => ram_block11a9.PORTBADDR7
address_b[7] => ram_block11a10.PORTBADDR7
address_b[7] => ram_block11a11.PORTBADDR7
address_b[8] => ram_block11a0.PORTBADDR8
address_b[8] => ram_block11a1.PORTBADDR8
address_b[8] => ram_block11a2.PORTBADDR8
address_b[8] => ram_block11a3.PORTBADDR8
address_b[8] => ram_block11a4.PORTBADDR8
address_b[8] => ram_block11a5.PORTBADDR8
address_b[8] => ram_block11a6.PORTBADDR8
address_b[8] => ram_block11a7.PORTBADDR8
address_b[8] => ram_block11a8.PORTBADDR8
address_b[8] => ram_block11a9.PORTBADDR8
address_b[8] => ram_block11a10.PORTBADDR8
address_b[8] => ram_block11a11.PORTBADDR8
address_b[9] => ram_block11a0.PORTBADDR9
address_b[9] => ram_block11a1.PORTBADDR9
address_b[9] => ram_block11a2.PORTBADDR9
address_b[9] => ram_block11a3.PORTBADDR9
address_b[9] => ram_block11a4.PORTBADDR9
address_b[9] => ram_block11a5.PORTBADDR9
address_b[9] => ram_block11a6.PORTBADDR9
address_b[9] => ram_block11a7.PORTBADDR9
address_b[9] => ram_block11a8.PORTBADDR9
address_b[9] => ram_block11a9.PORTBADDR9
address_b[9] => ram_block11a10.PORTBADDR9
address_b[9] => ram_block11a11.PORTBADDR9
addressstall_b => ram_block11a0.PORTBADDRSTALL
addressstall_b => ram_block11a1.PORTBADDRSTALL
addressstall_b => ram_block11a2.PORTBADDRSTALL
addressstall_b => ram_block11a3.PORTBADDRSTALL
addressstall_b => ram_block11a4.PORTBADDRSTALL
addressstall_b => ram_block11a5.PORTBADDRSTALL
addressstall_b => ram_block11a6.PORTBADDRSTALL
addressstall_b => ram_block11a7.PORTBADDRSTALL
addressstall_b => ram_block11a8.PORTBADDRSTALL
addressstall_b => ram_block11a9.PORTBADDRSTALL
addressstall_b => ram_block11a10.PORTBADDRSTALL
addressstall_b => ram_block11a11.PORTBADDRSTALL
clock0 => ram_block11a0.CLK0
clock0 => ram_block11a1.CLK0
clock0 => ram_block11a2.CLK0
clock0 => ram_block11a3.CLK0
clock0 => ram_block11a4.CLK0
clock0 => ram_block11a5.CLK0
clock0 => ram_block11a6.CLK0
clock0 => ram_block11a7.CLK0
clock0 => ram_block11a8.CLK0
clock0 => ram_block11a9.CLK0
clock0 => ram_block11a10.CLK0
clock0 => ram_block11a11.CLK0
clock1 => ram_block11a0.CLK1
clock1 => ram_block11a1.CLK1
clock1 => ram_block11a2.CLK1
clock1 => ram_block11a3.CLK1
clock1 => ram_block11a4.CLK1
clock1 => ram_block11a5.CLK1
clock1 => ram_block11a6.CLK1
clock1 => ram_block11a7.CLK1
clock1 => ram_block11a8.CLK1
clock1 => ram_block11a9.CLK1
clock1 => ram_block11a10.CLK1
clock1 => ram_block11a11.CLK1
clocken1 => ram_block11a0.ENA1
clocken1 => ram_block11a1.ENA1
clocken1 => ram_block11a2.ENA1
clocken1 => ram_block11a3.ENA1
clocken1 => ram_block11a4.ENA1
clocken1 => ram_block11a5.ENA1
clocken1 => ram_block11a6.ENA1
clocken1 => ram_block11a7.ENA1
clocken1 => ram_block11a8.ENA1
clocken1 => ram_block11a9.ENA1
clocken1 => ram_block11a10.ENA1
clocken1 => ram_block11a11.ENA1
data_a[0] => ram_block11a0.PORTADATAIN
data_a[1] => ram_block11a1.PORTADATAIN
data_a[2] => ram_block11a2.PORTADATAIN
data_a[3] => ram_block11a3.PORTADATAIN
data_a[4] => ram_block11a4.PORTADATAIN
data_a[5] => ram_block11a5.PORTADATAIN
data_a[6] => ram_block11a6.PORTADATAIN
data_a[7] => ram_block11a7.PORTADATAIN
data_a[8] => ram_block11a8.PORTADATAIN
data_a[9] => ram_block11a9.PORTADATAIN
data_a[10] => ram_block11a10.PORTADATAIN
data_a[11] => ram_block11a11.PORTADATAIN
q_b[0] <= ram_block11a0.PORTBDATAOUT
q_b[1] <= ram_block11a1.PORTBDATAOUT
q_b[2] <= ram_block11a2.PORTBDATAOUT
q_b[3] <= ram_block11a3.PORTBDATAOUT
q_b[4] <= ram_block11a4.PORTBDATAOUT
q_b[5] <= ram_block11a5.PORTBDATAOUT
q_b[6] <= ram_block11a6.PORTBDATAOUT
q_b[7] <= ram_block11a7.PORTBDATAOUT
q_b[8] <= ram_block11a8.PORTBDATAOUT
q_b[9] <= ram_block11a9.PORTBDATAOUT
q_b[10] <= ram_block11a10.PORTBDATAOUT
q_b[11] <= ram_block11a11.PORTBDATAOUT
wren_a => ram_block11a0.PORTAWE
wren_a => ram_block11a0.ENA0
wren_a => ram_block11a1.PORTAWE
wren_a => ram_block11a1.ENA0
wren_a => ram_block11a2.PORTAWE
wren_a => ram_block11a2.ENA0
wren_a => ram_block11a3.PORTAWE
wren_a => ram_block11a3.ENA0
wren_a => ram_block11a4.PORTAWE
wren_a => ram_block11a4.ENA0
wren_a => ram_block11a5.PORTAWE
wren_a => ram_block11a5.ENA0
wren_a => ram_block11a6.PORTAWE
wren_a => ram_block11a6.ENA0
wren_a => ram_block11a7.PORTAWE
wren_a => ram_block11a7.ENA0
wren_a => ram_block11a8.PORTAWE
wren_a => ram_block11a8.ENA0
wren_a => ram_block11a9.PORTAWE
wren_a => ram_block11a9.ENA0
wren_a => ram_block11a10.PORTAWE
wren_a => ram_block11a10.ENA0
wren_a => ram_block11a11.PORTAWE
wren_a => ram_block11a11.ENA0


|TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp
clock => dffpipe_b09:dffpipe12.clock
d[0] => dffpipe_b09:dffpipe12.d[0]
d[1] => dffpipe_b09:dffpipe12.d[1]
d[2] => dffpipe_b09:dffpipe12.d[2]
d[3] => dffpipe_b09:dffpipe12.d[3]
d[4] => dffpipe_b09:dffpipe12.d[4]
d[5] => dffpipe_b09:dffpipe12.d[5]
d[6] => dffpipe_b09:dffpipe12.d[6]
d[7] => dffpipe_b09:dffpipe12.d[7]
d[8] => dffpipe_b09:dffpipe12.d[8]
d[9] => dffpipe_b09:dffpipe12.d[9]
d[10] => dffpipe_b09:dffpipe12.d[10]
q[0] <= dffpipe_b09:dffpipe12.q[0]
q[1] <= dffpipe_b09:dffpipe12.q[1]
q[2] <= dffpipe_b09:dffpipe12.q[2]
q[3] <= dffpipe_b09:dffpipe12.q[3]
q[4] <= dffpipe_b09:dffpipe12.q[4]
q[5] <= dffpipe_b09:dffpipe12.q[5]
q[6] <= dffpipe_b09:dffpipe12.q[6]
q[7] <= dffpipe_b09:dffpipe12.q[7]
q[8] <= dffpipe_b09:dffpipe12.q[8]
q[9] <= dffpipe_b09:dffpipe12.q[9]
q[10] <= dffpipe_b09:dffpipe12.q[10]


|TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_qal:rs_dgwp|dffpipe_b09:dffpipe12
clock => dffe13a[10].CLK
clock => dffe13a[9].CLK
clock => dffe13a[8].CLK
clock => dffe13a[7].CLK
clock => dffe13a[6].CLK
clock => dffe13a[5].CLK
clock => dffe13a[4].CLK
clock => dffe13a[3].CLK
clock => dffe13a[2].CLK
clock => dffe13a[1].CLK
clock => dffe13a[0].CLK
clock => dffe14a[10].CLK
clock => dffe14a[9].CLK
clock => dffe14a[8].CLK
clock => dffe14a[7].CLK
clock => dffe14a[6].CLK
clock => dffe14a[5].CLK
clock => dffe14a[4].CLK
clock => dffe14a[3].CLK
clock => dffe14a[2].CLK
clock => dffe14a[1].CLK
clock => dffe14a[0].CLK
d[0] => dffe13a[0].IN0
d[1] => dffe13a[1].IN0
d[2] => dffe13a[2].IN0
d[3] => dffe13a[3].IN0
d[4] => dffe13a[4].IN0
d[5] => dffe13a[5].IN0
d[6] => dffe13a[6].IN0
d[7] => dffe13a[7].IN0
d[8] => dffe13a[8].IN0
d[9] => dffe13a[9].IN0
d[10] => dffe13a[10].IN0
q[0] <= dffe14a[0].DB_MAX_OUTPUT_PORT_TYPE
q[1] <= dffe14a[1].DB_MAX_OUTPUT_PORT_TYPE
q[2] <= dffe14a[2].DB_MAX_OUTPUT_PORT_TYPE
q[3] <= dffe14a[3].DB_MAX_OUTPUT_PORT_TYPE
q[4] <= dffe14a[4].DB_MAX_OUTPUT_PORT_TYPE
q[5] <= dffe14a[5].DB_MAX_OUTPUT_PORT_TYPE
q[6] <= dffe14a[6].DB_MAX_OUTPUT_PORT_TYPE
q[7] <= dffe14a[7].DB_MAX_OUTPUT_PORT_TYPE
q[8] <= dffe14a[8].DB_MAX_OUTPUT_PORT_TYPE
q[9] <= dffe14a[9].DB_MAX_OUTPUT_PORT_TYPE
q[10] <= dffe14a[10].DB_MAX_OUTPUT_PORT_TYPE


|TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp
clock => dffpipe_c09:dffpipe15.clock
d[0] => dffpipe_c09:dffpipe15.d[0]
d[1] => dffpipe_c09:dffpipe15.d[1]
d[2] => dffpipe_c09:dffpipe15.d[2]
d[3] => dffpipe_c09:dffpipe15.d[3]
d[4] => dffpipe_c09:dffpipe15.d[4]
d[5] => dffpipe_c09:dffpipe15.d[5]
d[6] => dffpipe_c09:dffpipe15.d[6]
d[7] => dffpipe_c09:dffpipe15.d[7]
d[8] => dffpipe_c09:dffpipe15.d[8]
d[9] => dffpipe_c09:dffpipe15.d[9]
d[10] => dffpipe_c09:dffpipe15.d[10]
q[0] <= dffpipe_c09:dffpipe15.q[0]
q[1] <= dffpipe_c09:dffpipe15.q[1]
q[2] <= dffpipe_c09:dffpipe15.q[2]
q[3] <= dffpipe_c09:dffpipe15.q[3]
q[4] <= dffpipe_c09:dffpipe15.q[4]
q[5] <= dffpipe_c09:dffpipe15.q[5]
q[6] <= dffpipe_c09:dffpipe15.q[6]
q[7] <= dffpipe_c09:dffpipe15.q[7]
q[8] <= dffpipe_c09:dffpipe15.q[8]
q[9] <= dffpipe_c09:dffpipe15.q[9]
q[10] <= dffpipe_c09:dffpipe15.q[10]


|TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|alt_synch_pipe_ral:ws_dgrp|dffpipe_c09:dffpipe15
clock => dffe16a[10].CLK
clock => dffe16a[9].CLK
clock => dffe16a[8].CLK
clock => dffe16a[7].CLK
clock => dffe16a[6].CLK
clock => dffe16a[5].CLK
clock => dffe16a[4].CLK
clock => dffe16a[3].CLK
clock => dffe16a[2].CLK
clock => dffe16a[1].CLK
clock => dffe16a[0].CLK
clock => dffe17a[10].CLK
clock => dffe17a[9].CLK
clock => dffe17a[8].CLK
clock => dffe17a[7].CLK
clock => dffe17a[6].CLK
clock => dffe17a[5].CLK
clock => dffe17a[4].CLK
clock => dffe17a[3].CLK
clock => dffe17a[2].CLK
clock => dffe17a[1].CLK
clock => dffe17a[0].CLK
d[0] => dffe16a[0].IN0
d[1] => dffe16a[1].IN0
d[2] => dffe16a[2].IN0
d[3] => dffe16a[3].IN0
d[4] => dffe16a[4].IN0
d[5] => dffe16a[5].IN0
d[6] => dffe16a[6].IN0
d[7] => dffe16a[7].IN0
d[8] => dffe16a[8].IN0
d[9] => dffe16a[9].IN0
d[10] => dffe16a[10].IN0
q[0] <= dffe17a[0].DB_MAX_OUTPUT_PORT_TYPE
q[1] <= dffe17a[1].DB_MAX_OUTPUT_PORT_TYPE
q[2] <= dffe17a[2].DB_MAX_OUTPUT_PORT_TYPE
q[3] <= dffe17a[3].DB_MAX_OUTPUT_PORT_TYPE
q[4] <= dffe17a[4].DB_MAX_OUTPUT_PORT_TYPE
q[5] <= dffe17a[5].DB_MAX_OUTPUT_PORT_TYPE
q[6] <= dffe17a[6].DB_MAX_OUTPUT_PORT_TYPE
q[7] <= dffe17a[7].DB_MAX_OUTPUT_PORT_TYPE
q[8] <= dffe17a[8].DB_MAX_OUTPUT_PORT_TYPE
q[9] <= dffe17a[9].DB_MAX_OUTPUT_PORT_TYPE
q[10] <= dffe17a[10].DB_MAX_OUTPUT_PORT_TYPE


|TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|cmpr_o76:rdempty_eq_comp
aeb <= aeb_result_wire[0].DB_MAX_OUTPUT_PORT_TYPE
dataa[0] => data_wire[2].IN0
dataa[1] => data_wire[2].IN0
dataa[2] => data_wire[3].IN0
dataa[3] => data_wire[3].IN0
dataa[4] => data_wire[4].IN0
dataa[5] => data_wire[4].IN0
dataa[6] => data_wire[5].IN0
dataa[7] => data_wire[5].IN0
dataa[8] => data_wire[6].IN0
dataa[9] => data_wire[6].IN0
dataa[10] => data_wire[7].IN0
datab[0] => data_wire[2].IN1
datab[1] => data_wire[2].IN1
datab[2] => data_wire[3].IN1
datab[3] => data_wire[3].IN1
datab[4] => data_wire[4].IN1
datab[5] => data_wire[4].IN1
datab[6] => data_wire[5].IN1
datab[7] => data_wire[5].IN1
datab[8] => data_wire[6].IN1
datab[9] => data_wire[6].IN1
datab[10] => data_wire[7].IN1


|TOP|TYFIFO:u_AD2_FIFO|dcfifo:dcfifo_component|dcfifo_vve1:auto_generated|cmpr_o76:wrfull_eq_comp
aeb <= aeb_result_wire[0].DB_MAX_OUTPUT_PORT_TYPE
dataa[0] => data_wire[2].IN0
dataa[1] => data_wire[2].IN0
dataa[2] => data_wire[3].IN0
dataa[3] => data_wire[3].IN0
dataa[4] => data_wire[4].IN0
dataa[5] => data_wire[4].IN0
dataa[6] => data_wire[5].IN0
dataa[7] => data_wire[5].IN0
dataa[8] => data_wire[6].IN0
dataa[9] => data_wire[6].IN0
dataa[10] => data_wire[7].IN0
datab[0] => data_wire[2].IN1
datab[1] => data_wire[2].IN1
datab[2] => data_wire[3].IN1
datab[3] => data_wire[3].IN1
datab[4] => data_wire[4].IN1
datab[5] => data_wire[4].IN1
datab[6] => data_wire[5].IN1
datab[7] => data_wire[5].IN1
datab[8] => data_wire[6].IN1
datab[9] => data_wire[6].IN1
datab[10] => data_wire[7].IN1


|TOP|FREQ_DEV:u_AD2_DEV
CLK => FREQ_WORD[0].CLK
CLK => FREQ_WORD[1].CLK
CLK => FREQ_WORD[2].CLK
CLK => FREQ_WORD[3].CLK
CLK => FREQ_WORD[4].CLK
CLK => FREQ_WORD[5].CLK
CLK => FREQ_WORD[6].CLK
CLK => FREQ_WORD[7].CLK
CLK => FREQ_WORD[8].CLK
CLK => FREQ_WORD[9].CLK
CLK => FREQ_WORD[10].CLK
CLK => FREQ_WORD[11].CLK
CLK => FREQ_WORD[12].CLK
CLK => FREQ_WORD[13].CLK
CLK => FREQ_WORD[14].CLK
CLK => FREQ_WORD[15].CLK
CLK => FREQ_WORD[16].CLK
CLK => FREQ_WORD[17].CLK
CLK => FREQ_WORD[18].CLK
CLK => FREQ_WORD[19].CLK
CLK => FREQ_WORD[20].CLK
CLK => FREQ_WORD[21].CLK
CLK => FREQ_WORD[22].CLK
CLK => FREQ_WORD[23].CLK
CLK => FREQ_WORD[24].CLK
CLK => FREQ_WORD[25].CLK
CLK => FREQ_WORD[26].CLK
CLK => FREQ_WORD[27].CLK
CLK => FREQ_WORD[28].CLK
CLK => FREQ_WORD[29].CLK
CLK => FREQ_WORD[30].CLK
CLK => FREQ_WORD[31].CLK
CLK => FREQ_OUT~reg0.CLK
CLK => ACC[0].CLK
CLK => ACC[1].CLK
CLK => ACC[2].CLK
CLK => ACC[3].CLK
CLK => ACC[4].CLK
CLK => ACC[5].CLK
CLK => ACC[6].CLK
CLK => ACC[7].CLK
CLK => ACC[8].CLK
CLK => ACC[9].CLK
CLK => ACC[10].CLK
CLK => ACC[11].CLK
CLK => ACC[12].CLK
CLK => ACC[13].CLK
CLK => ACC[14].CLK
CLK => ACC[15].CLK
CLK => ACC[16].CLK
CLK => ACC[17].CLK
CLK => ACC[18].CLK
CLK => ACC[19].CLK
CLK => ACC[20].CLK
CLK => ACC[21].CLK
CLK => ACC[22].CLK
CLK => ACC[23].CLK
CLK => ACC[24].CLK
CLK => ACC[25].CLK
CLK => ACC[26].CLK
CLK => ACC[27].CLK
CLK => ACC[28].CLK
CLK => ACC[29].CLK
CLK => ACC[30].CLK
CLK => ACC[31].CLK
EN => ACC[0].ENA
EN => ACC[1].ENA
EN => ACC[2].ENA
EN => ACC[3].ENA
EN => ACC[4].ENA
EN => ACC[5].ENA
EN => ACC[6].ENA
EN => ACC[7].ENA
EN => ACC[8].ENA
EN => ACC[9].ENA
EN => ACC[10].ENA
EN => ACC[11].ENA
EN => ACC[12].ENA
EN => ACC[13].ENA
EN => ACC[14].ENA
EN => ACC[15].ENA
EN => ACC[16].ENA
EN => ACC[17].ENA
EN => ACC[18].ENA
EN => ACC[19].ENA
EN => ACC[20].ENA
EN => ACC[21].ENA
EN => ACC[22].ENA
EN => ACC[23].ENA
EN => ACC[24].ENA
EN => ACC[25].ENA
EN => ACC[26].ENA
EN => ACC[27].ENA
EN => ACC[28].ENA
EN => ACC[29].ENA
EN => ACC[30].ENA
EN => ACC[31].ENA
FREQH_W[0] => FREQ_WORD[16].DATAIN
FREQH_W[1] => FREQ_WORD[17].DATAIN
FREQH_W[2] => FREQ_WORD[18].DATAIN
FREQH_W[3] => FREQ_WORD[19].DATAIN
FREQH_W[4] => FREQ_WORD[20].DATAIN
FREQH_W[5] => FREQ_WORD[21].DATAIN
FREQH_W[6] => FREQ_WORD[22].DATAIN
FREQH_W[7] => FREQ_WORD[23].DATAIN
FREQH_W[8] => FREQ_WORD[24].DATAIN
FREQH_W[9] => FREQ_WORD[25].DATAIN
FREQH_W[10] => FREQ_WORD[26].DATAIN
FREQH_W[11] => FREQ_WORD[27].DATAIN
FREQH_W[12] => FREQ_WORD[28].DATAIN
FREQH_W[13] => FREQ_WORD[29].DATAIN
FREQH_W[14] => FREQ_WORD[30].DATAIN
FREQH_W[15] => FREQ_WORD[31].DATAIN
FREQL_W[0] => FREQ_WORD[0].DATAIN
FREQL_W[1] => FREQ_WORD[1].DATAIN
FREQL_W[2] => FREQ_WORD[2].DATAIN
FREQL_W[3] => FREQ_WORD[3].DATAIN
FREQL_W[4] => FREQ_WORD[4].DATAIN
FREQL_W[5] => FREQ_WORD[5].DATAIN
FREQL_W[6] => FREQ_WORD[6].DATAIN
FREQL_W[7] => FREQ_WORD[7].DATAIN
FREQL_W[8] => FREQ_WORD[8].DATAIN
FREQL_W[9] => FREQ_WORD[9].DATAIN
FREQL_W[10] => FREQ_WORD[10].DATAIN
FREQL_W[11] => FREQ_WORD[11].DATAIN
FREQL_W[12] => FREQ_WORD[12].DATAIN
FREQL_W[13] => FREQ_WORD[13].DATAIN
FREQL_W[14] => FREQ_WORD[14].DATAIN
FREQL_W[15] => FREQ_WORD[15].DATAIN
FREQ_OUT <= FREQ_OUT~reg0.DB_MAX_OUTPUT_PORT_TYPE


|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD
CS => always0.IN0
WR_EN => always0.IN1
DATA0[0] => DA1_OUTH[0]$latch.DATAIN
DATA0[1] => DA1_OUTH[1]$latch.DATAIN
DATA0[2] => DA1_OUTH[2]$latch.DATAIN
DATA0[3] => DA1_OUTH[3]$latch.DATAIN
DATA0[4] => DA1_OUTH[4]$latch.DATAIN
DATA0[5] => DA1_OUTH[5]$latch.DATAIN
DATA0[6] => DA1_OUTH[6]$latch.DATAIN
DATA0[7] => DA1_OUTH[7]$latch.DATAIN
DATA0[8] => DA1_OUTH[8]$latch.DATAIN
DATA0[9] => DA1_OUTH[9]$latch.DATAIN
DATA0[10] => DA1_OUTH[10]$latch.DATAIN
DATA0[11] => DA1_OUTH[11]$latch.DATAIN
DATA0[12] => DA1_OUTH[12]$latch.DATAIN
DATA0[13] => DA1_OUTH[13]$latch.DATAIN
DATA0[14] => DA1_OUTH[14]$latch.DATAIN
DATA0[15] => DA1_OUTH[15]$latch.DATAIN
DATA1[0] => DA1_OUTL[0]$latch.DATAIN
DATA1[1] => DA1_OUTL[1]$latch.DATAIN
DATA1[2] => DA1_OUTL[2]$latch.DATAIN
DATA1[3] => DA1_OUTL[3]$latch.DATAIN
DATA1[4] => DA1_OUTL[4]$latch.DATAIN
DATA1[5] => DA1_OUTL[5]$latch.DATAIN
DATA1[6] => DA1_OUTL[6]$latch.DATAIN
DATA1[7] => DA1_OUTL[7]$latch.DATAIN
DATA1[8] => DA1_OUTL[8]$latch.DATAIN
DATA1[9] => DA1_OUTL[9]$latch.DATAIN
DATA1[10] => DA1_OUTL[10]$latch.DATAIN
DATA1[11] => DA1_OUTL[11]$latch.DATAIN
DATA1[12] => DA1_OUTL[12]$latch.DATAIN
DATA1[13] => DA1_OUTL[13]$latch.DATAIN
DATA1[14] => DA1_OUTL[14]$latch.DATAIN
DATA1[15] => DA1_OUTL[15]$latch.DATAIN
DATA2[0] => DA2_OUTH[0]$latch.DATAIN
DATA2[1] => DA2_OUTH[1]$latch.DATAIN
DATA2[2] => DA2_OUTH[2]$latch.DATAIN
DATA2[3] => DA2_OUTH[3]$latch.DATAIN
DATA2[4] => DA2_OUTH[4]$latch.DATAIN
DATA2[5] => DA2_OUTH[5]$latch.DATAIN
DATA2[6] => DA2_OUTH[6]$latch.DATAIN
DATA2[7] => DA2_OUTH[7]$latch.DATAIN
DATA2[8] => DA2_OUTH[8]$latch.DATAIN
DATA2[9] => DA2_OUTH[9]$latch.DATAIN
DATA2[10] => DA2_OUTH[10]$latch.DATAIN
DATA2[11] => DA2_OUTH[11]$latch.DATAIN
DATA2[12] => DA2_OUTH[12]$latch.DATAIN
DATA2[13] => DA2_OUTH[13]$latch.DATAIN
DATA2[14] => DA2_OUTH[14]$latch.DATAIN
DATA2[15] => DA2_OUTH[15]$latch.DATAIN
DATA3[0] => DA2_OUTL[0]$latch.DATAIN
DATA3[1] => DA2_OUTL[1]$latch.DATAIN
DATA3[2] => DA2_OUTL[2]$latch.DATAIN
DATA3[3] => DA2_OUTL[3]$latch.DATAIN
DATA3[4] => DA2_OUTL[4]$latch.DATAIN
DATA3[5] => DA2_OUTL[5]$latch.DATAIN
DATA3[6] => DA2_OUTL[6]$latch.DATAIN
DATA3[7] => DA2_OUTL[7]$latch.DATAIN
DATA3[8] => DA2_OUTL[8]$latch.DATAIN
DATA3[9] => DA2_OUTL[9]$latch.DATAIN
DATA3[10] => DA2_OUTL[10]$latch.DATAIN
DATA3[11] => DA2_OUTL[11]$latch.DATAIN
DATA3[12] => DA2_OUTL[12]$latch.DATAIN
DATA3[13] => DA2_OUTL[13]$latch.DATAIN
DATA3[14] => DA2_OUTL[14]$latch.DATAIN
DATA3[15] => DA2_OUTL[15]$latch.DATAIN
ADDR[0] => Equal0.IN31
ADDR[0] => Equal1.IN31
ADDR[0] => Equal2.IN31
ADDR[0] => Equal3.IN31
ADDR[1] => Equal0.IN30
ADDR[1] => Equal1.IN30
ADDR[1] => Equal2.IN30
ADDR[1] => Equal3.IN30
ADDR[2] => Equal0.IN29
ADDR[2] => Equal1.IN29
ADDR[2] => Equal2.IN29
ADDR[2] => Equal3.IN29
ADDR[3] => Equal0.IN28
ADDR[3] => Equal1.IN28
ADDR[3] => Equal2.IN28
ADDR[3] => Equal3.IN28
ADDR[4] => Equal0.IN27
ADDR[4] => Equal1.IN27
ADDR[4] => Equal2.IN27
ADDR[4] => Equal3.IN27
ADDR[5] => Equal0.IN26
ADDR[5] => Equal1.IN26
ADDR[5] => Equal2.IN26
ADDR[5] => Equal3.IN26
ADDR[6] => Equal0.IN25
ADDR[6] => Equal1.IN25
ADDR[6] => Equal2.IN25
ADDR[6] => Equal3.IN25
ADDR[7] => Equal0.IN24
ADDR[7] => Equal1.IN24
ADDR[7] => Equal2.IN24
ADDR[7] => Equal3.IN24
ADDR[8] => Equal0.IN23
ADDR[8] => Equal1.IN23
ADDR[8] => Equal2.IN23
ADDR[8] => Equal3.IN23
ADDR[9] => Equal0.IN22
ADDR[9] => Equal1.IN22
ADDR[9] => Equal2.IN22
ADDR[9] => Equal3.IN22
ADDR[10] => Equal0.IN21
ADDR[10] => Equal1.IN21
ADDR[10] => Equal2.IN21
ADDR[10] => Equal3.IN21
ADDR[11] => Equal0.IN20
ADDR[11] => Equal1.IN20
ADDR[11] => Equal2.IN20
ADDR[11] => Equal3.IN20
ADDR[12] => Equal0.IN19
ADDR[12] => Equal1.IN19
ADDR[12] => Equal2.IN19
ADDR[12] => Equal3.IN19
ADDR[13] => Equal0.IN18
ADDR[13] => Equal1.IN18
ADDR[13] => Equal2.IN18
ADDR[13] => Equal3.IN18
ADDR[14] => Equal0.IN17
ADDR[14] => Equal1.IN17
ADDR[14] => Equal2.IN17
ADDR[14] => Equal3.IN17
ADDR[15] => Equal0.IN16
ADDR[15] => Equal1.IN16
ADDR[15] => Equal2.IN16
ADDR[15] => Equal3.IN16
DA1_OUTH[0] <= DA1_OUTH[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[1] <= DA1_OUTH[1]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[2] <= DA1_OUTH[2]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[3] <= DA1_OUTH[3]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[4] <= DA1_OUTH[4]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[5] <= DA1_OUTH[5]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[6] <= DA1_OUTH[6]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[7] <= DA1_OUTH[7]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[8] <= DA1_OUTH[8]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[9] <= DA1_OUTH[9]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[10] <= DA1_OUTH[10]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[11] <= DA1_OUTH[11]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[12] <= DA1_OUTH[12]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[13] <= DA1_OUTH[13]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[14] <= DA1_OUTH[14]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTH[15] <= DA1_OUTH[15]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[0] <= DA1_OUTL[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[1] <= DA1_OUTL[1]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[2] <= DA1_OUTL[2]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[3] <= DA1_OUTL[3]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[4] <= DA1_OUTL[4]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[5] <= DA1_OUTL[5]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[6] <= DA1_OUTL[6]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[7] <= DA1_OUTL[7]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[8] <= DA1_OUTL[8]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[9] <= DA1_OUTL[9]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[10] <= DA1_OUTL[10]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[11] <= DA1_OUTL[11]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[12] <= DA1_OUTL[12]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[13] <= DA1_OUTL[13]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[14] <= DA1_OUTL[14]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTL[15] <= DA1_OUTL[15]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[0] <= DA2_OUTH[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[1] <= DA2_OUTH[1]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[2] <= DA2_OUTH[2]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[3] <= DA2_OUTH[3]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[4] <= DA2_OUTH[4]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[5] <= DA2_OUTH[5]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[6] <= DA2_OUTH[6]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[7] <= DA2_OUTH[7]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[8] <= DA2_OUTH[8]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[9] <= DA2_OUTH[9]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[10] <= DA2_OUTH[10]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[11] <= DA2_OUTH[11]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[12] <= DA2_OUTH[12]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[13] <= DA2_OUTH[13]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[14] <= DA2_OUTH[14]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTH[15] <= DA2_OUTH[15]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[0] <= DA2_OUTL[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[1] <= DA2_OUTL[1]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[2] <= DA2_OUTL[2]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[3] <= DA2_OUTL[3]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[4] <= DA2_OUTL[4]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[5] <= DA2_OUTL[5]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[6] <= DA2_OUTL[6]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[7] <= DA2_OUTL[7]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[8] <= DA2_OUTL[8]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[9] <= DA2_OUTL[9]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[10] <= DA2_OUTL[10]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[11] <= DA2_OUTL[11]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[12] <= DA2_OUTL[12]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[13] <= DA2_OUTL[13]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[14] <= DA2_OUTL[14]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTL[15] <= DA2_OUTL[15]$latch.DB_MAX_OUTPUT_PORT_TYPE


|TOP|voltage_scaler_clocked:inst12
clk => scaled_data[0]~reg0.CLK
clk => scaled_data[1]~reg0.CLK
clk => scaled_data[2]~reg0.CLK
clk => scaled_data[3]~reg0.CLK
clk => scaled_data[4]~reg0.CLK
clk => scaled_data[5]~reg0.CLK
clk => scaled_data[6]~reg0.CLK
clk => scaled_data[7]~reg0.CLK
clk => scaled_data[8]~reg0.CLK
clk => scaled_data[9]~reg0.CLK
clk => scaled_data[10]~reg0.CLK
clk => scaled_data[11]~reg0.CLK
clk => scaled_data[12]~reg0.CLK
clk => scaled_data[13]~reg0.CLK
rom_data[0] => LessThan0.IN28
rom_data[0] => Add0.IN28
rom_data[0] => Mult1.IN12
rom_data[1] => LessThan0.IN27
rom_data[1] => Add0.IN27
rom_data[1] => Mult1.IN11
rom_data[2] => LessThan0.IN26
rom_data[2] => Add0.IN26
rom_data[2] => Mult1.IN10
rom_data[3] => LessThan0.IN25
rom_data[3] => Add0.IN25
rom_data[3] => Mult1.IN9
rom_data[4] => LessThan0.IN24
rom_data[4] => Add0.IN24
rom_data[4] => Mult1.IN8
rom_data[5] => LessThan0.IN23
rom_data[5] => Add0.IN23
rom_data[5] => Mult1.IN7
rom_data[6] => LessThan0.IN22
rom_data[6] => Add0.IN22
rom_data[6] => Mult1.IN6
rom_data[7] => LessThan0.IN21
rom_data[7] => Add0.IN21
rom_data[7] => Mult1.IN5
rom_data[8] => LessThan0.IN20
rom_data[8] => Add0.IN20
rom_data[8] => Mult1.IN4
rom_data[9] => LessThan0.IN19
rom_data[9] => Add0.IN19
rom_data[9] => Mult1.IN3
rom_data[10] => LessThan0.IN18
rom_data[10] => Add0.IN18
rom_data[10] => Mult1.IN2
rom_data[11] => LessThan0.IN17
rom_data[11] => Add0.IN17
rom_data[11] => Mult1.IN1
rom_data[12] => LessThan0.IN16
rom_data[12] => Add0.IN16
rom_data[12] => Mult1.IN0
rom_data[13] => LessThan0.IN15
rom_data[13] => Add0.IN15
rom_data[13] => Add2.IN1
voltage_mv[0] => Mult0.IN43
voltage_mv[0] => Mult1.IN43
voltage_mv[1] => Mult0.IN42
voltage_mv[1] => Mult1.IN42
voltage_mv[2] => Mult0.IN41
voltage_mv[2] => Mult1.IN41
voltage_mv[3] => Mult0.IN40
voltage_mv[3] => Mult1.IN40
voltage_mv[4] => Mult0.IN39
voltage_mv[4] => Mult1.IN39
voltage_mv[5] => Mult0.IN38
voltage_mv[5] => Mult1.IN38
voltage_mv[6] => Mult0.IN37
voltage_mv[6] => Mult1.IN37
voltage_mv[7] => Mult0.IN36
voltage_mv[7] => Mult1.IN36
voltage_mv[8] => Mult0.IN35
voltage_mv[8] => Mult1.IN35
voltage_mv[9] => Mult0.IN34
voltage_mv[9] => Mult1.IN34
voltage_mv[10] => Mult0.IN33
voltage_mv[10] => Mult1.IN33
voltage_mv[11] => Mult0.IN32
voltage_mv[11] => Mult1.IN32
scaled_data[0] <= scaled_data[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[1] <= scaled_data[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[2] <= scaled_data[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[3] <= scaled_data[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[4] <= scaled_data[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[5] <= scaled_data[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[6] <= scaled_data[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[7] <= scaled_data[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[8] <= scaled_data[8]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[9] <= scaled_data[9]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[10] <= scaled_data[10]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[11] <= scaled_data[11]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[12] <= scaled_data[12]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[13] <= scaled_data[13]~reg0.DB_MAX_OUTPUT_PORT_TYPE


|TOP|DA_WAVEFORM_A:inst15
CLK => CLK.IN4
WAVEFORM[0] => WAVEFORM_A[0].DATAIN
WAVEFORM[1] => WAVEFORM_A[1].DATAIN
WAVEFORM[2] => WAVEFORM_A[2].DATAIN
WAVEFORM[3] => WAVEFORM_A[3].DATAIN
WAVEFORM[4] => WAVEFORM_A[4].DATAIN
WAVEFORM[5] => WAVEFORM_A[5].DATAIN
WAVEFORM[6] => WAVEFORM_A[6].DATAIN
WAVEFORM[7] => WAVEFORM_A[7].DATAIN
WAVEFORM[8] => ~NO_FANOUT~
WAVEFORM[9] => ~NO_FANOUT~
WAVEFORM[10] => ~NO_FANOUT~
WAVEFORM[11] => ~NO_FANOUT~
WAVEFORM[12] => ~NO_FANOUT~
WAVEFORM[13] => ~NO_FANOUT~
WAVEFORM[14] => ~NO_FANOUT~
WAVEFORM[15] => ~NO_FANOUT~
addr_a[0] => addr_a[0].IN4
addr_a[1] => addr_a[1].IN4
addr_a[2] => addr_a[2].IN4
addr_a[3] => addr_a[3].IN4
addr_a[4] => addr_a[4].IN4
addr_a[5] => addr_a[5].IN4
addr_a[6] => addr_a[6].IN4
addr_a[7] => addr_a[7].IN4
addr_a[8] => addr_a[8].IN4
addr_a[9] => addr_a[9].IN4
CS => always0.IN0
WR_EN => always0.IN1
ADDR[0] => Equal0.IN31
ADDR[1] => Equal0.IN30
ADDR[2] => Equal0.IN29
ADDR[3] => Equal0.IN28
ADDR[4] => Equal0.IN27
ADDR[5] => Equal0.IN26
ADDR[6] => Equal0.IN25
ADDR[7] => Equal0.IN24
ADDR[8] => Equal0.IN23
ADDR[9] => Equal0.IN22
ADDR[10] => Equal0.IN21
ADDR[11] => Equal0.IN20
ADDR[12] => Equal0.IN19
ADDR[13] => Equal0.IN18
ADDR[14] => Equal0.IN17
ADDR[15] => Equal0.IN16
POW_A[0] <= WAVE_DATA_A_reg[0].DB_MAX_OUTPUT_PORT_TYPE
POW_A[1] <= WAVE_DATA_A_reg[1].DB_MAX_OUTPUT_PORT_TYPE
POW_A[2] <= WAVE_DATA_A_reg[2].DB_MAX_OUTPUT_PORT_TYPE
POW_A[3] <= WAVE_DATA_A_reg[3].DB_MAX_OUTPUT_PORT_TYPE
POW_A[4] <= WAVE_DATA_A_reg[4].DB_MAX_OUTPUT_PORT_TYPE
POW_A[5] <= WAVE_DATA_A_reg[5].DB_MAX_OUTPUT_PORT_TYPE
POW_A[6] <= WAVE_DATA_A_reg[6].DB_MAX_OUTPUT_PORT_TYPE
POW_A[7] <= WAVE_DATA_A_reg[7].DB_MAX_OUTPUT_PORT_TYPE
POW_A[8] <= WAVE_DATA_A_reg[8].DB_MAX_OUTPUT_PORT_TYPE
POW_A[9] <= WAVE_DATA_A_reg[9].DB_MAX_OUTPUT_PORT_TYPE
POW_A[10] <= WAVE_DATA_A_reg[10].DB_MAX_OUTPUT_PORT_TYPE
POW_A[11] <= WAVE_DATA_A_reg[11].DB_MAX_OUTPUT_PORT_TYPE
POW_A[12] <= WAVE_DATA_A_reg[12].DB_MAX_OUTPUT_PORT_TYPE
POW_A[13] <= WAVE_DATA_A_reg[13].DB_MAX_OUTPUT_PORT_TYPE


|TOP|DA_WAVEFORM_A:inst15|sinromvpp:sinromvpp_inst
address[0] => address[0].IN1
address[1] => address[1].IN1
address[2] => address[2].IN1
address[3] => address[3].IN1
address[4] => address[4].IN1
address[5] => address[5].IN1
address[6] => address[6].IN1
address[7] => address[7].IN1
address[8] => address[8].IN1
address[9] => address[9].IN1
clock => clock.IN1
q[0] <= altsyncram:altsyncram_component.q_a
q[1] <= altsyncram:altsyncram_component.q_a
q[2] <= altsyncram:altsyncram_component.q_a
q[3] <= altsyncram:altsyncram_component.q_a
q[4] <= altsyncram:altsyncram_component.q_a
q[5] <= altsyncram:altsyncram_component.q_a
q[6] <= altsyncram:altsyncram_component.q_a
q[7] <= altsyncram:altsyncram_component.q_a
q[8] <= altsyncram:altsyncram_component.q_a
q[9] <= altsyncram:altsyncram_component.q_a
q[10] <= altsyncram:altsyncram_component.q_a
q[11] <= altsyncram:altsyncram_component.q_a
q[12] <= altsyncram:altsyncram_component.q_a
q[13] <= altsyncram:altsyncram_component.q_a


|TOP|DA_WAVEFORM_A:inst15|sinromvpp:sinromvpp_inst|altsyncram:altsyncram_component
wren_a => ~NO_FANOUT~
rden_a => ~NO_FANOUT~
wren_b => ~NO_FANOUT~
rden_b => ~NO_FANOUT~
data_a[0] => ~NO_FANOUT~
data_a[1] => ~NO_FANOUT~
data_a[2] => ~NO_FANOUT~
data_a[3] => ~NO_FANOUT~
data_a[4] => ~NO_FANOUT~
data_a[5] => ~NO_FANOUT~
data_a[6] => ~NO_FANOUT~
data_a[7] => ~NO_FANOUT~
data_a[8] => ~NO_FANOUT~
data_a[9] => ~NO_FANOUT~
data_a[10] => ~NO_FANOUT~
data_a[11] => ~NO_FANOUT~
data_a[12] => ~NO_FANOUT~
data_a[13] => ~NO_FANOUT~
data_b[0] => ~NO_FANOUT~
address_a[0] => altsyncram_h3b1:auto_generated.address_a[0]
address_a[1] => altsyncram_h3b1:auto_generated.address_a[1]
address_a[2] => altsyncram_h3b1:auto_generated.address_a[2]
address_a[3] => altsyncram_h3b1:auto_generated.address_a[3]
address_a[4] => altsyncram_h3b1:auto_generated.address_a[4]
address_a[5] => altsyncram_h3b1:auto_generated.address_a[5]
address_a[6] => altsyncram_h3b1:auto_generated.address_a[6]
address_a[7] => altsyncram_h3b1:auto_generated.address_a[7]
address_a[8] => altsyncram_h3b1:auto_generated.address_a[8]
address_a[9] => altsyncram_h3b1:auto_generated.address_a[9]
address_b[0] => ~NO_FANOUT~
addressstall_a => ~NO_FANOUT~
addressstall_b => ~NO_FANOUT~
clock0 => altsyncram_h3b1:auto_generated.clock0
clock1 => ~NO_FANOUT~
clocken0 => ~NO_FANOUT~
clocken1 => ~NO_FANOUT~
clocken2 => ~NO_FANOUT~
clocken3 => ~NO_FANOUT~
aclr0 => ~NO_FANOUT~
aclr1 => ~NO_FANOUT~
byteena_a[0] => ~NO_FANOUT~
byteena_b[0] => ~NO_FANOUT~
q_a[0] <= altsyncram_h3b1:auto_generated.q_a[0]
q_a[1] <= altsyncram_h3b1:auto_generated.q_a[1]
q_a[2] <= altsyncram_h3b1:auto_generated.q_a[2]
q_a[3] <= altsyncram_h3b1:auto_generated.q_a[3]
q_a[4] <= altsyncram_h3b1:auto_generated.q_a[4]
q_a[5] <= altsyncram_h3b1:auto_generated.q_a[5]
q_a[6] <= altsyncram_h3b1:auto_generated.q_a[6]
q_a[7] <= altsyncram_h3b1:auto_generated.q_a[7]
q_a[8] <= altsyncram_h3b1:auto_generated.q_a[8]
q_a[9] <= altsyncram_h3b1:auto_generated.q_a[9]
q_a[10] <= altsyncram_h3b1:auto_generated.q_a[10]
q_a[11] <= altsyncram_h3b1:auto_generated.q_a[11]
q_a[12] <= altsyncram_h3b1:auto_generated.q_a[12]
q_a[13] <= altsyncram_h3b1:auto_generated.q_a[13]
q_b[0] <= <GND>
eccstatus[0] <= <GND>
eccstatus[1] <= <GND>
eccstatus[2] <= <GND>


|TOP|DA_WAVEFORM_A:inst15|sinromvpp:sinromvpp_inst|altsyncram:altsyncram_component|altsyncram_h3b1:auto_generated
address_a[0] => ram_block1a0.PORTAADDR
address_a[0] => ram_block1a1.PORTAADDR
address_a[0] => ram_block1a2.PORTAADDR
address_a[0] => ram_block1a3.PORTAADDR
address_a[0] => ram_block1a4.PORTAADDR
address_a[0] => ram_block1a5.PORTAADDR
address_a[0] => ram_block1a6.PORTAADDR
address_a[0] => ram_block1a7.PORTAADDR
address_a[0] => ram_block1a8.PORTAADDR
address_a[0] => ram_block1a9.PORTAADDR
address_a[0] => ram_block1a10.PORTAADDR
address_a[0] => ram_block1a11.PORTAADDR
address_a[0] => ram_block1a12.PORTAADDR
address_a[0] => ram_block1a13.PORTAADDR
address_a[1] => ram_block1a0.PORTAADDR1
address_a[1] => ram_block1a1.PORTAADDR1
address_a[1] => ram_block1a2.PORTAADDR1
address_a[1] => ram_block1a3.PORTAADDR1
address_a[1] => ram_block1a4.PORTAADDR1
address_a[1] => ram_block1a5.PORTAADDR1
address_a[1] => ram_block1a6.PORTAADDR1
address_a[1] => ram_block1a7.PORTAADDR1
address_a[1] => ram_block1a8.PORTAADDR1
address_a[1] => ram_block1a9.PORTAADDR1
address_a[1] => ram_block1a10.PORTAADDR1
address_a[1] => ram_block1a11.PORTAADDR1
address_a[1] => ram_block1a12.PORTAADDR1
address_a[1] => ram_block1a13.PORTAADDR1
address_a[2] => ram_block1a0.PORTAADDR2
address_a[2] => ram_block1a1.PORTAADDR2
address_a[2] => ram_block1a2.PORTAADDR2
address_a[2] => ram_block1a3.PORTAADDR2
address_a[2] => ram_block1a4.PORTAADDR2
address_a[2] => ram_block1a5.PORTAADDR2
address_a[2] => ram_block1a6.PORTAADDR2
address_a[2] => ram_block1a7.PORTAADDR2
address_a[2] => ram_block1a8.PORTAADDR2
address_a[2] => ram_block1a9.PORTAADDR2
address_a[2] => ram_block1a10.PORTAADDR2
address_a[2] => ram_block1a11.PORTAADDR2
address_a[2] => ram_block1a12.PORTAADDR2
address_a[2] => ram_block1a13.PORTAADDR2
address_a[3] => ram_block1a0.PORTAADDR3
address_a[3] => ram_block1a1.PORTAADDR3
address_a[3] => ram_block1a2.PORTAADDR3
address_a[3] => ram_block1a3.PORTAADDR3
address_a[3] => ram_block1a4.PORTAADDR3
address_a[3] => ram_block1a5.PORTAADDR3
address_a[3] => ram_block1a6.PORTAADDR3
address_a[3] => ram_block1a7.PORTAADDR3
address_a[3] => ram_block1a8.PORTAADDR3
address_a[3] => ram_block1a9.PORTAADDR3
address_a[3] => ram_block1a10.PORTAADDR3
address_a[3] => ram_block1a11.PORTAADDR3
address_a[3] => ram_block1a12.PORTAADDR3
address_a[3] => ram_block1a13.PORTAADDR3
address_a[4] => ram_block1a0.PORTAADDR4
address_a[4] => ram_block1a1.PORTAADDR4
address_a[4] => ram_block1a2.PORTAADDR4
address_a[4] => ram_block1a3.PORTAADDR4
address_a[4] => ram_block1a4.PORTAADDR4
address_a[4] => ram_block1a5.PORTAADDR4
address_a[4] => ram_block1a6.PORTAADDR4
address_a[4] => ram_block1a7.PORTAADDR4
address_a[4] => ram_block1a8.PORTAADDR4
address_a[4] => ram_block1a9.PORTAADDR4
address_a[4] => ram_block1a10.PORTAADDR4
address_a[4] => ram_block1a11.PORTAADDR4
address_a[4] => ram_block1a12.PORTAADDR4
address_a[4] => ram_block1a13.PORTAADDR4
address_a[5] => ram_block1a0.PORTAADDR5
address_a[5] => ram_block1a1.PORTAADDR5
address_a[5] => ram_block1a2.PORTAADDR5
address_a[5] => ram_block1a3.PORTAADDR5
address_a[5] => ram_block1a4.PORTAADDR5
address_a[5] => ram_block1a5.PORTAADDR5
address_a[5] => ram_block1a6.PORTAADDR5
address_a[5] => ram_block1a7.PORTAADDR5
address_a[5] => ram_block1a8.PORTAADDR5
address_a[5] => ram_block1a9.PORTAADDR5
address_a[5] => ram_block1a10.PORTAADDR5
address_a[5] => ram_block1a11.PORTAADDR5
address_a[5] => ram_block1a12.PORTAADDR5
address_a[5] => ram_block1a13.PORTAADDR5
address_a[6] => ram_block1a0.PORTAADDR6
address_a[6] => ram_block1a1.PORTAADDR6
address_a[6] => ram_block1a2.PORTAADDR6
address_a[6] => ram_block1a3.PORTAADDR6
address_a[6] => ram_block1a4.PORTAADDR6
address_a[6] => ram_block1a5.PORTAADDR6
address_a[6] => ram_block1a6.PORTAADDR6
address_a[6] => ram_block1a7.PORTAADDR6
address_a[6] => ram_block1a8.PORTAADDR6
address_a[6] => ram_block1a9.PORTAADDR6
address_a[6] => ram_block1a10.PORTAADDR6
address_a[6] => ram_block1a11.PORTAADDR6
address_a[6] => ram_block1a12.PORTAADDR6
address_a[6] => ram_block1a13.PORTAADDR6
address_a[7] => ram_block1a0.PORTAADDR7
address_a[7] => ram_block1a1.PORTAADDR7
address_a[7] => ram_block1a2.PORTAADDR7
address_a[7] => ram_block1a3.PORTAADDR7
address_a[7] => ram_block1a4.PORTAADDR7
address_a[7] => ram_block1a5.PORTAADDR7
address_a[7] => ram_block1a6.PORTAADDR7
address_a[7] => ram_block1a7.PORTAADDR7
address_a[7] => ram_block1a8.PORTAADDR7
address_a[7] => ram_block1a9.PORTAADDR7
address_a[7] => ram_block1a10.PORTAADDR7
address_a[7] => ram_block1a11.PORTAADDR7
address_a[7] => ram_block1a12.PORTAADDR7
address_a[7] => ram_block1a13.PORTAADDR7
address_a[8] => ram_block1a0.PORTAADDR8
address_a[8] => ram_block1a1.PORTAADDR8
address_a[8] => ram_block1a2.PORTAADDR8
address_a[8] => ram_block1a3.PORTAADDR8
address_a[8] => ram_block1a4.PORTAADDR8
address_a[8] => ram_block1a5.PORTAADDR8
address_a[8] => ram_block1a6.PORTAADDR8
address_a[8] => ram_block1a7.PORTAADDR8
address_a[8] => ram_block1a8.PORTAADDR8
address_a[8] => ram_block1a9.PORTAADDR8
address_a[8] => ram_block1a10.PORTAADDR8
address_a[8] => ram_block1a11.PORTAADDR8
address_a[8] => ram_block1a12.PORTAADDR8
address_a[8] => ram_block1a13.PORTAADDR8
address_a[9] => ram_block1a0.PORTAADDR9
address_a[9] => ram_block1a1.PORTAADDR9
address_a[9] => ram_block1a2.PORTAADDR9
address_a[9] => ram_block1a3.PORTAADDR9
address_a[9] => ram_block1a4.PORTAADDR9
address_a[9] => ram_block1a5.PORTAADDR9
address_a[9] => ram_block1a6.PORTAADDR9
address_a[9] => ram_block1a7.PORTAADDR9
address_a[9] => ram_block1a8.PORTAADDR9
address_a[9] => ram_block1a9.PORTAADDR9
address_a[9] => ram_block1a10.PORTAADDR9
address_a[9] => ram_block1a11.PORTAADDR9
address_a[9] => ram_block1a12.PORTAADDR9
address_a[9] => ram_block1a13.PORTAADDR9
clock0 => ram_block1a0.CLK0
clock0 => ram_block1a1.CLK0
clock0 => ram_block1a2.CLK0
clock0 => ram_block1a3.CLK0
clock0 => ram_block1a4.CLK0
clock0 => ram_block1a5.CLK0
clock0 => ram_block1a6.CLK0
clock0 => ram_block1a7.CLK0
clock0 => ram_block1a8.CLK0
clock0 => ram_block1a9.CLK0
clock0 => ram_block1a10.CLK0
clock0 => ram_block1a11.CLK0
clock0 => ram_block1a12.CLK0
clock0 => ram_block1a13.CLK0
q_a[0] <= ram_block1a0.PORTADATAOUT
q_a[1] <= ram_block1a1.PORTADATAOUT
q_a[2] <= ram_block1a2.PORTADATAOUT
q_a[3] <= ram_block1a3.PORTADATAOUT
q_a[4] <= ram_block1a4.PORTADATAOUT
q_a[5] <= ram_block1a5.PORTADATAOUT
q_a[6] <= ram_block1a6.PORTADATAOUT
q_a[7] <= ram_block1a7.PORTADATAOUT
q_a[8] <= ram_block1a8.PORTADATAOUT
q_a[9] <= ram_block1a9.PORTADATAOUT
q_a[10] <= ram_block1a10.PORTADATAOUT
q_a[11] <= ram_block1a11.PORTADATAOUT
q_a[12] <= ram_block1a12.PORTADATAOUT
q_a[13] <= ram_block1a13.PORTADATAOUT


|TOP|DA_WAVEFORM_A:inst15|sqaure_rom:sqaure_rom_inst
address[0] => address[0].IN1
address[1] => address[1].IN1
address[2] => address[2].IN1
address[3] => address[3].IN1
address[4] => address[4].IN1
address[5] => address[5].IN1
address[6] => address[6].IN1
address[7] => address[7].IN1
address[8] => address[8].IN1
address[9] => address[9].IN1
clock => clock.IN1
q[0] <= altsyncram:altsyncram_component.q_a
q[1] <= altsyncram:altsyncram_component.q_a
q[2] <= altsyncram:altsyncram_component.q_a
q[3] <= altsyncram:altsyncram_component.q_a
q[4] <= altsyncram:altsyncram_component.q_a
q[5] <= altsyncram:altsyncram_component.q_a
q[6] <= altsyncram:altsyncram_component.q_a
q[7] <= altsyncram:altsyncram_component.q_a
q[8] <= altsyncram:altsyncram_component.q_a
q[9] <= altsyncram:altsyncram_component.q_a
q[10] <= altsyncram:altsyncram_component.q_a
q[11] <= altsyncram:altsyncram_component.q_a
q[12] <= altsyncram:altsyncram_component.q_a
q[13] <= altsyncram:altsyncram_component.q_a


|TOP|DA_WAVEFORM_A:inst15|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component
wren_a => ~NO_FANOUT~
rden_a => ~NO_FANOUT~
wren_b => ~NO_FANOUT~
rden_b => ~NO_FANOUT~
data_a[0] => ~NO_FANOUT~
data_a[1] => ~NO_FANOUT~
data_a[2] => ~NO_FANOUT~
data_a[3] => ~NO_FANOUT~
data_a[4] => ~NO_FANOUT~
data_a[5] => ~NO_FANOUT~
data_a[6] => ~NO_FANOUT~
data_a[7] => ~NO_FANOUT~
data_a[8] => ~NO_FANOUT~
data_a[9] => ~NO_FANOUT~
data_a[10] => ~NO_FANOUT~
data_a[11] => ~NO_FANOUT~
data_a[12] => ~NO_FANOUT~
data_a[13] => ~NO_FANOUT~
data_b[0] => ~NO_FANOUT~
address_a[0] => altsyncram_odb1:auto_generated.address_a[0]
address_a[1] => altsyncram_odb1:auto_generated.address_a[1]
address_a[2] => altsyncram_odb1:auto_generated.address_a[2]
address_a[3] => altsyncram_odb1:auto_generated.address_a[3]
address_a[4] => altsyncram_odb1:auto_generated.address_a[4]
address_a[5] => altsyncram_odb1:auto_generated.address_a[5]
address_a[6] => altsyncram_odb1:auto_generated.address_a[6]
address_a[7] => altsyncram_odb1:auto_generated.address_a[7]
address_a[8] => altsyncram_odb1:auto_generated.address_a[8]
address_a[9] => altsyncram_odb1:auto_generated.address_a[9]
address_b[0] => ~NO_FANOUT~
addressstall_a => ~NO_FANOUT~
addressstall_b => ~NO_FANOUT~
clock0 => altsyncram_odb1:auto_generated.clock0
clock1 => ~NO_FANOUT~
clocken0 => ~NO_FANOUT~
clocken1 => ~NO_FANOUT~
clocken2 => ~NO_FANOUT~
clocken3 => ~NO_FANOUT~
aclr0 => ~NO_FANOUT~
aclr1 => ~NO_FANOUT~
byteena_a[0] => ~NO_FANOUT~
byteena_b[0] => ~NO_FANOUT~
q_a[0] <= altsyncram_odb1:auto_generated.q_a[0]
q_a[1] <= altsyncram_odb1:auto_generated.q_a[1]
q_a[2] <= altsyncram_odb1:auto_generated.q_a[2]
q_a[3] <= altsyncram_odb1:auto_generated.q_a[3]
q_a[4] <= altsyncram_odb1:auto_generated.q_a[4]
q_a[5] <= altsyncram_odb1:auto_generated.q_a[5]
q_a[6] <= altsyncram_odb1:auto_generated.q_a[6]
q_a[7] <= altsyncram_odb1:auto_generated.q_a[7]
q_a[8] <= altsyncram_odb1:auto_generated.q_a[8]
q_a[9] <= altsyncram_odb1:auto_generated.q_a[9]
q_a[10] <= altsyncram_odb1:auto_generated.q_a[10]
q_a[11] <= altsyncram_odb1:auto_generated.q_a[11]
q_a[12] <= altsyncram_odb1:auto_generated.q_a[12]
q_a[13] <= altsyncram_odb1:auto_generated.q_a[13]
q_b[0] <= <GND>
eccstatus[0] <= <GND>
eccstatus[1] <= <GND>
eccstatus[2] <= <GND>


|TOP|DA_WAVEFORM_A:inst15|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component|altsyncram_odb1:auto_generated
address_a[0] => ram_block1a0.PORTAADDR
address_a[0] => ram_block1a1.PORTAADDR
address_a[0] => ram_block1a2.PORTAADDR
address_a[0] => ram_block1a3.PORTAADDR
address_a[0] => ram_block1a4.PORTAADDR
address_a[0] => ram_block1a5.PORTAADDR
address_a[0] => ram_block1a6.PORTAADDR
address_a[0] => ram_block1a7.PORTAADDR
address_a[0] => ram_block1a8.PORTAADDR
address_a[0] => ram_block1a9.PORTAADDR
address_a[0] => ram_block1a10.PORTAADDR
address_a[0] => ram_block1a11.PORTAADDR
address_a[0] => ram_block1a12.PORTAADDR
address_a[0] => ram_block1a13.PORTAADDR
address_a[1] => ram_block1a0.PORTAADDR1
address_a[1] => ram_block1a1.PORTAADDR1
address_a[1] => ram_block1a2.PORTAADDR1
address_a[1] => ram_block1a3.PORTAADDR1
address_a[1] => ram_block1a4.PORTAADDR1
address_a[1] => ram_block1a5.PORTAADDR1
address_a[1] => ram_block1a6.PORTAADDR1
address_a[1] => ram_block1a7.PORTAADDR1
address_a[1] => ram_block1a8.PORTAADDR1
address_a[1] => ram_block1a9.PORTAADDR1
address_a[1] => ram_block1a10.PORTAADDR1
address_a[1] => ram_block1a11.PORTAADDR1
address_a[1] => ram_block1a12.PORTAADDR1
address_a[1] => ram_block1a13.PORTAADDR1
address_a[2] => ram_block1a0.PORTAADDR2
address_a[2] => ram_block1a1.PORTAADDR2
address_a[2] => ram_block1a2.PORTAADDR2
address_a[2] => ram_block1a3.PORTAADDR2
address_a[2] => ram_block1a4.PORTAADDR2
address_a[2] => ram_block1a5.PORTAADDR2
address_a[2] => ram_block1a6.PORTAADDR2
address_a[2] => ram_block1a7.PORTAADDR2
address_a[2] => ram_block1a8.PORTAADDR2
address_a[2] => ram_block1a9.PORTAADDR2
address_a[2] => ram_block1a10.PORTAADDR2
address_a[2] => ram_block1a11.PORTAADDR2
address_a[2] => ram_block1a12.PORTAADDR2
address_a[2] => ram_block1a13.PORTAADDR2
address_a[3] => ram_block1a0.PORTAADDR3
address_a[3] => ram_block1a1.PORTAADDR3
address_a[3] => ram_block1a2.PORTAADDR3
address_a[3] => ram_block1a3.PORTAADDR3
address_a[3] => ram_block1a4.PORTAADDR3
address_a[3] => ram_block1a5.PORTAADDR3
address_a[3] => ram_block1a6.PORTAADDR3
address_a[3] => ram_block1a7.PORTAADDR3
address_a[3] => ram_block1a8.PORTAADDR3
address_a[3] => ram_block1a9.PORTAADDR3
address_a[3] => ram_block1a10.PORTAADDR3
address_a[3] => ram_block1a11.PORTAADDR3
address_a[3] => ram_block1a12.PORTAADDR3
address_a[3] => ram_block1a13.PORTAADDR3
address_a[4] => ram_block1a0.PORTAADDR4
address_a[4] => ram_block1a1.PORTAADDR4
address_a[4] => ram_block1a2.PORTAADDR4
address_a[4] => ram_block1a3.PORTAADDR4
address_a[4] => ram_block1a4.PORTAADDR4
address_a[4] => ram_block1a5.PORTAADDR4
address_a[4] => ram_block1a6.PORTAADDR4
address_a[4] => ram_block1a7.PORTAADDR4
address_a[4] => ram_block1a8.PORTAADDR4
address_a[4] => ram_block1a9.PORTAADDR4
address_a[4] => ram_block1a10.PORTAADDR4
address_a[4] => ram_block1a11.PORTAADDR4
address_a[4] => ram_block1a12.PORTAADDR4
address_a[4] => ram_block1a13.PORTAADDR4
address_a[5] => ram_block1a0.PORTAADDR5
address_a[5] => ram_block1a1.PORTAADDR5
address_a[5] => ram_block1a2.PORTAADDR5
address_a[5] => ram_block1a3.PORTAADDR5
address_a[5] => ram_block1a4.PORTAADDR5
address_a[5] => ram_block1a5.PORTAADDR5
address_a[5] => ram_block1a6.PORTAADDR5
address_a[5] => ram_block1a7.PORTAADDR5
address_a[5] => ram_block1a8.PORTAADDR5
address_a[5] => ram_block1a9.PORTAADDR5
address_a[5] => ram_block1a10.PORTAADDR5
address_a[5] => ram_block1a11.PORTAADDR5
address_a[5] => ram_block1a12.PORTAADDR5
address_a[5] => ram_block1a13.PORTAADDR5
address_a[6] => ram_block1a0.PORTAADDR6
address_a[6] => ram_block1a1.PORTAADDR6
address_a[6] => ram_block1a2.PORTAADDR6
address_a[6] => ram_block1a3.PORTAADDR6
address_a[6] => ram_block1a4.PORTAADDR6
address_a[6] => ram_block1a5.PORTAADDR6
address_a[6] => ram_block1a6.PORTAADDR6
address_a[6] => ram_block1a7.PORTAADDR6
address_a[6] => ram_block1a8.PORTAADDR6
address_a[6] => ram_block1a9.PORTAADDR6
address_a[6] => ram_block1a10.PORTAADDR6
address_a[6] => ram_block1a11.PORTAADDR6
address_a[6] => ram_block1a12.PORTAADDR6
address_a[6] => ram_block1a13.PORTAADDR6
address_a[7] => ram_block1a0.PORTAADDR7
address_a[7] => ram_block1a1.PORTAADDR7
address_a[7] => ram_block1a2.PORTAADDR7
address_a[7] => ram_block1a3.PORTAADDR7
address_a[7] => ram_block1a4.PORTAADDR7
address_a[7] => ram_block1a5.PORTAADDR7
address_a[7] => ram_block1a6.PORTAADDR7
address_a[7] => ram_block1a7.PORTAADDR7
address_a[7] => ram_block1a8.PORTAADDR7
address_a[7] => ram_block1a9.PORTAADDR7
address_a[7] => ram_block1a10.PORTAADDR7
address_a[7] => ram_block1a11.PORTAADDR7
address_a[7] => ram_block1a12.PORTAADDR7
address_a[7] => ram_block1a13.PORTAADDR7
address_a[8] => ram_block1a0.PORTAADDR8
address_a[8] => ram_block1a1.PORTAADDR8
address_a[8] => ram_block1a2.PORTAADDR8
address_a[8] => ram_block1a3.PORTAADDR8
address_a[8] => ram_block1a4.PORTAADDR8
address_a[8] => ram_block1a5.PORTAADDR8
address_a[8] => ram_block1a6.PORTAADDR8
address_a[8] => ram_block1a7.PORTAADDR8
address_a[8] => ram_block1a8.PORTAADDR8
address_a[8] => ram_block1a9.PORTAADDR8
address_a[8] => ram_block1a10.PORTAADDR8
address_a[8] => ram_block1a11.PORTAADDR8
address_a[8] => ram_block1a12.PORTAADDR8
address_a[8] => ram_block1a13.PORTAADDR8
address_a[9] => ram_block1a0.PORTAADDR9
address_a[9] => ram_block1a1.PORTAADDR9
address_a[9] => ram_block1a2.PORTAADDR9
address_a[9] => ram_block1a3.PORTAADDR9
address_a[9] => ram_block1a4.PORTAADDR9
address_a[9] => ram_block1a5.PORTAADDR9
address_a[9] => ram_block1a6.PORTAADDR9
address_a[9] => ram_block1a7.PORTAADDR9
address_a[9] => ram_block1a8.PORTAADDR9
address_a[9] => ram_block1a9.PORTAADDR9
address_a[9] => ram_block1a10.PORTAADDR9
address_a[9] => ram_block1a11.PORTAADDR9
address_a[9] => ram_block1a12.PORTAADDR9
address_a[9] => ram_block1a13.PORTAADDR9
clock0 => ram_block1a0.CLK0
clock0 => ram_block1a1.CLK0
clock0 => ram_block1a2.CLK0
clock0 => ram_block1a3.CLK0
clock0 => ram_block1a4.CLK0
clock0 => ram_block1a5.CLK0
clock0 => ram_block1a6.CLK0
clock0 => ram_block1a7.CLK0
clock0 => ram_block1a8.CLK0
clock0 => ram_block1a9.CLK0
clock0 => ram_block1a10.CLK0
clock0 => ram_block1a11.CLK0
clock0 => ram_block1a12.CLK0
clock0 => ram_block1a13.CLK0
q_a[0] <= ram_block1a0.PORTADATAOUT
q_a[1] <= ram_block1a1.PORTADATAOUT
q_a[2] <= ram_block1a2.PORTADATAOUT
q_a[3] <= ram_block1a3.PORTADATAOUT
q_a[4] <= ram_block1a4.PORTADATAOUT
q_a[5] <= ram_block1a5.PORTADATAOUT
q_a[6] <= ram_block1a6.PORTADATAOUT
q_a[7] <= ram_block1a7.PORTADATAOUT
q_a[8] <= ram_block1a8.PORTADATAOUT
q_a[9] <= ram_block1a9.PORTADATAOUT
q_a[10] <= ram_block1a10.PORTADATAOUT
q_a[11] <= ram_block1a11.PORTADATAOUT
q_a[12] <= ram_block1a12.PORTADATAOUT
q_a[13] <= ram_block1a13.PORTADATAOUT


|TOP|DA_WAVEFORM_A:inst15|triangle_rom:triangle_rom_inst
address[0] => address[0].IN1
address[1] => address[1].IN1
address[2] => address[2].IN1
address[3] => address[3].IN1
address[4] => address[4].IN1
address[5] => address[5].IN1
address[6] => address[6].IN1
address[7] => address[7].IN1
address[8] => address[8].IN1
address[9] => address[9].IN1
clock => clock.IN1
q[0] <= altsyncram:altsyncram_component.q_a
q[1] <= altsyncram:altsyncram_component.q_a
q[2] <= altsyncram:altsyncram_component.q_a
q[3] <= altsyncram:altsyncram_component.q_a
q[4] <= altsyncram:altsyncram_component.q_a
q[5] <= altsyncram:altsyncram_component.q_a
q[6] <= altsyncram:altsyncram_component.q_a
q[7] <= altsyncram:altsyncram_component.q_a
q[8] <= altsyncram:altsyncram_component.q_a
q[9] <= altsyncram:altsyncram_component.q_a
q[10] <= altsyncram:altsyncram_component.q_a
q[11] <= altsyncram:altsyncram_component.q_a
q[12] <= altsyncram:altsyncram_component.q_a
q[13] <= altsyncram:altsyncram_component.q_a


|TOP|DA_WAVEFORM_A:inst15|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component
wren_a => ~NO_FANOUT~
rden_a => ~NO_FANOUT~
wren_b => ~NO_FANOUT~
rden_b => ~NO_FANOUT~
data_a[0] => ~NO_FANOUT~
data_a[1] => ~NO_FANOUT~
data_a[2] => ~NO_FANOUT~
data_a[3] => ~NO_FANOUT~
data_a[4] => ~NO_FANOUT~
data_a[5] => ~NO_FANOUT~
data_a[6] => ~NO_FANOUT~
data_a[7] => ~NO_FANOUT~
data_a[8] => ~NO_FANOUT~
data_a[9] => ~NO_FANOUT~
data_a[10] => ~NO_FANOUT~
data_a[11] => ~NO_FANOUT~
data_a[12] => ~NO_FANOUT~
data_a[13] => ~NO_FANOUT~
data_b[0] => ~NO_FANOUT~
address_a[0] => altsyncram_tjb1:auto_generated.address_a[0]
address_a[1] => altsyncram_tjb1:auto_generated.address_a[1]
address_a[2] => altsyncram_tjb1:auto_generated.address_a[2]
address_a[3] => altsyncram_tjb1:auto_generated.address_a[3]
address_a[4] => altsyncram_tjb1:auto_generated.address_a[4]
address_a[5] => altsyncram_tjb1:auto_generated.address_a[5]
address_a[6] => altsyncram_tjb1:auto_generated.address_a[6]
address_a[7] => altsyncram_tjb1:auto_generated.address_a[7]
address_a[8] => altsyncram_tjb1:auto_generated.address_a[8]
address_a[9] => altsyncram_tjb1:auto_generated.address_a[9]
address_b[0] => ~NO_FANOUT~
addressstall_a => ~NO_FANOUT~
addressstall_b => ~NO_FANOUT~
clock0 => altsyncram_tjb1:auto_generated.clock0
clock1 => ~NO_FANOUT~
clocken0 => ~NO_FANOUT~
clocken1 => ~NO_FANOUT~
clocken2 => ~NO_FANOUT~
clocken3 => ~NO_FANOUT~
aclr0 => ~NO_FANOUT~
aclr1 => ~NO_FANOUT~
byteena_a[0] => ~NO_FANOUT~
byteena_b[0] => ~NO_FANOUT~
q_a[0] <= altsyncram_tjb1:auto_generated.q_a[0]
q_a[1] <= altsyncram_tjb1:auto_generated.q_a[1]
q_a[2] <= altsyncram_tjb1:auto_generated.q_a[2]
q_a[3] <= altsyncram_tjb1:auto_generated.q_a[3]
q_a[4] <= altsyncram_tjb1:auto_generated.q_a[4]
q_a[5] <= altsyncram_tjb1:auto_generated.q_a[5]
q_a[6] <= altsyncram_tjb1:auto_generated.q_a[6]
q_a[7] <= altsyncram_tjb1:auto_generated.q_a[7]
q_a[8] <= altsyncram_tjb1:auto_generated.q_a[8]
q_a[9] <= altsyncram_tjb1:auto_generated.q_a[9]
q_a[10] <= altsyncram_tjb1:auto_generated.q_a[10]
q_a[11] <= altsyncram_tjb1:auto_generated.q_a[11]
q_a[12] <= altsyncram_tjb1:auto_generated.q_a[12]
q_a[13] <= altsyncram_tjb1:auto_generated.q_a[13]
q_b[0] <= <GND>
eccstatus[0] <= <GND>
eccstatus[1] <= <GND>
eccstatus[2] <= <GND>


|TOP|DA_WAVEFORM_A:inst15|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component|altsyncram_tjb1:auto_generated
address_a[0] => ram_block1a0.PORTAADDR
address_a[0] => ram_block1a1.PORTAADDR
address_a[0] => ram_block1a2.PORTAADDR
address_a[0] => ram_block1a3.PORTAADDR
address_a[0] => ram_block1a4.PORTAADDR
address_a[0] => ram_block1a5.PORTAADDR
address_a[0] => ram_block1a6.PORTAADDR
address_a[0] => ram_block1a7.PORTAADDR
address_a[0] => ram_block1a8.PORTAADDR
address_a[0] => ram_block1a9.PORTAADDR
address_a[0] => ram_block1a10.PORTAADDR
address_a[0] => ram_block1a11.PORTAADDR
address_a[0] => ram_block1a12.PORTAADDR
address_a[0] => ram_block1a13.PORTAADDR
address_a[1] => ram_block1a0.PORTAADDR1
address_a[1] => ram_block1a1.PORTAADDR1
address_a[1] => ram_block1a2.PORTAADDR1
address_a[1] => ram_block1a3.PORTAADDR1
address_a[1] => ram_block1a4.PORTAADDR1
address_a[1] => ram_block1a5.PORTAADDR1
address_a[1] => ram_block1a6.PORTAADDR1
address_a[1] => ram_block1a7.PORTAADDR1
address_a[1] => ram_block1a8.PORTAADDR1
address_a[1] => ram_block1a9.PORTAADDR1
address_a[1] => ram_block1a10.PORTAADDR1
address_a[1] => ram_block1a11.PORTAADDR1
address_a[1] => ram_block1a12.PORTAADDR1
address_a[1] => ram_block1a13.PORTAADDR1
address_a[2] => ram_block1a0.PORTAADDR2
address_a[2] => ram_block1a1.PORTAADDR2
address_a[2] => ram_block1a2.PORTAADDR2
address_a[2] => ram_block1a3.PORTAADDR2
address_a[2] => ram_block1a4.PORTAADDR2
address_a[2] => ram_block1a5.PORTAADDR2
address_a[2] => ram_block1a6.PORTAADDR2
address_a[2] => ram_block1a7.PORTAADDR2
address_a[2] => ram_block1a8.PORTAADDR2
address_a[2] => ram_block1a9.PORTAADDR2
address_a[2] => ram_block1a10.PORTAADDR2
address_a[2] => ram_block1a11.PORTAADDR2
address_a[2] => ram_block1a12.PORTAADDR2
address_a[2] => ram_block1a13.PORTAADDR2
address_a[3] => ram_block1a0.PORTAADDR3
address_a[3] => ram_block1a1.PORTAADDR3
address_a[3] => ram_block1a2.PORTAADDR3
address_a[3] => ram_block1a3.PORTAADDR3
address_a[3] => ram_block1a4.PORTAADDR3
address_a[3] => ram_block1a5.PORTAADDR3
address_a[3] => ram_block1a6.PORTAADDR3
address_a[3] => ram_block1a7.PORTAADDR3
address_a[3] => ram_block1a8.PORTAADDR3
address_a[3] => ram_block1a9.PORTAADDR3
address_a[3] => ram_block1a10.PORTAADDR3
address_a[3] => ram_block1a11.PORTAADDR3
address_a[3] => ram_block1a12.PORTAADDR3
address_a[3] => ram_block1a13.PORTAADDR3
address_a[4] => ram_block1a0.PORTAADDR4
address_a[4] => ram_block1a1.PORTAADDR4
address_a[4] => ram_block1a2.PORTAADDR4
address_a[4] => ram_block1a3.PORTAADDR4
address_a[4] => ram_block1a4.PORTAADDR4
address_a[4] => ram_block1a5.PORTAADDR4
address_a[4] => ram_block1a6.PORTAADDR4
address_a[4] => ram_block1a7.PORTAADDR4
address_a[4] => ram_block1a8.PORTAADDR4
address_a[4] => ram_block1a9.PORTAADDR4
address_a[4] => ram_block1a10.PORTAADDR4
address_a[4] => ram_block1a11.PORTAADDR4
address_a[4] => ram_block1a12.PORTAADDR4
address_a[4] => ram_block1a13.PORTAADDR4
address_a[5] => ram_block1a0.PORTAADDR5
address_a[5] => ram_block1a1.PORTAADDR5
address_a[5] => ram_block1a2.PORTAADDR5
address_a[5] => ram_block1a3.PORTAADDR5
address_a[5] => ram_block1a4.PORTAADDR5
address_a[5] => ram_block1a5.PORTAADDR5
address_a[5] => ram_block1a6.PORTAADDR5
address_a[5] => ram_block1a7.PORTAADDR5
address_a[5] => ram_block1a8.PORTAADDR5
address_a[5] => ram_block1a9.PORTAADDR5
address_a[5] => ram_block1a10.PORTAADDR5
address_a[5] => ram_block1a11.PORTAADDR5
address_a[5] => ram_block1a12.PORTAADDR5
address_a[5] => ram_block1a13.PORTAADDR5
address_a[6] => ram_block1a0.PORTAADDR6
address_a[6] => ram_block1a1.PORTAADDR6
address_a[6] => ram_block1a2.PORTAADDR6
address_a[6] => ram_block1a3.PORTAADDR6
address_a[6] => ram_block1a4.PORTAADDR6
address_a[6] => ram_block1a5.PORTAADDR6
address_a[6] => ram_block1a6.PORTAADDR6
address_a[6] => ram_block1a7.PORTAADDR6
address_a[6] => ram_block1a8.PORTAADDR6
address_a[6] => ram_block1a9.PORTAADDR6
address_a[6] => ram_block1a10.PORTAADDR6
address_a[6] => ram_block1a11.PORTAADDR6
address_a[6] => ram_block1a12.PORTAADDR6
address_a[6] => ram_block1a13.PORTAADDR6
address_a[7] => ram_block1a0.PORTAADDR7
address_a[7] => ram_block1a1.PORTAADDR7
address_a[7] => ram_block1a2.PORTAADDR7
address_a[7] => ram_block1a3.PORTAADDR7
address_a[7] => ram_block1a4.PORTAADDR7
address_a[7] => ram_block1a5.PORTAADDR7
address_a[7] => ram_block1a6.PORTAADDR7
address_a[7] => ram_block1a7.PORTAADDR7
address_a[7] => ram_block1a8.PORTAADDR7
address_a[7] => ram_block1a9.PORTAADDR7
address_a[7] => ram_block1a10.PORTAADDR7
address_a[7] => ram_block1a11.PORTAADDR7
address_a[7] => ram_block1a12.PORTAADDR7
address_a[7] => ram_block1a13.PORTAADDR7
address_a[8] => ram_block1a0.PORTAADDR8
address_a[8] => ram_block1a1.PORTAADDR8
address_a[8] => ram_block1a2.PORTAADDR8
address_a[8] => ram_block1a3.PORTAADDR8
address_a[8] => ram_block1a4.PORTAADDR8
address_a[8] => ram_block1a5.PORTAADDR8
address_a[8] => ram_block1a6.PORTAADDR8
address_a[8] => ram_block1a7.PORTAADDR8
address_a[8] => ram_block1a8.PORTAADDR8
address_a[8] => ram_block1a9.PORTAADDR8
address_a[8] => ram_block1a10.PORTAADDR8
address_a[8] => ram_block1a11.PORTAADDR8
address_a[8] => ram_block1a12.PORTAADDR8
address_a[8] => ram_block1a13.PORTAADDR8
address_a[9] => ram_block1a0.PORTAADDR9
address_a[9] => ram_block1a1.PORTAADDR9
address_a[9] => ram_block1a2.PORTAADDR9
address_a[9] => ram_block1a3.PORTAADDR9
address_a[9] => ram_block1a4.PORTAADDR9
address_a[9] => ram_block1a5.PORTAADDR9
address_a[9] => ram_block1a6.PORTAADDR9
address_a[9] => ram_block1a7.PORTAADDR9
address_a[9] => ram_block1a8.PORTAADDR9
address_a[9] => ram_block1a9.PORTAADDR9
address_a[9] => ram_block1a10.PORTAADDR9
address_a[9] => ram_block1a11.PORTAADDR9
address_a[9] => ram_block1a12.PORTAADDR9
address_a[9] => ram_block1a13.PORTAADDR9
clock0 => ram_block1a0.CLK0
clock0 => ram_block1a1.CLK0
clock0 => ram_block1a2.CLK0
clock0 => ram_block1a3.CLK0
clock0 => ram_block1a4.CLK0
clock0 => ram_block1a5.CLK0
clock0 => ram_block1a6.CLK0
clock0 => ram_block1a7.CLK0
clock0 => ram_block1a8.CLK0
clock0 => ram_block1a9.CLK0
clock0 => ram_block1a10.CLK0
clock0 => ram_block1a11.CLK0
clock0 => ram_block1a12.CLK0
clock0 => ram_block1a13.CLK0
q_a[0] <= ram_block1a0.PORTADATAOUT
q_a[1] <= ram_block1a1.PORTADATAOUT
q_a[2] <= ram_block1a2.PORTADATAOUT
q_a[3] <= ram_block1a3.PORTADATAOUT
q_a[4] <= ram_block1a4.PORTADATAOUT
q_a[5] <= ram_block1a5.PORTADATAOUT
q_a[6] <= ram_block1a6.PORTADATAOUT
q_a[7] <= ram_block1a7.PORTADATAOUT
q_a[8] <= ram_block1a8.PORTADATAOUT
q_a[9] <= ram_block1a9.PORTADATAOUT
q_a[10] <= ram_block1a10.PORTADATAOUT
q_a[11] <= ram_block1a11.PORTADATAOUT
q_a[12] <= ram_block1a12.PORTADATAOUT
q_a[13] <= ram_block1a13.PORTADATAOUT


|TOP|DA_WAVEFORM_A:inst15|sawtooth_rom:sawtooth_rom_inst
address[0] => address[0].IN1
address[1] => address[1].IN1
address[2] => address[2].IN1
address[3] => address[3].IN1
address[4] => address[4].IN1
address[5] => address[5].IN1
address[6] => address[6].IN1
address[7] => address[7].IN1
address[8] => address[8].IN1
address[9] => address[9].IN1
clock => clock.IN1
q[0] <= altsyncram:altsyncram_component.q_a
q[1] <= altsyncram:altsyncram_component.q_a
q[2] <= altsyncram:altsyncram_component.q_a
q[3] <= altsyncram:altsyncram_component.q_a
q[4] <= altsyncram:altsyncram_component.q_a
q[5] <= altsyncram:altsyncram_component.q_a
q[6] <= altsyncram:altsyncram_component.q_a
q[7] <= altsyncram:altsyncram_component.q_a
q[8] <= altsyncram:altsyncram_component.q_a
q[9] <= altsyncram:altsyncram_component.q_a
q[10] <= altsyncram:altsyncram_component.q_a
q[11] <= altsyncram:altsyncram_component.q_a
q[12] <= altsyncram:altsyncram_component.q_a
q[13] <= altsyncram:altsyncram_component.q_a


|TOP|DA_WAVEFORM_A:inst15|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component
wren_a => ~NO_FANOUT~
rden_a => ~NO_FANOUT~
wren_b => ~NO_FANOUT~
rden_b => ~NO_FANOUT~
data_a[0] => ~NO_FANOUT~
data_a[1] => ~NO_FANOUT~
data_a[2] => ~NO_FANOUT~
data_a[3] => ~NO_FANOUT~
data_a[4] => ~NO_FANOUT~
data_a[5] => ~NO_FANOUT~
data_a[6] => ~NO_FANOUT~
data_a[7] => ~NO_FANOUT~
data_a[8] => ~NO_FANOUT~
data_a[9] => ~NO_FANOUT~
data_a[10] => ~NO_FANOUT~
data_a[11] => ~NO_FANOUT~
data_a[12] => ~NO_FANOUT~
data_a[13] => ~NO_FANOUT~
data_b[0] => ~NO_FANOUT~
address_a[0] => altsyncram_0lb1:auto_generated.address_a[0]
address_a[1] => altsyncram_0lb1:auto_generated.address_a[1]
address_a[2] => altsyncram_0lb1:auto_generated.address_a[2]
address_a[3] => altsyncram_0lb1:auto_generated.address_a[3]
address_a[4] => altsyncram_0lb1:auto_generated.address_a[4]
address_a[5] => altsyncram_0lb1:auto_generated.address_a[5]
address_a[6] => altsyncram_0lb1:auto_generated.address_a[6]
address_a[7] => altsyncram_0lb1:auto_generated.address_a[7]
address_a[8] => altsyncram_0lb1:auto_generated.address_a[8]
address_a[9] => altsyncram_0lb1:auto_generated.address_a[9]
address_b[0] => ~NO_FANOUT~
addressstall_a => ~NO_FANOUT~
addressstall_b => ~NO_FANOUT~
clock0 => altsyncram_0lb1:auto_generated.clock0
clock1 => ~NO_FANOUT~
clocken0 => ~NO_FANOUT~
clocken1 => ~NO_FANOUT~
clocken2 => ~NO_FANOUT~
clocken3 => ~NO_FANOUT~
aclr0 => ~NO_FANOUT~
aclr1 => ~NO_FANOUT~
byteena_a[0] => ~NO_FANOUT~
byteena_b[0] => ~NO_FANOUT~
q_a[0] <= altsyncram_0lb1:auto_generated.q_a[0]
q_a[1] <= altsyncram_0lb1:auto_generated.q_a[1]
q_a[2] <= altsyncram_0lb1:auto_generated.q_a[2]
q_a[3] <= altsyncram_0lb1:auto_generated.q_a[3]
q_a[4] <= altsyncram_0lb1:auto_generated.q_a[4]
q_a[5] <= altsyncram_0lb1:auto_generated.q_a[5]
q_a[6] <= altsyncram_0lb1:auto_generated.q_a[6]
q_a[7] <= altsyncram_0lb1:auto_generated.q_a[7]
q_a[8] <= altsyncram_0lb1:auto_generated.q_a[8]
q_a[9] <= altsyncram_0lb1:auto_generated.q_a[9]
q_a[10] <= altsyncram_0lb1:auto_generated.q_a[10]
q_a[11] <= altsyncram_0lb1:auto_generated.q_a[11]
q_a[12] <= altsyncram_0lb1:auto_generated.q_a[12]
q_a[13] <= altsyncram_0lb1:auto_generated.q_a[13]
q_b[0] <= <GND>
eccstatus[0] <= <GND>
eccstatus[1] <= <GND>
eccstatus[2] <= <GND>


|TOP|DA_WAVEFORM_A:inst15|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component|altsyncram_0lb1:auto_generated
address_a[0] => ram_block1a0.PORTAADDR
address_a[0] => ram_block1a1.PORTAADDR
address_a[0] => ram_block1a2.PORTAADDR
address_a[0] => ram_block1a3.PORTAADDR
address_a[0] => ram_block1a4.PORTAADDR
address_a[0] => ram_block1a5.PORTAADDR
address_a[0] => ram_block1a6.PORTAADDR
address_a[0] => ram_block1a7.PORTAADDR
address_a[0] => ram_block1a8.PORTAADDR
address_a[0] => ram_block1a9.PORTAADDR
address_a[0] => ram_block1a10.PORTAADDR
address_a[0] => ram_block1a11.PORTAADDR
address_a[0] => ram_block1a12.PORTAADDR
address_a[0] => ram_block1a13.PORTAADDR
address_a[1] => ram_block1a0.PORTAADDR1
address_a[1] => ram_block1a1.PORTAADDR1
address_a[1] => ram_block1a2.PORTAADDR1
address_a[1] => ram_block1a3.PORTAADDR1
address_a[1] => ram_block1a4.PORTAADDR1
address_a[1] => ram_block1a5.PORTAADDR1
address_a[1] => ram_block1a6.PORTAADDR1
address_a[1] => ram_block1a7.PORTAADDR1
address_a[1] => ram_block1a8.PORTAADDR1
address_a[1] => ram_block1a9.PORTAADDR1
address_a[1] => ram_block1a10.PORTAADDR1
address_a[1] => ram_block1a11.PORTAADDR1
address_a[1] => ram_block1a12.PORTAADDR1
address_a[1] => ram_block1a13.PORTAADDR1
address_a[2] => ram_block1a0.PORTAADDR2
address_a[2] => ram_block1a1.PORTAADDR2
address_a[2] => ram_block1a2.PORTAADDR2
address_a[2] => ram_block1a3.PORTAADDR2
address_a[2] => ram_block1a4.PORTAADDR2
address_a[2] => ram_block1a5.PORTAADDR2
address_a[2] => ram_block1a6.PORTAADDR2
address_a[2] => ram_block1a7.PORTAADDR2
address_a[2] => ram_block1a8.PORTAADDR2
address_a[2] => ram_block1a9.PORTAADDR2
address_a[2] => ram_block1a10.PORTAADDR2
address_a[2] => ram_block1a11.PORTAADDR2
address_a[2] => ram_block1a12.PORTAADDR2
address_a[2] => ram_block1a13.PORTAADDR2
address_a[3] => ram_block1a0.PORTAADDR3
address_a[3] => ram_block1a1.PORTAADDR3
address_a[3] => ram_block1a2.PORTAADDR3
address_a[3] => ram_block1a3.PORTAADDR3
address_a[3] => ram_block1a4.PORTAADDR3
address_a[3] => ram_block1a5.PORTAADDR3
address_a[3] => ram_block1a6.PORTAADDR3
address_a[3] => ram_block1a7.PORTAADDR3
address_a[3] => ram_block1a8.PORTAADDR3
address_a[3] => ram_block1a9.PORTAADDR3
address_a[3] => ram_block1a10.PORTAADDR3
address_a[3] => ram_block1a11.PORTAADDR3
address_a[3] => ram_block1a12.PORTAADDR3
address_a[3] => ram_block1a13.PORTAADDR3
address_a[4] => ram_block1a0.PORTAADDR4
address_a[4] => ram_block1a1.PORTAADDR4
address_a[4] => ram_block1a2.PORTAADDR4
address_a[4] => ram_block1a3.PORTAADDR4
address_a[4] => ram_block1a4.PORTAADDR4
address_a[4] => ram_block1a5.PORTAADDR4
address_a[4] => ram_block1a6.PORTAADDR4
address_a[4] => ram_block1a7.PORTAADDR4
address_a[4] => ram_block1a8.PORTAADDR4
address_a[4] => ram_block1a9.PORTAADDR4
address_a[4] => ram_block1a10.PORTAADDR4
address_a[4] => ram_block1a11.PORTAADDR4
address_a[4] => ram_block1a12.PORTAADDR4
address_a[4] => ram_block1a13.PORTAADDR4
address_a[5] => ram_block1a0.PORTAADDR5
address_a[5] => ram_block1a1.PORTAADDR5
address_a[5] => ram_block1a2.PORTAADDR5
address_a[5] => ram_block1a3.PORTAADDR5
address_a[5] => ram_block1a4.PORTAADDR5
address_a[5] => ram_block1a5.PORTAADDR5
address_a[5] => ram_block1a6.PORTAADDR5
address_a[5] => ram_block1a7.PORTAADDR5
address_a[5] => ram_block1a8.PORTAADDR5
address_a[5] => ram_block1a9.PORTAADDR5
address_a[5] => ram_block1a10.PORTAADDR5
address_a[5] => ram_block1a11.PORTAADDR5
address_a[5] => ram_block1a12.PORTAADDR5
address_a[5] => ram_block1a13.PORTAADDR5
address_a[6] => ram_block1a0.PORTAADDR6
address_a[6] => ram_block1a1.PORTAADDR6
address_a[6] => ram_block1a2.PORTAADDR6
address_a[6] => ram_block1a3.PORTAADDR6
address_a[6] => ram_block1a4.PORTAADDR6
address_a[6] => ram_block1a5.PORTAADDR6
address_a[6] => ram_block1a6.PORTAADDR6
address_a[6] => ram_block1a7.PORTAADDR6
address_a[6] => ram_block1a8.PORTAADDR6
address_a[6] => ram_block1a9.PORTAADDR6
address_a[6] => ram_block1a10.PORTAADDR6
address_a[6] => ram_block1a11.PORTAADDR6
address_a[6] => ram_block1a12.PORTAADDR6
address_a[6] => ram_block1a13.PORTAADDR6
address_a[7] => ram_block1a0.PORTAADDR7
address_a[7] => ram_block1a1.PORTAADDR7
address_a[7] => ram_block1a2.PORTAADDR7
address_a[7] => ram_block1a3.PORTAADDR7
address_a[7] => ram_block1a4.PORTAADDR7
address_a[7] => ram_block1a5.PORTAADDR7
address_a[7] => ram_block1a6.PORTAADDR7
address_a[7] => ram_block1a7.PORTAADDR7
address_a[7] => ram_block1a8.PORTAADDR7
address_a[7] => ram_block1a9.PORTAADDR7
address_a[7] => ram_block1a10.PORTAADDR7
address_a[7] => ram_block1a11.PORTAADDR7
address_a[7] => ram_block1a12.PORTAADDR7
address_a[7] => ram_block1a13.PORTAADDR7
address_a[8] => ram_block1a0.PORTAADDR8
address_a[8] => ram_block1a1.PORTAADDR8
address_a[8] => ram_block1a2.PORTAADDR8
address_a[8] => ram_block1a3.PORTAADDR8
address_a[8] => ram_block1a4.PORTAADDR8
address_a[8] => ram_block1a5.PORTAADDR8
address_a[8] => ram_block1a6.PORTAADDR8
address_a[8] => ram_block1a7.PORTAADDR8
address_a[8] => ram_block1a8.PORTAADDR8
address_a[8] => ram_block1a9.PORTAADDR8
address_a[8] => ram_block1a10.PORTAADDR8
address_a[8] => ram_block1a11.PORTAADDR8
address_a[8] => ram_block1a12.PORTAADDR8
address_a[8] => ram_block1a13.PORTAADDR8
address_a[9] => ram_block1a0.PORTAADDR9
address_a[9] => ram_block1a1.PORTAADDR9
address_a[9] => ram_block1a2.PORTAADDR9
address_a[9] => ram_block1a3.PORTAADDR9
address_a[9] => ram_block1a4.PORTAADDR9
address_a[9] => ram_block1a5.PORTAADDR9
address_a[9] => ram_block1a6.PORTAADDR9
address_a[9] => ram_block1a7.PORTAADDR9
address_a[9] => ram_block1a8.PORTAADDR9
address_a[9] => ram_block1a9.PORTAADDR9
address_a[9] => ram_block1a10.PORTAADDR9
address_a[9] => ram_block1a11.PORTAADDR9
address_a[9] => ram_block1a12.PORTAADDR9
address_a[9] => ram_block1a13.PORTAADDR9
clock0 => ram_block1a0.CLK0
clock0 => ram_block1a1.CLK0
clock0 => ram_block1a2.CLK0
clock0 => ram_block1a3.CLK0
clock0 => ram_block1a4.CLK0
clock0 => ram_block1a5.CLK0
clock0 => ram_block1a6.CLK0
clock0 => ram_block1a7.CLK0
clock0 => ram_block1a8.CLK0
clock0 => ram_block1a9.CLK0
clock0 => ram_block1a10.CLK0
clock0 => ram_block1a11.CLK0
clock0 => ram_block1a12.CLK0
clock0 => ram_block1a13.CLK0
q_a[0] <= ram_block1a0.PORTADATAOUT
q_a[1] <= ram_block1a1.PORTADATAOUT
q_a[2] <= ram_block1a2.PORTADATAOUT
q_a[3] <= ram_block1a3.PORTADATAOUT
q_a[4] <= ram_block1a4.PORTADATAOUT
q_a[5] <= ram_block1a5.PORTADATAOUT
q_a[6] <= ram_block1a6.PORTADATAOUT
q_a[7] <= ram_block1a7.PORTADATAOUT
q_a[8] <= ram_block1a8.PORTADATAOUT
q_a[9] <= ram_block1a9.PORTADATAOUT
q_a[10] <= ram_block1a10.PORTADATAOUT
q_a[11] <= ram_block1a11.PORTADATAOUT
q_a[12] <= ram_block1a12.PORTADATAOUT
q_a[13] <= ram_block1a13.PORTADATAOUT


|TOP|DA_APMPLITUDE:inst10
CS => always0.IN0
WR_EN => always0.IN1
DATA_ina[0] => DA1_OUTA[0]$latch.DATAIN
DATA_ina[1] => DA1_OUTA[1]$latch.DATAIN
DATA_ina[2] => DA1_OUTA[2]$latch.DATAIN
DATA_ina[3] => DA1_OUTA[3]$latch.DATAIN
DATA_ina[4] => DA1_OUTA[4]$latch.DATAIN
DATA_ina[5] => DA1_OUTA[5]$latch.DATAIN
DATA_ina[6] => DA1_OUTA[6]$latch.DATAIN
DATA_ina[7] => DA1_OUTA[7]$latch.DATAIN
DATA_ina[8] => DA1_OUTA[8]$latch.DATAIN
DATA_ina[9] => DA1_OUTA[9]$latch.DATAIN
DATA_ina[10] => DA1_OUTA[10]$latch.DATAIN
DATA_ina[11] => DA1_OUTA[11]$latch.DATAIN
DATA_ina[12] => ~NO_FANOUT~
DATA_ina[13] => ~NO_FANOUT~
DATA_ina[14] => ~NO_FANOUT~
DATA_ina[15] => ~NO_FANOUT~
DATA_inb[0] => DA2_OUTB[0]$latch.DATAIN
DATA_inb[1] => DA2_OUTB[1]$latch.DATAIN
DATA_inb[2] => DA2_OUTB[2]$latch.DATAIN
DATA_inb[3] => DA2_OUTB[3]$latch.DATAIN
DATA_inb[4] => DA2_OUTB[4]$latch.DATAIN
DATA_inb[5] => DA2_OUTB[5]$latch.DATAIN
DATA_inb[6] => DA2_OUTB[6]$latch.DATAIN
DATA_inb[7] => DA2_OUTB[7]$latch.DATAIN
DATA_inb[8] => DA2_OUTB[8]$latch.DATAIN
DATA_inb[9] => DA2_OUTB[9]$latch.DATAIN
DATA_inb[10] => DA2_OUTB[10]$latch.DATAIN
DATA_inb[11] => DA2_OUTB[11]$latch.DATAIN
DATA_inb[12] => ~NO_FANOUT~
DATA_inb[13] => ~NO_FANOUT~
DATA_inb[14] => ~NO_FANOUT~
DATA_inb[15] => ~NO_FANOUT~
ADDR[0] => Equal0.IN31
ADDR[0] => Equal1.IN31
ADDR[1] => Equal0.IN30
ADDR[1] => Equal1.IN30
ADDR[2] => Equal0.IN29
ADDR[2] => Equal1.IN29
ADDR[3] => Equal0.IN28
ADDR[3] => Equal1.IN28
ADDR[4] => Equal0.IN27
ADDR[4] => Equal1.IN27
ADDR[5] => Equal0.IN26
ADDR[5] => Equal1.IN26
ADDR[6] => Equal0.IN25
ADDR[6] => Equal1.IN25
ADDR[7] => Equal0.IN24
ADDR[7] => Equal1.IN24
ADDR[8] => Equal0.IN23
ADDR[8] => Equal1.IN23
ADDR[9] => Equal0.IN22
ADDR[9] => Equal1.IN22
ADDR[10] => Equal0.IN21
ADDR[10] => Equal1.IN21
ADDR[11] => Equal0.IN20
ADDR[11] => Equal1.IN20
ADDR[12] => Equal0.IN19
ADDR[12] => Equal1.IN19
ADDR[13] => Equal0.IN18
ADDR[13] => Equal1.IN18
ADDR[14] => Equal0.IN17
ADDR[14] => Equal1.IN17
ADDR[15] => Equal0.IN16
ADDR[15] => Equal1.IN16
DA1_OUTA[0] <= DA1_OUTA[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTA[1] <= DA1_OUTA[1]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTA[2] <= DA1_OUTA[2]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTA[3] <= DA1_OUTA[3]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTA[4] <= DA1_OUTA[4]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTA[5] <= DA1_OUTA[5]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTA[6] <= DA1_OUTA[6]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTA[7] <= DA1_OUTA[7]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTA[8] <= DA1_OUTA[8]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTA[9] <= DA1_OUTA[9]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTA[10] <= DA1_OUTA[10]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA1_OUTA[11] <= DA1_OUTA[11]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTB[0] <= DA2_OUTB[0]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTB[1] <= DA2_OUTB[1]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTB[2] <= DA2_OUTB[2]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTB[3] <= DA2_OUTB[3]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTB[4] <= DA2_OUTB[4]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTB[5] <= DA2_OUTB[5]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTB[6] <= DA2_OUTB[6]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTB[7] <= DA2_OUTB[7]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTB[8] <= DA2_OUTB[8]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTB[9] <= DA2_OUTB[9]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTB[10] <= DA2_OUTB[10]$latch.DB_MAX_OUTPUT_PORT_TYPE
DA2_OUTB[11] <= DA2_OUTB[11]$latch.DB_MAX_OUTPUT_PORT_TYPE


|TOP|voltage_scaler_clocked:inst11
clk => scaled_data[0]~reg0.CLK
clk => scaled_data[1]~reg0.CLK
clk => scaled_data[2]~reg0.CLK
clk => scaled_data[3]~reg0.CLK
clk => scaled_data[4]~reg0.CLK
clk => scaled_data[5]~reg0.CLK
clk => scaled_data[6]~reg0.CLK
clk => scaled_data[7]~reg0.CLK
clk => scaled_data[8]~reg0.CLK
clk => scaled_data[9]~reg0.CLK
clk => scaled_data[10]~reg0.CLK
clk => scaled_data[11]~reg0.CLK
clk => scaled_data[12]~reg0.CLK
clk => scaled_data[13]~reg0.CLK
rom_data[0] => LessThan0.IN28
rom_data[0] => Add0.IN28
rom_data[0] => Mult1.IN12
rom_data[1] => LessThan0.IN27
rom_data[1] => Add0.IN27
rom_data[1] => Mult1.IN11
rom_data[2] => LessThan0.IN26
rom_data[2] => Add0.IN26
rom_data[2] => Mult1.IN10
rom_data[3] => LessThan0.IN25
rom_data[3] => Add0.IN25
rom_data[3] => Mult1.IN9
rom_data[4] => LessThan0.IN24
rom_data[4] => Add0.IN24
rom_data[4] => Mult1.IN8
rom_data[5] => LessThan0.IN23
rom_data[5] => Add0.IN23
rom_data[5] => Mult1.IN7
rom_data[6] => LessThan0.IN22
rom_data[6] => Add0.IN22
rom_data[6] => Mult1.IN6
rom_data[7] => LessThan0.IN21
rom_data[7] => Add0.IN21
rom_data[7] => Mult1.IN5
rom_data[8] => LessThan0.IN20
rom_data[8] => Add0.IN20
rom_data[8] => Mult1.IN4
rom_data[9] => LessThan0.IN19
rom_data[9] => Add0.IN19
rom_data[9] => Mult1.IN3
rom_data[10] => LessThan0.IN18
rom_data[10] => Add0.IN18
rom_data[10] => Mult1.IN2
rom_data[11] => LessThan0.IN17
rom_data[11] => Add0.IN17
rom_data[11] => Mult1.IN1
rom_data[12] => LessThan0.IN16
rom_data[12] => Add0.IN16
rom_data[12] => Mult1.IN0
rom_data[13] => LessThan0.IN15
rom_data[13] => Add0.IN15
rom_data[13] => Add2.IN1
voltage_mv[0] => Mult0.IN43
voltage_mv[0] => Mult1.IN43
voltage_mv[1] => Mult0.IN42
voltage_mv[1] => Mult1.IN42
voltage_mv[2] => Mult0.IN41
voltage_mv[2] => Mult1.IN41
voltage_mv[3] => Mult0.IN40
voltage_mv[3] => Mult1.IN40
voltage_mv[4] => Mult0.IN39
voltage_mv[4] => Mult1.IN39
voltage_mv[5] => Mult0.IN38
voltage_mv[5] => Mult1.IN38
voltage_mv[6] => Mult0.IN37
voltage_mv[6] => Mult1.IN37
voltage_mv[7] => Mult0.IN36
voltage_mv[7] => Mult1.IN36
voltage_mv[8] => Mult0.IN35
voltage_mv[8] => Mult1.IN35
voltage_mv[9] => Mult0.IN34
voltage_mv[9] => Mult1.IN34
voltage_mv[10] => Mult0.IN33
voltage_mv[10] => Mult1.IN33
voltage_mv[11] => Mult0.IN32
voltage_mv[11] => Mult1.IN32
scaled_data[0] <= scaled_data[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[1] <= scaled_data[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[2] <= scaled_data[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[3] <= scaled_data[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[4] <= scaled_data[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[5] <= scaled_data[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[6] <= scaled_data[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[7] <= scaled_data[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[8] <= scaled_data[8]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[9] <= scaled_data[9]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[10] <= scaled_data[10]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[11] <= scaled_data[11]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[12] <= scaled_data[12]~reg0.DB_MAX_OUTPUT_PORT_TYPE
scaled_data[13] <= scaled_data[13]~reg0.DB_MAX_OUTPUT_PORT_TYPE


|TOP|DA_WAVEFORM_B:inst18
CLK => CLK.IN4
WAVEFORM[0] => ~NO_FANOUT~
WAVEFORM[1] => ~NO_FANOUT~
WAVEFORM[2] => ~NO_FANOUT~
WAVEFORM[3] => ~NO_FANOUT~
WAVEFORM[4] => ~NO_FANOUT~
WAVEFORM[5] => ~NO_FANOUT~
WAVEFORM[6] => ~NO_FANOUT~
WAVEFORM[7] => ~NO_FANOUT~
WAVEFORM[8] => WAVEFORM_B[0].DATAIN
WAVEFORM[9] => WAVEFORM_B[1].DATAIN
WAVEFORM[10] => WAVEFORM_B[2].DATAIN
WAVEFORM[11] => WAVEFORM_B[3].DATAIN
WAVEFORM[12] => WAVEFORM_B[4].DATAIN
WAVEFORM[13] => WAVEFORM_B[5].DATAIN
WAVEFORM[14] => WAVEFORM_B[6].DATAIN
WAVEFORM[15] => WAVEFORM_B[7].DATAIN
addr_b[0] => addr_b[0].IN4
addr_b[1] => addr_b[1].IN4
addr_b[2] => addr_b[2].IN4
addr_b[3] => addr_b[3].IN4
addr_b[4] => addr_b[4].IN4
addr_b[5] => addr_b[5].IN4
addr_b[6] => addr_b[6].IN4
addr_b[7] => addr_b[7].IN4
addr_b[8] => addr_b[8].IN4
addr_b[9] => addr_b[9].IN4
CS => always0.IN0
WR_EN => always0.IN1
ADDR[0] => Equal0.IN31
ADDR[1] => Equal0.IN30
ADDR[2] => Equal0.IN29
ADDR[3] => Equal0.IN28
ADDR[4] => Equal0.IN27
ADDR[5] => Equal0.IN26
ADDR[6] => Equal0.IN25
ADDR[7] => Equal0.IN24
ADDR[8] => Equal0.IN23
ADDR[9] => Equal0.IN22
ADDR[10] => Equal0.IN21
ADDR[11] => Equal0.IN20
ADDR[12] => Equal0.IN19
ADDR[13] => Equal0.IN18
ADDR[14] => Equal0.IN17
ADDR[15] => Equal0.IN16
POW_B[0] <= WAVE_DATA_B_reg[0].DB_MAX_OUTPUT_PORT_TYPE
POW_B[1] <= WAVE_DATA_B_reg[1].DB_MAX_OUTPUT_PORT_TYPE
POW_B[2] <= WAVE_DATA_B_reg[2].DB_MAX_OUTPUT_PORT_TYPE
POW_B[3] <= WAVE_DATA_B_reg[3].DB_MAX_OUTPUT_PORT_TYPE
POW_B[4] <= WAVE_DATA_B_reg[4].DB_MAX_OUTPUT_PORT_TYPE
POW_B[5] <= WAVE_DATA_B_reg[5].DB_MAX_OUTPUT_PORT_TYPE
POW_B[6] <= WAVE_DATA_B_reg[6].DB_MAX_OUTPUT_PORT_TYPE
POW_B[7] <= WAVE_DATA_B_reg[7].DB_MAX_OUTPUT_PORT_TYPE
POW_B[8] <= WAVE_DATA_B_reg[8].DB_MAX_OUTPUT_PORT_TYPE
POW_B[9] <= WAVE_DATA_B_reg[9].DB_MAX_OUTPUT_PORT_TYPE
POW_B[10] <= WAVE_DATA_B_reg[10].DB_MAX_OUTPUT_PORT_TYPE
POW_B[11] <= WAVE_DATA_B_reg[11].DB_MAX_OUTPUT_PORT_TYPE
POW_B[12] <= WAVE_DATA_B_reg[12].DB_MAX_OUTPUT_PORT_TYPE
POW_B[13] <= WAVE_DATA_B_reg[13].DB_MAX_OUTPUT_PORT_TYPE


|TOP|DA_WAVEFORM_B:inst18|sinromvpp:sinromvpp_inst
address[0] => address[0].IN1
address[1] => address[1].IN1
address[2] => address[2].IN1
address[3] => address[3].IN1
address[4] => address[4].IN1
address[5] => address[5].IN1
address[6] => address[6].IN1
address[7] => address[7].IN1
address[8] => address[8].IN1
address[9] => address[9].IN1
clock => clock.IN1
q[0] <= altsyncram:altsyncram_component.q_a
q[1] <= altsyncram:altsyncram_component.q_a
q[2] <= altsyncram:altsyncram_component.q_a
q[3] <= altsyncram:altsyncram_component.q_a
q[4] <= altsyncram:altsyncram_component.q_a
q[5] <= altsyncram:altsyncram_component.q_a
q[6] <= altsyncram:altsyncram_component.q_a
q[7] <= altsyncram:altsyncram_component.q_a
q[8] <= altsyncram:altsyncram_component.q_a
q[9] <= altsyncram:altsyncram_component.q_a
q[10] <= altsyncram:altsyncram_component.q_a
q[11] <= altsyncram:altsyncram_component.q_a
q[12] <= altsyncram:altsyncram_component.q_a
q[13] <= altsyncram:altsyncram_component.q_a


|TOP|DA_WAVEFORM_B:inst18|sinromvpp:sinromvpp_inst|altsyncram:altsyncram_component
wren_a => ~NO_FANOUT~
rden_a => ~NO_FANOUT~
wren_b => ~NO_FANOUT~
rden_b => ~NO_FANOUT~
data_a[0] => ~NO_FANOUT~
data_a[1] => ~NO_FANOUT~
data_a[2] => ~NO_FANOUT~
data_a[3] => ~NO_FANOUT~
data_a[4] => ~NO_FANOUT~
data_a[5] => ~NO_FANOUT~
data_a[6] => ~NO_FANOUT~
data_a[7] => ~NO_FANOUT~
data_a[8] => ~NO_FANOUT~
data_a[9] => ~NO_FANOUT~
data_a[10] => ~NO_FANOUT~
data_a[11] => ~NO_FANOUT~
data_a[12] => ~NO_FANOUT~
data_a[13] => ~NO_FANOUT~
data_b[0] => ~NO_FANOUT~
address_a[0] => altsyncram_h3b1:auto_generated.address_a[0]
address_a[1] => altsyncram_h3b1:auto_generated.address_a[1]
address_a[2] => altsyncram_h3b1:auto_generated.address_a[2]
address_a[3] => altsyncram_h3b1:auto_generated.address_a[3]
address_a[4] => altsyncram_h3b1:auto_generated.address_a[4]
address_a[5] => altsyncram_h3b1:auto_generated.address_a[5]
address_a[6] => altsyncram_h3b1:auto_generated.address_a[6]
address_a[7] => altsyncram_h3b1:auto_generated.address_a[7]
address_a[8] => altsyncram_h3b1:auto_generated.address_a[8]
address_a[9] => altsyncram_h3b1:auto_generated.address_a[9]
address_b[0] => ~NO_FANOUT~
addressstall_a => ~NO_FANOUT~
addressstall_b => ~NO_FANOUT~
clock0 => altsyncram_h3b1:auto_generated.clock0
clock1 => ~NO_FANOUT~
clocken0 => ~NO_FANOUT~
clocken1 => ~NO_FANOUT~
clocken2 => ~NO_FANOUT~
clocken3 => ~NO_FANOUT~
aclr0 => ~NO_FANOUT~
aclr1 => ~NO_FANOUT~
byteena_a[0] => ~NO_FANOUT~
byteena_b[0] => ~NO_FANOUT~
q_a[0] <= altsyncram_h3b1:auto_generated.q_a[0]
q_a[1] <= altsyncram_h3b1:auto_generated.q_a[1]
q_a[2] <= altsyncram_h3b1:auto_generated.q_a[2]
q_a[3] <= altsyncram_h3b1:auto_generated.q_a[3]
q_a[4] <= altsyncram_h3b1:auto_generated.q_a[4]
q_a[5] <= altsyncram_h3b1:auto_generated.q_a[5]
q_a[6] <= altsyncram_h3b1:auto_generated.q_a[6]
q_a[7] <= altsyncram_h3b1:auto_generated.q_a[7]
q_a[8] <= altsyncram_h3b1:auto_generated.q_a[8]
q_a[9] <= altsyncram_h3b1:auto_generated.q_a[9]
q_a[10] <= altsyncram_h3b1:auto_generated.q_a[10]
q_a[11] <= altsyncram_h3b1:auto_generated.q_a[11]
q_a[12] <= altsyncram_h3b1:auto_generated.q_a[12]
q_a[13] <= altsyncram_h3b1:auto_generated.q_a[13]
q_b[0] <= <GND>
eccstatus[0] <= <GND>
eccstatus[1] <= <GND>
eccstatus[2] <= <GND>


|TOP|DA_WAVEFORM_B:inst18|sinromvpp:sinromvpp_inst|altsyncram:altsyncram_component|altsyncram_h3b1:auto_generated
address_a[0] => ram_block1a0.PORTAADDR
address_a[0] => ram_block1a1.PORTAADDR
address_a[0] => ram_block1a2.PORTAADDR
address_a[0] => ram_block1a3.PORTAADDR
address_a[0] => ram_block1a4.PORTAADDR
address_a[0] => ram_block1a5.PORTAADDR
address_a[0] => ram_block1a6.PORTAADDR
address_a[0] => ram_block1a7.PORTAADDR
address_a[0] => ram_block1a8.PORTAADDR
address_a[0] => ram_block1a9.PORTAADDR
address_a[0] => ram_block1a10.PORTAADDR
address_a[0] => ram_block1a11.PORTAADDR
address_a[0] => ram_block1a12.PORTAADDR
address_a[0] => ram_block1a13.PORTAADDR
address_a[1] => ram_block1a0.PORTAADDR1
address_a[1] => ram_block1a1.PORTAADDR1
address_a[1] => ram_block1a2.PORTAADDR1
address_a[1] => ram_block1a3.PORTAADDR1
address_a[1] => ram_block1a4.PORTAADDR1
address_a[1] => ram_block1a5.PORTAADDR1
address_a[1] => ram_block1a6.PORTAADDR1
address_a[1] => ram_block1a7.PORTAADDR1
address_a[1] => ram_block1a8.PORTAADDR1
address_a[1] => ram_block1a9.PORTAADDR1
address_a[1] => ram_block1a10.PORTAADDR1
address_a[1] => ram_block1a11.PORTAADDR1
address_a[1] => ram_block1a12.PORTAADDR1
address_a[1] => ram_block1a13.PORTAADDR1
address_a[2] => ram_block1a0.PORTAADDR2
address_a[2] => ram_block1a1.PORTAADDR2
address_a[2] => ram_block1a2.PORTAADDR2
address_a[2] => ram_block1a3.PORTAADDR2
address_a[2] => ram_block1a4.PORTAADDR2
address_a[2] => ram_block1a5.PORTAADDR2
address_a[2] => ram_block1a6.PORTAADDR2
address_a[2] => ram_block1a7.PORTAADDR2
address_a[2] => ram_block1a8.PORTAADDR2
address_a[2] => ram_block1a9.PORTAADDR2
address_a[2] => ram_block1a10.PORTAADDR2
address_a[2] => ram_block1a11.PORTAADDR2
address_a[2] => ram_block1a12.PORTAADDR2
address_a[2] => ram_block1a13.PORTAADDR2
address_a[3] => ram_block1a0.PORTAADDR3
address_a[3] => ram_block1a1.PORTAADDR3
address_a[3] => ram_block1a2.PORTAADDR3
address_a[3] => ram_block1a3.PORTAADDR3
address_a[3] => ram_block1a4.PORTAADDR3
address_a[3] => ram_block1a5.PORTAADDR3
address_a[3] => ram_block1a6.PORTAADDR3
address_a[3] => ram_block1a7.PORTAADDR3
address_a[3] => ram_block1a8.PORTAADDR3
address_a[3] => ram_block1a9.PORTAADDR3
address_a[3] => ram_block1a10.PORTAADDR3
address_a[3] => ram_block1a11.PORTAADDR3
address_a[3] => ram_block1a12.PORTAADDR3
address_a[3] => ram_block1a13.PORTAADDR3
address_a[4] => ram_block1a0.PORTAADDR4
address_a[4] => ram_block1a1.PORTAADDR4
address_a[4] => ram_block1a2.PORTAADDR4
address_a[4] => ram_block1a3.PORTAADDR4
address_a[4] => ram_block1a4.PORTAADDR4
address_a[4] => ram_block1a5.PORTAADDR4
address_a[4] => ram_block1a6.PORTAADDR4
address_a[4] => ram_block1a7.PORTAADDR4
address_a[4] => ram_block1a8.PORTAADDR4
address_a[4] => ram_block1a9.PORTAADDR4
address_a[4] => ram_block1a10.PORTAADDR4
address_a[4] => ram_block1a11.PORTAADDR4
address_a[4] => ram_block1a12.PORTAADDR4
address_a[4] => ram_block1a13.PORTAADDR4
address_a[5] => ram_block1a0.PORTAADDR5
address_a[5] => ram_block1a1.PORTAADDR5
address_a[5] => ram_block1a2.PORTAADDR5
address_a[5] => ram_block1a3.PORTAADDR5
address_a[5] => ram_block1a4.PORTAADDR5
address_a[5] => ram_block1a5.PORTAADDR5
address_a[5] => ram_block1a6.PORTAADDR5
address_a[5] => ram_block1a7.PORTAADDR5
address_a[5] => ram_block1a8.PORTAADDR5
address_a[5] => ram_block1a9.PORTAADDR5
address_a[5] => ram_block1a10.PORTAADDR5
address_a[5] => ram_block1a11.PORTAADDR5
address_a[5] => ram_block1a12.PORTAADDR5
address_a[5] => ram_block1a13.PORTAADDR5
address_a[6] => ram_block1a0.PORTAADDR6
address_a[6] => ram_block1a1.PORTAADDR6
address_a[6] => ram_block1a2.PORTAADDR6
address_a[6] => ram_block1a3.PORTAADDR6
address_a[6] => ram_block1a4.PORTAADDR6
address_a[6] => ram_block1a5.PORTAADDR6
address_a[6] => ram_block1a6.PORTAADDR6
address_a[6] => ram_block1a7.PORTAADDR6
address_a[6] => ram_block1a8.PORTAADDR6
address_a[6] => ram_block1a9.PORTAADDR6
address_a[6] => ram_block1a10.PORTAADDR6
address_a[6] => ram_block1a11.PORTAADDR6
address_a[6] => ram_block1a12.PORTAADDR6
address_a[6] => ram_block1a13.PORTAADDR6
address_a[7] => ram_block1a0.PORTAADDR7
address_a[7] => ram_block1a1.PORTAADDR7
address_a[7] => ram_block1a2.PORTAADDR7
address_a[7] => ram_block1a3.PORTAADDR7
address_a[7] => ram_block1a4.PORTAADDR7
address_a[7] => ram_block1a5.PORTAADDR7
address_a[7] => ram_block1a6.PORTAADDR7
address_a[7] => ram_block1a7.PORTAADDR7
address_a[7] => ram_block1a8.PORTAADDR7
address_a[7] => ram_block1a9.PORTAADDR7
address_a[7] => ram_block1a10.PORTAADDR7
address_a[7] => ram_block1a11.PORTAADDR7
address_a[7] => ram_block1a12.PORTAADDR7
address_a[7] => ram_block1a13.PORTAADDR7
address_a[8] => ram_block1a0.PORTAADDR8
address_a[8] => ram_block1a1.PORTAADDR8
address_a[8] => ram_block1a2.PORTAADDR8
address_a[8] => ram_block1a3.PORTAADDR8
address_a[8] => ram_block1a4.PORTAADDR8
address_a[8] => ram_block1a5.PORTAADDR8
address_a[8] => ram_block1a6.PORTAADDR8
address_a[8] => ram_block1a7.PORTAADDR8
address_a[8] => ram_block1a8.PORTAADDR8
address_a[8] => ram_block1a9.PORTAADDR8
address_a[8] => ram_block1a10.PORTAADDR8
address_a[8] => ram_block1a11.PORTAADDR8
address_a[8] => ram_block1a12.PORTAADDR8
address_a[8] => ram_block1a13.PORTAADDR8
address_a[9] => ram_block1a0.PORTAADDR9
address_a[9] => ram_block1a1.PORTAADDR9
address_a[9] => ram_block1a2.PORTAADDR9
address_a[9] => ram_block1a3.PORTAADDR9
address_a[9] => ram_block1a4.PORTAADDR9
address_a[9] => ram_block1a5.PORTAADDR9
address_a[9] => ram_block1a6.PORTAADDR9
address_a[9] => ram_block1a7.PORTAADDR9
address_a[9] => ram_block1a8.PORTAADDR9
address_a[9] => ram_block1a9.PORTAADDR9
address_a[9] => ram_block1a10.PORTAADDR9
address_a[9] => ram_block1a11.PORTAADDR9
address_a[9] => ram_block1a12.PORTAADDR9
address_a[9] => ram_block1a13.PORTAADDR9
clock0 => ram_block1a0.CLK0
clock0 => ram_block1a1.CLK0
clock0 => ram_block1a2.CLK0
clock0 => ram_block1a3.CLK0
clock0 => ram_block1a4.CLK0
clock0 => ram_block1a5.CLK0
clock0 => ram_block1a6.CLK0
clock0 => ram_block1a7.CLK0
clock0 => ram_block1a8.CLK0
clock0 => ram_block1a9.CLK0
clock0 => ram_block1a10.CLK0
clock0 => ram_block1a11.CLK0
clock0 => ram_block1a12.CLK0
clock0 => ram_block1a13.CLK0
q_a[0] <= ram_block1a0.PORTADATAOUT
q_a[1] <= ram_block1a1.PORTADATAOUT
q_a[2] <= ram_block1a2.PORTADATAOUT
q_a[3] <= ram_block1a3.PORTADATAOUT
q_a[4] <= ram_block1a4.PORTADATAOUT
q_a[5] <= ram_block1a5.PORTADATAOUT
q_a[6] <= ram_block1a6.PORTADATAOUT
q_a[7] <= ram_block1a7.PORTADATAOUT
q_a[8] <= ram_block1a8.PORTADATAOUT
q_a[9] <= ram_block1a9.PORTADATAOUT
q_a[10] <= ram_block1a10.PORTADATAOUT
q_a[11] <= ram_block1a11.PORTADATAOUT
q_a[12] <= ram_block1a12.PORTADATAOUT
q_a[13] <= ram_block1a13.PORTADATAOUT


|TOP|DA_WAVEFORM_B:inst18|sqaure_rom:sqaure_rom_inst
address[0] => address[0].IN1
address[1] => address[1].IN1
address[2] => address[2].IN1
address[3] => address[3].IN1
address[4] => address[4].IN1
address[5] => address[5].IN1
address[6] => address[6].IN1
address[7] => address[7].IN1
address[8] => address[8].IN1
address[9] => address[9].IN1
clock => clock.IN1
q[0] <= altsyncram:altsyncram_component.q_a
q[1] <= altsyncram:altsyncram_component.q_a
q[2] <= altsyncram:altsyncram_component.q_a
q[3] <= altsyncram:altsyncram_component.q_a
q[4] <= altsyncram:altsyncram_component.q_a
q[5] <= altsyncram:altsyncram_component.q_a
q[6] <= altsyncram:altsyncram_component.q_a
q[7] <= altsyncram:altsyncram_component.q_a
q[8] <= altsyncram:altsyncram_component.q_a
q[9] <= altsyncram:altsyncram_component.q_a
q[10] <= altsyncram:altsyncram_component.q_a
q[11] <= altsyncram:altsyncram_component.q_a
q[12] <= altsyncram:altsyncram_component.q_a
q[13] <= altsyncram:altsyncram_component.q_a


|TOP|DA_WAVEFORM_B:inst18|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component
wren_a => ~NO_FANOUT~
rden_a => ~NO_FANOUT~
wren_b => ~NO_FANOUT~
rden_b => ~NO_FANOUT~
data_a[0] => ~NO_FANOUT~
data_a[1] => ~NO_FANOUT~
data_a[2] => ~NO_FANOUT~
data_a[3] => ~NO_FANOUT~
data_a[4] => ~NO_FANOUT~
data_a[5] => ~NO_FANOUT~
data_a[6] => ~NO_FANOUT~
data_a[7] => ~NO_FANOUT~
data_a[8] => ~NO_FANOUT~
data_a[9] => ~NO_FANOUT~
data_a[10] => ~NO_FANOUT~
data_a[11] => ~NO_FANOUT~
data_a[12] => ~NO_FANOUT~
data_a[13] => ~NO_FANOUT~
data_b[0] => ~NO_FANOUT~
address_a[0] => altsyncram_odb1:auto_generated.address_a[0]
address_a[1] => altsyncram_odb1:auto_generated.address_a[1]
address_a[2] => altsyncram_odb1:auto_generated.address_a[2]
address_a[3] => altsyncram_odb1:auto_generated.address_a[3]
address_a[4] => altsyncram_odb1:auto_generated.address_a[4]
address_a[5] => altsyncram_odb1:auto_generated.address_a[5]
address_a[6] => altsyncram_odb1:auto_generated.address_a[6]
address_a[7] => altsyncram_odb1:auto_generated.address_a[7]
address_a[8] => altsyncram_odb1:auto_generated.address_a[8]
address_a[9] => altsyncram_odb1:auto_generated.address_a[9]
address_b[0] => ~NO_FANOUT~
addressstall_a => ~NO_FANOUT~
addressstall_b => ~NO_FANOUT~
clock0 => altsyncram_odb1:auto_generated.clock0
clock1 => ~NO_FANOUT~
clocken0 => ~NO_FANOUT~
clocken1 => ~NO_FANOUT~
clocken2 => ~NO_FANOUT~
clocken3 => ~NO_FANOUT~
aclr0 => ~NO_FANOUT~
aclr1 => ~NO_FANOUT~
byteena_a[0] => ~NO_FANOUT~
byteena_b[0] => ~NO_FANOUT~
q_a[0] <= altsyncram_odb1:auto_generated.q_a[0]
q_a[1] <= altsyncram_odb1:auto_generated.q_a[1]
q_a[2] <= altsyncram_odb1:auto_generated.q_a[2]
q_a[3] <= altsyncram_odb1:auto_generated.q_a[3]
q_a[4] <= altsyncram_odb1:auto_generated.q_a[4]
q_a[5] <= altsyncram_odb1:auto_generated.q_a[5]
q_a[6] <= altsyncram_odb1:auto_generated.q_a[6]
q_a[7] <= altsyncram_odb1:auto_generated.q_a[7]
q_a[8] <= altsyncram_odb1:auto_generated.q_a[8]
q_a[9] <= altsyncram_odb1:auto_generated.q_a[9]
q_a[10] <= altsyncram_odb1:auto_generated.q_a[10]
q_a[11] <= altsyncram_odb1:auto_generated.q_a[11]
q_a[12] <= altsyncram_odb1:auto_generated.q_a[12]
q_a[13] <= altsyncram_odb1:auto_generated.q_a[13]
q_b[0] <= <GND>
eccstatus[0] <= <GND>
eccstatus[1] <= <GND>
eccstatus[2] <= <GND>


|TOP|DA_WAVEFORM_B:inst18|sqaure_rom:sqaure_rom_inst|altsyncram:altsyncram_component|altsyncram_odb1:auto_generated
address_a[0] => ram_block1a0.PORTAADDR
address_a[0] => ram_block1a1.PORTAADDR
address_a[0] => ram_block1a2.PORTAADDR
address_a[0] => ram_block1a3.PORTAADDR
address_a[0] => ram_block1a4.PORTAADDR
address_a[0] => ram_block1a5.PORTAADDR
address_a[0] => ram_block1a6.PORTAADDR
address_a[0] => ram_block1a7.PORTAADDR
address_a[0] => ram_block1a8.PORTAADDR
address_a[0] => ram_block1a9.PORTAADDR
address_a[0] => ram_block1a10.PORTAADDR
address_a[0] => ram_block1a11.PORTAADDR
address_a[0] => ram_block1a12.PORTAADDR
address_a[0] => ram_block1a13.PORTAADDR
address_a[1] => ram_block1a0.PORTAADDR1
address_a[1] => ram_block1a1.PORTAADDR1
address_a[1] => ram_block1a2.PORTAADDR1
address_a[1] => ram_block1a3.PORTAADDR1
address_a[1] => ram_block1a4.PORTAADDR1
address_a[1] => ram_block1a5.PORTAADDR1
address_a[1] => ram_block1a6.PORTAADDR1
address_a[1] => ram_block1a7.PORTAADDR1
address_a[1] => ram_block1a8.PORTAADDR1
address_a[1] => ram_block1a9.PORTAADDR1
address_a[1] => ram_block1a10.PORTAADDR1
address_a[1] => ram_block1a11.PORTAADDR1
address_a[1] => ram_block1a12.PORTAADDR1
address_a[1] => ram_block1a13.PORTAADDR1
address_a[2] => ram_block1a0.PORTAADDR2
address_a[2] => ram_block1a1.PORTAADDR2
address_a[2] => ram_block1a2.PORTAADDR2
address_a[2] => ram_block1a3.PORTAADDR2
address_a[2] => ram_block1a4.PORTAADDR2
address_a[2] => ram_block1a5.PORTAADDR2
address_a[2] => ram_block1a6.PORTAADDR2
address_a[2] => ram_block1a7.PORTAADDR2
address_a[2] => ram_block1a8.PORTAADDR2
address_a[2] => ram_block1a9.PORTAADDR2
address_a[2] => ram_block1a10.PORTAADDR2
address_a[2] => ram_block1a11.PORTAADDR2
address_a[2] => ram_block1a12.PORTAADDR2
address_a[2] => ram_block1a13.PORTAADDR2
address_a[3] => ram_block1a0.PORTAADDR3
address_a[3] => ram_block1a1.PORTAADDR3
address_a[3] => ram_block1a2.PORTAADDR3
address_a[3] => ram_block1a3.PORTAADDR3
address_a[3] => ram_block1a4.PORTAADDR3
address_a[3] => ram_block1a5.PORTAADDR3
address_a[3] => ram_block1a6.PORTAADDR3
address_a[3] => ram_block1a7.PORTAADDR3
address_a[3] => ram_block1a8.PORTAADDR3
address_a[3] => ram_block1a9.PORTAADDR3
address_a[3] => ram_block1a10.PORTAADDR3
address_a[3] => ram_block1a11.PORTAADDR3
address_a[3] => ram_block1a12.PORTAADDR3
address_a[3] => ram_block1a13.PORTAADDR3
address_a[4] => ram_block1a0.PORTAADDR4
address_a[4] => ram_block1a1.PORTAADDR4
address_a[4] => ram_block1a2.PORTAADDR4
address_a[4] => ram_block1a3.PORTAADDR4
address_a[4] => ram_block1a4.PORTAADDR4
address_a[4] => ram_block1a5.PORTAADDR4
address_a[4] => ram_block1a6.PORTAADDR4
address_a[4] => ram_block1a7.PORTAADDR4
address_a[4] => ram_block1a8.PORTAADDR4
address_a[4] => ram_block1a9.PORTAADDR4
address_a[4] => ram_block1a10.PORTAADDR4
address_a[4] => ram_block1a11.PORTAADDR4
address_a[4] => ram_block1a12.PORTAADDR4
address_a[4] => ram_block1a13.PORTAADDR4
address_a[5] => ram_block1a0.PORTAADDR5
address_a[5] => ram_block1a1.PORTAADDR5
address_a[5] => ram_block1a2.PORTAADDR5
address_a[5] => ram_block1a3.PORTAADDR5
address_a[5] => ram_block1a4.PORTAADDR5
address_a[5] => ram_block1a5.PORTAADDR5
address_a[5] => ram_block1a6.PORTAADDR5
address_a[5] => ram_block1a7.PORTAADDR5
address_a[5] => ram_block1a8.PORTAADDR5
address_a[5] => ram_block1a9.PORTAADDR5
address_a[5] => ram_block1a10.PORTAADDR5
address_a[5] => ram_block1a11.PORTAADDR5
address_a[5] => ram_block1a12.PORTAADDR5
address_a[5] => ram_block1a13.PORTAADDR5
address_a[6] => ram_block1a0.PORTAADDR6
address_a[6] => ram_block1a1.PORTAADDR6
address_a[6] => ram_block1a2.PORTAADDR6
address_a[6] => ram_block1a3.PORTAADDR6
address_a[6] => ram_block1a4.PORTAADDR6
address_a[6] => ram_block1a5.PORTAADDR6
address_a[6] => ram_block1a6.PORTAADDR6
address_a[6] => ram_block1a7.PORTAADDR6
address_a[6] => ram_block1a8.PORTAADDR6
address_a[6] => ram_block1a9.PORTAADDR6
address_a[6] => ram_block1a10.PORTAADDR6
address_a[6] => ram_block1a11.PORTAADDR6
address_a[6] => ram_block1a12.PORTAADDR6
address_a[6] => ram_block1a13.PORTAADDR6
address_a[7] => ram_block1a0.PORTAADDR7
address_a[7] => ram_block1a1.PORTAADDR7
address_a[7] => ram_block1a2.PORTAADDR7
address_a[7] => ram_block1a3.PORTAADDR7
address_a[7] => ram_block1a4.PORTAADDR7
address_a[7] => ram_block1a5.PORTAADDR7
address_a[7] => ram_block1a6.PORTAADDR7
address_a[7] => ram_block1a7.PORTAADDR7
address_a[7] => ram_block1a8.PORTAADDR7
address_a[7] => ram_block1a9.PORTAADDR7
address_a[7] => ram_block1a10.PORTAADDR7
address_a[7] => ram_block1a11.PORTAADDR7
address_a[7] => ram_block1a12.PORTAADDR7
address_a[7] => ram_block1a13.PORTAADDR7
address_a[8] => ram_block1a0.PORTAADDR8
address_a[8] => ram_block1a1.PORTAADDR8
address_a[8] => ram_block1a2.PORTAADDR8
address_a[8] => ram_block1a3.PORTAADDR8
address_a[8] => ram_block1a4.PORTAADDR8
address_a[8] => ram_block1a5.PORTAADDR8
address_a[8] => ram_block1a6.PORTAADDR8
address_a[8] => ram_block1a7.PORTAADDR8
address_a[8] => ram_block1a8.PORTAADDR8
address_a[8] => ram_block1a9.PORTAADDR8
address_a[8] => ram_block1a10.PORTAADDR8
address_a[8] => ram_block1a11.PORTAADDR8
address_a[8] => ram_block1a12.PORTAADDR8
address_a[8] => ram_block1a13.PORTAADDR8
address_a[9] => ram_block1a0.PORTAADDR9
address_a[9] => ram_block1a1.PORTAADDR9
address_a[9] => ram_block1a2.PORTAADDR9
address_a[9] => ram_block1a3.PORTAADDR9
address_a[9] => ram_block1a4.PORTAADDR9
address_a[9] => ram_block1a5.PORTAADDR9
address_a[9] => ram_block1a6.PORTAADDR9
address_a[9] => ram_block1a7.PORTAADDR9
address_a[9] => ram_block1a8.PORTAADDR9
address_a[9] => ram_block1a9.PORTAADDR9
address_a[9] => ram_block1a10.PORTAADDR9
address_a[9] => ram_block1a11.PORTAADDR9
address_a[9] => ram_block1a12.PORTAADDR9
address_a[9] => ram_block1a13.PORTAADDR9
clock0 => ram_block1a0.CLK0
clock0 => ram_block1a1.CLK0
clock0 => ram_block1a2.CLK0
clock0 => ram_block1a3.CLK0
clock0 => ram_block1a4.CLK0
clock0 => ram_block1a5.CLK0
clock0 => ram_block1a6.CLK0
clock0 => ram_block1a7.CLK0
clock0 => ram_block1a8.CLK0
clock0 => ram_block1a9.CLK0
clock0 => ram_block1a10.CLK0
clock0 => ram_block1a11.CLK0
clock0 => ram_block1a12.CLK0
clock0 => ram_block1a13.CLK0
q_a[0] <= ram_block1a0.PORTADATAOUT
q_a[1] <= ram_block1a1.PORTADATAOUT
q_a[2] <= ram_block1a2.PORTADATAOUT
q_a[3] <= ram_block1a3.PORTADATAOUT
q_a[4] <= ram_block1a4.PORTADATAOUT
q_a[5] <= ram_block1a5.PORTADATAOUT
q_a[6] <= ram_block1a6.PORTADATAOUT
q_a[7] <= ram_block1a7.PORTADATAOUT
q_a[8] <= ram_block1a8.PORTADATAOUT
q_a[9] <= ram_block1a9.PORTADATAOUT
q_a[10] <= ram_block1a10.PORTADATAOUT
q_a[11] <= ram_block1a11.PORTADATAOUT
q_a[12] <= ram_block1a12.PORTADATAOUT
q_a[13] <= ram_block1a13.PORTADATAOUT


|TOP|DA_WAVEFORM_B:inst18|triangle_rom:triangle_rom_inst
address[0] => address[0].IN1
address[1] => address[1].IN1
address[2] => address[2].IN1
address[3] => address[3].IN1
address[4] => address[4].IN1
address[5] => address[5].IN1
address[6] => address[6].IN1
address[7] => address[7].IN1
address[8] => address[8].IN1
address[9] => address[9].IN1
clock => clock.IN1
q[0] <= altsyncram:altsyncram_component.q_a
q[1] <= altsyncram:altsyncram_component.q_a
q[2] <= altsyncram:altsyncram_component.q_a
q[3] <= altsyncram:altsyncram_component.q_a
q[4] <= altsyncram:altsyncram_component.q_a
q[5] <= altsyncram:altsyncram_component.q_a
q[6] <= altsyncram:altsyncram_component.q_a
q[7] <= altsyncram:altsyncram_component.q_a
q[8] <= altsyncram:altsyncram_component.q_a
q[9] <= altsyncram:altsyncram_component.q_a
q[10] <= altsyncram:altsyncram_component.q_a
q[11] <= altsyncram:altsyncram_component.q_a
q[12] <= altsyncram:altsyncram_component.q_a
q[13] <= altsyncram:altsyncram_component.q_a


|TOP|DA_WAVEFORM_B:inst18|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component
wren_a => ~NO_FANOUT~
rden_a => ~NO_FANOUT~
wren_b => ~NO_FANOUT~
rden_b => ~NO_FANOUT~
data_a[0] => ~NO_FANOUT~
data_a[1] => ~NO_FANOUT~
data_a[2] => ~NO_FANOUT~
data_a[3] => ~NO_FANOUT~
data_a[4] => ~NO_FANOUT~
data_a[5] => ~NO_FANOUT~
data_a[6] => ~NO_FANOUT~
data_a[7] => ~NO_FANOUT~
data_a[8] => ~NO_FANOUT~
data_a[9] => ~NO_FANOUT~
data_a[10] => ~NO_FANOUT~
data_a[11] => ~NO_FANOUT~
data_a[12] => ~NO_FANOUT~
data_a[13] => ~NO_FANOUT~
data_b[0] => ~NO_FANOUT~
address_a[0] => altsyncram_tjb1:auto_generated.address_a[0]
address_a[1] => altsyncram_tjb1:auto_generated.address_a[1]
address_a[2] => altsyncram_tjb1:auto_generated.address_a[2]
address_a[3] => altsyncram_tjb1:auto_generated.address_a[3]
address_a[4] => altsyncram_tjb1:auto_generated.address_a[4]
address_a[5] => altsyncram_tjb1:auto_generated.address_a[5]
address_a[6] => altsyncram_tjb1:auto_generated.address_a[6]
address_a[7] => altsyncram_tjb1:auto_generated.address_a[7]
address_a[8] => altsyncram_tjb1:auto_generated.address_a[8]
address_a[9] => altsyncram_tjb1:auto_generated.address_a[9]
address_b[0] => ~NO_FANOUT~
addressstall_a => ~NO_FANOUT~
addressstall_b => ~NO_FANOUT~
clock0 => altsyncram_tjb1:auto_generated.clock0
clock1 => ~NO_FANOUT~
clocken0 => ~NO_FANOUT~
clocken1 => ~NO_FANOUT~
clocken2 => ~NO_FANOUT~
clocken3 => ~NO_FANOUT~
aclr0 => ~NO_FANOUT~
aclr1 => ~NO_FANOUT~
byteena_a[0] => ~NO_FANOUT~
byteena_b[0] => ~NO_FANOUT~
q_a[0] <= altsyncram_tjb1:auto_generated.q_a[0]
q_a[1] <= altsyncram_tjb1:auto_generated.q_a[1]
q_a[2] <= altsyncram_tjb1:auto_generated.q_a[2]
q_a[3] <= altsyncram_tjb1:auto_generated.q_a[3]
q_a[4] <= altsyncram_tjb1:auto_generated.q_a[4]
q_a[5] <= altsyncram_tjb1:auto_generated.q_a[5]
q_a[6] <= altsyncram_tjb1:auto_generated.q_a[6]
q_a[7] <= altsyncram_tjb1:auto_generated.q_a[7]
q_a[8] <= altsyncram_tjb1:auto_generated.q_a[8]
q_a[9] <= altsyncram_tjb1:auto_generated.q_a[9]
q_a[10] <= altsyncram_tjb1:auto_generated.q_a[10]
q_a[11] <= altsyncram_tjb1:auto_generated.q_a[11]
q_a[12] <= altsyncram_tjb1:auto_generated.q_a[12]
q_a[13] <= altsyncram_tjb1:auto_generated.q_a[13]
q_b[0] <= <GND>
eccstatus[0] <= <GND>
eccstatus[1] <= <GND>
eccstatus[2] <= <GND>


|TOP|DA_WAVEFORM_B:inst18|triangle_rom:triangle_rom_inst|altsyncram:altsyncram_component|altsyncram_tjb1:auto_generated
address_a[0] => ram_block1a0.PORTAADDR
address_a[0] => ram_block1a1.PORTAADDR
address_a[0] => ram_block1a2.PORTAADDR
address_a[0] => ram_block1a3.PORTAADDR
address_a[0] => ram_block1a4.PORTAADDR
address_a[0] => ram_block1a5.PORTAADDR
address_a[0] => ram_block1a6.PORTAADDR
address_a[0] => ram_block1a7.PORTAADDR
address_a[0] => ram_block1a8.PORTAADDR
address_a[0] => ram_block1a9.PORTAADDR
address_a[0] => ram_block1a10.PORTAADDR
address_a[0] => ram_block1a11.PORTAADDR
address_a[0] => ram_block1a12.PORTAADDR
address_a[0] => ram_block1a13.PORTAADDR
address_a[1] => ram_block1a0.PORTAADDR1
address_a[1] => ram_block1a1.PORTAADDR1
address_a[1] => ram_block1a2.PORTAADDR1
address_a[1] => ram_block1a3.PORTAADDR1
address_a[1] => ram_block1a4.PORTAADDR1
address_a[1] => ram_block1a5.PORTAADDR1
address_a[1] => ram_block1a6.PORTAADDR1
address_a[1] => ram_block1a7.PORTAADDR1
address_a[1] => ram_block1a8.PORTAADDR1
address_a[1] => ram_block1a9.PORTAADDR1
address_a[1] => ram_block1a10.PORTAADDR1
address_a[1] => ram_block1a11.PORTAADDR1
address_a[1] => ram_block1a12.PORTAADDR1
address_a[1] => ram_block1a13.PORTAADDR1
address_a[2] => ram_block1a0.PORTAADDR2
address_a[2] => ram_block1a1.PORTAADDR2
address_a[2] => ram_block1a2.PORTAADDR2
address_a[2] => ram_block1a3.PORTAADDR2
address_a[2] => ram_block1a4.PORTAADDR2
address_a[2] => ram_block1a5.PORTAADDR2
address_a[2] => ram_block1a6.PORTAADDR2
address_a[2] => ram_block1a7.PORTAADDR2
address_a[2] => ram_block1a8.PORTAADDR2
address_a[2] => ram_block1a9.PORTAADDR2
address_a[2] => ram_block1a10.PORTAADDR2
address_a[2] => ram_block1a11.PORTAADDR2
address_a[2] => ram_block1a12.PORTAADDR2
address_a[2] => ram_block1a13.PORTAADDR2
address_a[3] => ram_block1a0.PORTAADDR3
address_a[3] => ram_block1a1.PORTAADDR3
address_a[3] => ram_block1a2.PORTAADDR3
address_a[3] => ram_block1a3.PORTAADDR3
address_a[3] => ram_block1a4.PORTAADDR3
address_a[3] => ram_block1a5.PORTAADDR3
address_a[3] => ram_block1a6.PORTAADDR3
address_a[3] => ram_block1a7.PORTAADDR3
address_a[3] => ram_block1a8.PORTAADDR3
address_a[3] => ram_block1a9.PORTAADDR3
address_a[3] => ram_block1a10.PORTAADDR3
address_a[3] => ram_block1a11.PORTAADDR3
address_a[3] => ram_block1a12.PORTAADDR3
address_a[3] => ram_block1a13.PORTAADDR3
address_a[4] => ram_block1a0.PORTAADDR4
address_a[4] => ram_block1a1.PORTAADDR4
address_a[4] => ram_block1a2.PORTAADDR4
address_a[4] => ram_block1a3.PORTAADDR4
address_a[4] => ram_block1a4.PORTAADDR4
address_a[4] => ram_block1a5.PORTAADDR4
address_a[4] => ram_block1a6.PORTAADDR4
address_a[4] => ram_block1a7.PORTAADDR4
address_a[4] => ram_block1a8.PORTAADDR4
address_a[4] => ram_block1a9.PORTAADDR4
address_a[4] => ram_block1a10.PORTAADDR4
address_a[4] => ram_block1a11.PORTAADDR4
address_a[4] => ram_block1a12.PORTAADDR4
address_a[4] => ram_block1a13.PORTAADDR4
address_a[5] => ram_block1a0.PORTAADDR5
address_a[5] => ram_block1a1.PORTAADDR5
address_a[5] => ram_block1a2.PORTAADDR5
address_a[5] => ram_block1a3.PORTAADDR5
address_a[5] => ram_block1a4.PORTAADDR5
address_a[5] => ram_block1a5.PORTAADDR5
address_a[5] => ram_block1a6.PORTAADDR5
address_a[5] => ram_block1a7.PORTAADDR5
address_a[5] => ram_block1a8.PORTAADDR5
address_a[5] => ram_block1a9.PORTAADDR5
address_a[5] => ram_block1a10.PORTAADDR5
address_a[5] => ram_block1a11.PORTAADDR5
address_a[5] => ram_block1a12.PORTAADDR5
address_a[5] => ram_block1a13.PORTAADDR5
address_a[6] => ram_block1a0.PORTAADDR6
address_a[6] => ram_block1a1.PORTAADDR6
address_a[6] => ram_block1a2.PORTAADDR6
address_a[6] => ram_block1a3.PORTAADDR6
address_a[6] => ram_block1a4.PORTAADDR6
address_a[6] => ram_block1a5.PORTAADDR6
address_a[6] => ram_block1a6.PORTAADDR6
address_a[6] => ram_block1a7.PORTAADDR6
address_a[6] => ram_block1a8.PORTAADDR6
address_a[6] => ram_block1a9.PORTAADDR6
address_a[6] => ram_block1a10.PORTAADDR6
address_a[6] => ram_block1a11.PORTAADDR6
address_a[6] => ram_block1a12.PORTAADDR6
address_a[6] => ram_block1a13.PORTAADDR6
address_a[7] => ram_block1a0.PORTAADDR7
address_a[7] => ram_block1a1.PORTAADDR7
address_a[7] => ram_block1a2.PORTAADDR7
address_a[7] => ram_block1a3.PORTAADDR7
address_a[7] => ram_block1a4.PORTAADDR7
address_a[7] => ram_block1a5.PORTAADDR7
address_a[7] => ram_block1a6.PORTAADDR7
address_a[7] => ram_block1a7.PORTAADDR7
address_a[7] => ram_block1a8.PORTAADDR7
address_a[7] => ram_block1a9.PORTAADDR7
address_a[7] => ram_block1a10.PORTAADDR7
address_a[7] => ram_block1a11.PORTAADDR7
address_a[7] => ram_block1a12.PORTAADDR7
address_a[7] => ram_block1a13.PORTAADDR7
address_a[8] => ram_block1a0.PORTAADDR8
address_a[8] => ram_block1a1.PORTAADDR8
address_a[8] => ram_block1a2.PORTAADDR8
address_a[8] => ram_block1a3.PORTAADDR8
address_a[8] => ram_block1a4.PORTAADDR8
address_a[8] => ram_block1a5.PORTAADDR8
address_a[8] => ram_block1a6.PORTAADDR8
address_a[8] => ram_block1a7.PORTAADDR8
address_a[8] => ram_block1a8.PORTAADDR8
address_a[8] => ram_block1a9.PORTAADDR8
address_a[8] => ram_block1a10.PORTAADDR8
address_a[8] => ram_block1a11.PORTAADDR8
address_a[8] => ram_block1a12.PORTAADDR8
address_a[8] => ram_block1a13.PORTAADDR8
address_a[9] => ram_block1a0.PORTAADDR9
address_a[9] => ram_block1a1.PORTAADDR9
address_a[9] => ram_block1a2.PORTAADDR9
address_a[9] => ram_block1a3.PORTAADDR9
address_a[9] => ram_block1a4.PORTAADDR9
address_a[9] => ram_block1a5.PORTAADDR9
address_a[9] => ram_block1a6.PORTAADDR9
address_a[9] => ram_block1a7.PORTAADDR9
address_a[9] => ram_block1a8.PORTAADDR9
address_a[9] => ram_block1a9.PORTAADDR9
address_a[9] => ram_block1a10.PORTAADDR9
address_a[9] => ram_block1a11.PORTAADDR9
address_a[9] => ram_block1a12.PORTAADDR9
address_a[9] => ram_block1a13.PORTAADDR9
clock0 => ram_block1a0.CLK0
clock0 => ram_block1a1.CLK0
clock0 => ram_block1a2.CLK0
clock0 => ram_block1a3.CLK0
clock0 => ram_block1a4.CLK0
clock0 => ram_block1a5.CLK0
clock0 => ram_block1a6.CLK0
clock0 => ram_block1a7.CLK0
clock0 => ram_block1a8.CLK0
clock0 => ram_block1a9.CLK0
clock0 => ram_block1a10.CLK0
clock0 => ram_block1a11.CLK0
clock0 => ram_block1a12.CLK0
clock0 => ram_block1a13.CLK0
q_a[0] <= ram_block1a0.PORTADATAOUT
q_a[1] <= ram_block1a1.PORTADATAOUT
q_a[2] <= ram_block1a2.PORTADATAOUT
q_a[3] <= ram_block1a3.PORTADATAOUT
q_a[4] <= ram_block1a4.PORTADATAOUT
q_a[5] <= ram_block1a5.PORTADATAOUT
q_a[6] <= ram_block1a6.PORTADATAOUT
q_a[7] <= ram_block1a7.PORTADATAOUT
q_a[8] <= ram_block1a8.PORTADATAOUT
q_a[9] <= ram_block1a9.PORTADATAOUT
q_a[10] <= ram_block1a10.PORTADATAOUT
q_a[11] <= ram_block1a11.PORTADATAOUT
q_a[12] <= ram_block1a12.PORTADATAOUT
q_a[13] <= ram_block1a13.PORTADATAOUT


|TOP|DA_WAVEFORM_B:inst18|sawtooth_rom:sawtooth_rom_inst
address[0] => address[0].IN1
address[1] => address[1].IN1
address[2] => address[2].IN1
address[3] => address[3].IN1
address[4] => address[4].IN1
address[5] => address[5].IN1
address[6] => address[6].IN1
address[7] => address[7].IN1
address[8] => address[8].IN1
address[9] => address[9].IN1
clock => clock.IN1
q[0] <= altsyncram:altsyncram_component.q_a
q[1] <= altsyncram:altsyncram_component.q_a
q[2] <= altsyncram:altsyncram_component.q_a
q[3] <= altsyncram:altsyncram_component.q_a
q[4] <= altsyncram:altsyncram_component.q_a
q[5] <= altsyncram:altsyncram_component.q_a
q[6] <= altsyncram:altsyncram_component.q_a
q[7] <= altsyncram:altsyncram_component.q_a
q[8] <= altsyncram:altsyncram_component.q_a
q[9] <= altsyncram:altsyncram_component.q_a
q[10] <= altsyncram:altsyncram_component.q_a
q[11] <= altsyncram:altsyncram_component.q_a
q[12] <= altsyncram:altsyncram_component.q_a
q[13] <= altsyncram:altsyncram_component.q_a


|TOP|DA_WAVEFORM_B:inst18|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component
wren_a => ~NO_FANOUT~
rden_a => ~NO_FANOUT~
wren_b => ~NO_FANOUT~
rden_b => ~NO_FANOUT~
data_a[0] => ~NO_FANOUT~
data_a[1] => ~NO_FANOUT~
data_a[2] => ~NO_FANOUT~
data_a[3] => ~NO_FANOUT~
data_a[4] => ~NO_FANOUT~
data_a[5] => ~NO_FANOUT~
data_a[6] => ~NO_FANOUT~
data_a[7] => ~NO_FANOUT~
data_a[8] => ~NO_FANOUT~
data_a[9] => ~NO_FANOUT~
data_a[10] => ~NO_FANOUT~
data_a[11] => ~NO_FANOUT~
data_a[12] => ~NO_FANOUT~
data_a[13] => ~NO_FANOUT~
data_b[0] => ~NO_FANOUT~
address_a[0] => altsyncram_0lb1:auto_generated.address_a[0]
address_a[1] => altsyncram_0lb1:auto_generated.address_a[1]
address_a[2] => altsyncram_0lb1:auto_generated.address_a[2]
address_a[3] => altsyncram_0lb1:auto_generated.address_a[3]
address_a[4] => altsyncram_0lb1:auto_generated.address_a[4]
address_a[5] => altsyncram_0lb1:auto_generated.address_a[5]
address_a[6] => altsyncram_0lb1:auto_generated.address_a[6]
address_a[7] => altsyncram_0lb1:auto_generated.address_a[7]
address_a[8] => altsyncram_0lb1:auto_generated.address_a[8]
address_a[9] => altsyncram_0lb1:auto_generated.address_a[9]
address_b[0] => ~NO_FANOUT~
addressstall_a => ~NO_FANOUT~
addressstall_b => ~NO_FANOUT~
clock0 => altsyncram_0lb1:auto_generated.clock0
clock1 => ~NO_FANOUT~
clocken0 => ~NO_FANOUT~
clocken1 => ~NO_FANOUT~
clocken2 => ~NO_FANOUT~
clocken3 => ~NO_FANOUT~
aclr0 => ~NO_FANOUT~
aclr1 => ~NO_FANOUT~
byteena_a[0] => ~NO_FANOUT~
byteena_b[0] => ~NO_FANOUT~
q_a[0] <= altsyncram_0lb1:auto_generated.q_a[0]
q_a[1] <= altsyncram_0lb1:auto_generated.q_a[1]
q_a[2] <= altsyncram_0lb1:auto_generated.q_a[2]
q_a[3] <= altsyncram_0lb1:auto_generated.q_a[3]
q_a[4] <= altsyncram_0lb1:auto_generated.q_a[4]
q_a[5] <= altsyncram_0lb1:auto_generated.q_a[5]
q_a[6] <= altsyncram_0lb1:auto_generated.q_a[6]
q_a[7] <= altsyncram_0lb1:auto_generated.q_a[7]
q_a[8] <= altsyncram_0lb1:auto_generated.q_a[8]
q_a[9] <= altsyncram_0lb1:auto_generated.q_a[9]
q_a[10] <= altsyncram_0lb1:auto_generated.q_a[10]
q_a[11] <= altsyncram_0lb1:auto_generated.q_a[11]
q_a[12] <= altsyncram_0lb1:auto_generated.q_a[12]
q_a[13] <= altsyncram_0lb1:auto_generated.q_a[13]
q_b[0] <= <GND>
eccstatus[0] <= <GND>
eccstatus[1] <= <GND>
eccstatus[2] <= <GND>


|TOP|DA_WAVEFORM_B:inst18|sawtooth_rom:sawtooth_rom_inst|altsyncram:altsyncram_component|altsyncram_0lb1:auto_generated
address_a[0] => ram_block1a0.PORTAADDR
address_a[0] => ram_block1a1.PORTAADDR
address_a[0] => ram_block1a2.PORTAADDR
address_a[0] => ram_block1a3.PORTAADDR
address_a[0] => ram_block1a4.PORTAADDR
address_a[0] => ram_block1a5.PORTAADDR
address_a[0] => ram_block1a6.PORTAADDR
address_a[0] => ram_block1a7.PORTAADDR
address_a[0] => ram_block1a8.PORTAADDR
address_a[0] => ram_block1a9.PORTAADDR
address_a[0] => ram_block1a10.PORTAADDR
address_a[0] => ram_block1a11.PORTAADDR
address_a[0] => ram_block1a12.PORTAADDR
address_a[0] => ram_block1a13.PORTAADDR
address_a[1] => ram_block1a0.PORTAADDR1
address_a[1] => ram_block1a1.PORTAADDR1
address_a[1] => ram_block1a2.PORTAADDR1
address_a[1] => ram_block1a3.PORTAADDR1
address_a[1] => ram_block1a4.PORTAADDR1
address_a[1] => ram_block1a5.PORTAADDR1
address_a[1] => ram_block1a6.PORTAADDR1
address_a[1] => ram_block1a7.PORTAADDR1
address_a[1] => ram_block1a8.PORTAADDR1
address_a[1] => ram_block1a9.PORTAADDR1
address_a[1] => ram_block1a10.PORTAADDR1
address_a[1] => ram_block1a11.PORTAADDR1
address_a[1] => ram_block1a12.PORTAADDR1
address_a[1] => ram_block1a13.PORTAADDR1
address_a[2] => ram_block1a0.PORTAADDR2
address_a[2] => ram_block1a1.PORTAADDR2
address_a[2] => ram_block1a2.PORTAADDR2
address_a[2] => ram_block1a3.PORTAADDR2
address_a[2] => ram_block1a4.PORTAADDR2
address_a[2] => ram_block1a5.PORTAADDR2
address_a[2] => ram_block1a6.PORTAADDR2
address_a[2] => ram_block1a7.PORTAADDR2
address_a[2] => ram_block1a8.PORTAADDR2
address_a[2] => ram_block1a9.PORTAADDR2
address_a[2] => ram_block1a10.PORTAADDR2
address_a[2] => ram_block1a11.PORTAADDR2
address_a[2] => ram_block1a12.PORTAADDR2
address_a[2] => ram_block1a13.PORTAADDR2
address_a[3] => ram_block1a0.PORTAADDR3
address_a[3] => ram_block1a1.PORTAADDR3
address_a[3] => ram_block1a2.PORTAADDR3
address_a[3] => ram_block1a3.PORTAADDR3
address_a[3] => ram_block1a4.PORTAADDR3
address_a[3] => ram_block1a5.PORTAADDR3
address_a[3] => ram_block1a6.PORTAADDR3
address_a[3] => ram_block1a7.PORTAADDR3
address_a[3] => ram_block1a8.PORTAADDR3
address_a[3] => ram_block1a9.PORTAADDR3
address_a[3] => ram_block1a10.PORTAADDR3
address_a[3] => ram_block1a11.PORTAADDR3
address_a[3] => ram_block1a12.PORTAADDR3
address_a[3] => ram_block1a13.PORTAADDR3
address_a[4] => ram_block1a0.PORTAADDR4
address_a[4] => ram_block1a1.PORTAADDR4
address_a[4] => ram_block1a2.PORTAADDR4
address_a[4] => ram_block1a3.PORTAADDR4
address_a[4] => ram_block1a4.PORTAADDR4
address_a[4] => ram_block1a5.PORTAADDR4
address_a[4] => ram_block1a6.PORTAADDR4
address_a[4] => ram_block1a7.PORTAADDR4
address_a[4] => ram_block1a8.PORTAADDR4
address_a[4] => ram_block1a9.PORTAADDR4
address_a[4] => ram_block1a10.PORTAADDR4
address_a[4] => ram_block1a11.PORTAADDR4
address_a[4] => ram_block1a12.PORTAADDR4
address_a[4] => ram_block1a13.PORTAADDR4
address_a[5] => ram_block1a0.PORTAADDR5
address_a[5] => ram_block1a1.PORTAADDR5
address_a[5] => ram_block1a2.PORTAADDR5
address_a[5] => ram_block1a3.PORTAADDR5
address_a[5] => ram_block1a4.PORTAADDR5
address_a[5] => ram_block1a5.PORTAADDR5
address_a[5] => ram_block1a6.PORTAADDR5
address_a[5] => ram_block1a7.PORTAADDR5
address_a[5] => ram_block1a8.PORTAADDR5
address_a[5] => ram_block1a9.PORTAADDR5
address_a[5] => ram_block1a10.PORTAADDR5
address_a[5] => ram_block1a11.PORTAADDR5
address_a[5] => ram_block1a12.PORTAADDR5
address_a[5] => ram_block1a13.PORTAADDR5
address_a[6] => ram_block1a0.PORTAADDR6
address_a[6] => ram_block1a1.PORTAADDR6
address_a[6] => ram_block1a2.PORTAADDR6
address_a[6] => ram_block1a3.PORTAADDR6
address_a[6] => ram_block1a4.PORTAADDR6
address_a[6] => ram_block1a5.PORTAADDR6
address_a[6] => ram_block1a6.PORTAADDR6
address_a[6] => ram_block1a7.PORTAADDR6
address_a[6] => ram_block1a8.PORTAADDR6
address_a[6] => ram_block1a9.PORTAADDR6
address_a[6] => ram_block1a10.PORTAADDR6
address_a[6] => ram_block1a11.PORTAADDR6
address_a[6] => ram_block1a12.PORTAADDR6
address_a[6] => ram_block1a13.PORTAADDR6
address_a[7] => ram_block1a0.PORTAADDR7
address_a[7] => ram_block1a1.PORTAADDR7
address_a[7] => ram_block1a2.PORTAADDR7
address_a[7] => ram_block1a3.PORTAADDR7
address_a[7] => ram_block1a4.PORTAADDR7
address_a[7] => ram_block1a5.PORTAADDR7
address_a[7] => ram_block1a6.PORTAADDR7
address_a[7] => ram_block1a7.PORTAADDR7
address_a[7] => ram_block1a8.PORTAADDR7
address_a[7] => ram_block1a9.PORTAADDR7
address_a[7] => ram_block1a10.PORTAADDR7
address_a[7] => ram_block1a11.PORTAADDR7
address_a[7] => ram_block1a12.PORTAADDR7
address_a[7] => ram_block1a13.PORTAADDR7
address_a[8] => ram_block1a0.PORTAADDR8
address_a[8] => ram_block1a1.PORTAADDR8
address_a[8] => ram_block1a2.PORTAADDR8
address_a[8] => ram_block1a3.PORTAADDR8
address_a[8] => ram_block1a4.PORTAADDR8
address_a[8] => ram_block1a5.PORTAADDR8
address_a[8] => ram_block1a6.PORTAADDR8
address_a[8] => ram_block1a7.PORTAADDR8
address_a[8] => ram_block1a8.PORTAADDR8
address_a[8] => ram_block1a9.PORTAADDR8
address_a[8] => ram_block1a10.PORTAADDR8
address_a[8] => ram_block1a11.PORTAADDR8
address_a[8] => ram_block1a12.PORTAADDR8
address_a[8] => ram_block1a13.PORTAADDR8
address_a[9] => ram_block1a0.PORTAADDR9
address_a[9] => ram_block1a1.PORTAADDR9
address_a[9] => ram_block1a2.PORTAADDR9
address_a[9] => ram_block1a3.PORTAADDR9
address_a[9] => ram_block1a4.PORTAADDR9
address_a[9] => ram_block1a5.PORTAADDR9
address_a[9] => ram_block1a6.PORTAADDR9
address_a[9] => ram_block1a7.PORTAADDR9
address_a[9] => ram_block1a8.PORTAADDR9
address_a[9] => ram_block1a9.PORTAADDR9
address_a[9] => ram_block1a10.PORTAADDR9
address_a[9] => ram_block1a11.PORTAADDR9
address_a[9] => ram_block1a12.PORTAADDR9
address_a[9] => ram_block1a13.PORTAADDR9
clock0 => ram_block1a0.CLK0
clock0 => ram_block1a1.CLK0
clock0 => ram_block1a2.CLK0
clock0 => ram_block1a3.CLK0
clock0 => ram_block1a4.CLK0
clock0 => ram_block1a5.CLK0
clock0 => ram_block1a6.CLK0
clock0 => ram_block1a7.CLK0
clock0 => ram_block1a8.CLK0
clock0 => ram_block1a9.CLK0
clock0 => ram_block1a10.CLK0
clock0 => ram_block1a11.CLK0
clock0 => ram_block1a12.CLK0
clock0 => ram_block1a13.CLK0
q_a[0] <= ram_block1a0.PORTADATAOUT
q_a[1] <= ram_block1a1.PORTADATAOUT
q_a[2] <= ram_block1a2.PORTADATAOUT
q_a[3] <= ram_block1a3.PORTADATAOUT
q_a[4] <= ram_block1a4.PORTADATAOUT
q_a[5] <= ram_block1a5.PORTADATAOUT
q_a[6] <= ram_block1a6.PORTADATAOUT
q_a[7] <= ram_block1a7.PORTADATAOUT
q_a[8] <= ram_block1a8.PORTADATAOUT
q_a[9] <= ram_block1a9.PORTADATAOUT
q_a[10] <= ram_block1a10.PORTADATAOUT
q_a[11] <= ram_block1a11.PORTADATAOUT
q_a[12] <= ram_block1a12.PORTADATAOUT
q_a[13] <= ram_block1a13.PORTADATAOUT


