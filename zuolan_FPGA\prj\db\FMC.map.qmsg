{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1721436013191 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus Prime " "Running Quartus Prime Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Standard Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Standard Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1721436013195 ""} { "Info" "IQEXE_START_BANNER_TIME" "Sat Jul 20 08:40:13 2024 " "Processing started: Sat Jul 20 08:40:13 2024" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1721436013195 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436013195 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off JYX_FPGA_OBJECT -c FMC " "Command: quartus_map --read_settings_files=on --write_settings_files=off JYX_FPGA_OBJECT -c FMC" {  } {  } 0 0 "Command: %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436013195 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "12 12 " "Parallel compilation is enabled and will use 12 of the 12 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Analysis & Synthesis" 0 -1 1721436013359 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/zuolan/desktop/ee_object/jyx_fpga_object/src/ad_freq_measure.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/zuolan/desktop/ee_object/jyx_fpga_object/src/ad_freq_measure.v" { { "Info" "ISGN_ENTITY_NAME" "1 AD_FREQ_MEASURE " "Found entity 1: AD_FREQ_MEASURE" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018724 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018724 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/zuolan/desktop/ee_object/jyx_fpga_object/src/cnt32.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/zuolan/desktop/ee_object/jyx_fpga_object/src/cnt32.v" { { "Info" "ISGN_ENTITY_NAME" "1 CNT32 " "Found entity 1: CNT32" {  } { { "../src/CNT32.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/CNT32.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018726 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018726 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/zuolan/desktop/ee_object/jyx_fpga_object/src/ad_data_deal.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/zuolan/desktop/ee_object/jyx_fpga_object/src/ad_data_deal.v" { { "Info" "ISGN_ENTITY_NAME" "1 AD_DATA_DEAL " "Found entity 1: AD_DATA_DEAL" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018728 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018728 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/zuolan/desktop/ee_object/jyx_fpga_object/src/ad_freq_word.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/zuolan/desktop/ee_object/jyx_fpga_object/src/ad_freq_word.v" { { "Info" "ISGN_ENTITY_NAME" "1 AD_FREQ_WORD " "Found entity 1: AD_FREQ_WORD" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018729 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018729 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/zuolan/desktop/ee_object/jyx_fpga_object/src/da_freq_word.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/zuolan/desktop/ee_object/jyx_fpga_object/src/da_freq_word.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_FREQ_WORD " "Found entity 1: DA_FREQ_WORD" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018731 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018731 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/zuolan/desktop/ee_object/jyx_fpga_object/src/cnt10.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/zuolan/desktop/ee_object/jyx_fpga_object/src/cnt10.v" { { "Info" "ISGN_ENTITY_NAME" "1 CNT10 " "Found entity 1: CNT10" {  } { { "../src/CNT10.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/CNT10.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018733 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018733 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/zuolan/desktop/ee_object/jyx_fpga_object/src/freq_dev.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/zuolan/desktop/ee_object/jyx_fpga_object/src/freq_dev.v" { { "Info" "ISGN_ENTITY_NAME" "1 FREQ_DEV " "Found entity 1: FREQ_DEV" {  } { { "../src/FREQ_DEV.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FREQ_DEV.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018735 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018735 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/zuolan/desktop/ee_object/jyx_fpga_object/src/master_ctrl.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/zuolan/desktop/ee_object/jyx_fpga_object/src/master_ctrl.v" { { "Info" "ISGN_ENTITY_NAME" "1 MASTER_CTRL " "Found entity 1: MASTER_CTRL" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018737 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018737 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/zuolan/desktop/ee_object/jyx_fpga_object/src/fmc/fmc_control.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/zuolan/desktop/ee_object/jyx_fpga_object/src/fmc/fmc_control.v" { { "Info" "ISGN_ENTITY_NAME" "1 fmc_control " "Found entity 1: fmc_control" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 13 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018739 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018739 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/zuolan/desktop/ee_object/jyx_fpga_object/src/test.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/zuolan/desktop/ee_object/jyx_fpga_object/src/test.v" { { "Info" "ISGN_ENTITY_NAME" "1 test " "Found entity 1: test" {  } { { "../src/test.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/test.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018741 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018741 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "top.bdf 1 1 " "Found 1 design units, including 1 entities, in source file top.bdf" { { "Info" "ISGN_ENTITY_NAME" "1 TOP " "Found entity 1: TOP" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { } } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018742 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018742 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/zuolan/desktop/ee_object/jyx_fpga_object/ip/mypll.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/zuolan/desktop/ee_object/jyx_fpga_object/ip/mypll.v" { { "Info" "ISGN_ENTITY_NAME" "1 MYPLL " "Found entity 1: MYPLL" {  } { { "../ip/MYPLL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/ip/MYPLL.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018745 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018745 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/zuolan/desktop/ee_object/jyx_fpga_object/ip/sinrom.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/zuolan/desktop/ee_object/jyx_fpga_object/ip/sinrom.v" { { "Info" "ISGN_ENTITY_NAME" "1 SINROM " "Found entity 1: SINROM" {  } { { "../ip/SINROM.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/ip/SINROM.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018747 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018747 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/zuolan/desktop/ee_object/jyx_fpga_object/ip/tyfifo.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/zuolan/desktop/ee_object/jyx_fpga_object/ip/tyfifo.v" { { "Info" "ISGN_ENTITY_NAME" "1 TYFIFO " "Found entity 1: TYFIFO" {  } { { "../ip/TYFIFO.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/ip/TYFIFO.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018749 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018749 ""}
{ "Info" "ISGN_START_ELABORATION_TOP" "TOP " "Elaborating entity \"TOP\" for the top level hierarchy" {  } {  } 0 12127 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "Analysis & Synthesis" 0 -1 1721436018763 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "FREQ_DEV FREQ_DEV:u_DA1_DEV " "Elaborating entity \"FREQ_DEV\" for hierarchy \"FREQ_DEV:u_DA1_DEV\"" {  } { { "TOP.bdf" "u_DA1_DEV" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 856 2656 2888 968 "u_DA1_DEV" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436018779 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "MYPLL MYPLL:inst1 " "Elaborating entity \"MYPLL\" for hierarchy \"MYPLL:inst1\"" {  } { { "TOP.bdf" "inst1" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { -152 264 544 40 "inst1" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436018790 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altpll MYPLL:inst1\|altpll:altpll_component " "Elaborating entity \"altpll\" for hierarchy \"MYPLL:inst1\|altpll:altpll_component\"" {  } { { "../ip/MYPLL.v" "altpll_component" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/ip/MYPLL.v" 94 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436018817 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "MYPLL:inst1\|altpll:altpll_component " "Elaborated megafunction instantiation \"MYPLL:inst1\|altpll:altpll_component\"" {  } { { "../ip/MYPLL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/ip/MYPLL.v" 94 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436018825 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "MYPLL:inst1\|altpll:altpll_component " "Instantiated megafunction \"MYPLL:inst1\|altpll:altpll_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "bandwidth_type AUTO " "Parameter \"bandwidth_type\" = \"AUTO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_divide_by 1 " "Parameter \"clk0_divide_by\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_duty_cycle 50 " "Parameter \"clk0_duty_cycle\" = \"50\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_multiply_by 3 " "Parameter \"clk0_multiply_by\" = \"3\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_phase_shift 0 " "Parameter \"clk0_phase_shift\" = \"0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk1_divide_by 293 " "Parameter \"clk1_divide_by\" = \"293\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk1_duty_cycle 50 " "Parameter \"clk1_duty_cycle\" = \"50\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk1_multiply_by 6 " "Parameter \"clk1_multiply_by\" = \"6\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk1_phase_shift 0 " "Parameter \"clk1_phase_shift\" = \"0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "compensate_clock CLK0 " "Parameter \"compensate_clock\" = \"CLK0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "inclk0_input_frequency 20000 " "Parameter \"inclk0_input_frequency\" = \"20000\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint CBX_MODULE_PREFIX=MYPLL " "Parameter \"lpm_hint\" = \"CBX_MODULE_PREFIX=MYPLL\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altpll " "Parameter \"lpm_type\" = \"altpll\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode NORMAL " "Parameter \"operation_mode\" = \"NORMAL\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "pll_type AUTO " "Parameter \"pll_type\" = \"AUTO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_activeclock PORT_UNUSED " "Parameter \"port_activeclock\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_areset PORT_UNUSED " "Parameter \"port_areset\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad0 PORT_UNUSED " "Parameter \"port_clkbad0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad1 PORT_UNUSED " "Parameter \"port_clkbad1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkloss PORT_UNUSED " "Parameter \"port_clkloss\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkswitch PORT_UNUSED " "Parameter \"port_clkswitch\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_configupdate PORT_UNUSED " "Parameter \"port_configupdate\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_fbin PORT_UNUSED " "Parameter \"port_fbin\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk0 PORT_USED " "Parameter \"port_inclk0\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk1 PORT_UNUSED " "Parameter \"port_inclk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_locked PORT_UNUSED " "Parameter \"port_locked\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pfdena PORT_UNUSED " "Parameter \"port_pfdena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasecounterselect PORT_UNUSED " "Parameter \"port_phasecounterselect\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasedone PORT_UNUSED " "Parameter \"port_phasedone\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasestep PORT_UNUSED " "Parameter \"port_phasestep\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phaseupdown PORT_UNUSED " "Parameter \"port_phaseupdown\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pllena PORT_UNUSED " "Parameter \"port_pllena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanaclr PORT_UNUSED " "Parameter \"port_scanaclr\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclk PORT_UNUSED " "Parameter \"port_scanclk\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclkena PORT_UNUSED " "Parameter \"port_scanclkena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandata PORT_UNUSED " "Parameter \"port_scandata\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandataout PORT_UNUSED " "Parameter \"port_scandataout\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandone PORT_UNUSED " "Parameter \"port_scandone\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanread PORT_UNUSED " "Parameter \"port_scanread\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanwrite PORT_UNUSED " "Parameter \"port_scanwrite\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk0 PORT_USED " "Parameter \"port_clk0\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk1 PORT_USED " "Parameter \"port_clk1\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk2 PORT_UNUSED " "Parameter \"port_clk2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk3 PORT_UNUSED " "Parameter \"port_clk3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk4 PORT_UNUSED " "Parameter \"port_clk4\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk5 PORT_UNUSED " "Parameter \"port_clk5\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena0 PORT_UNUSED " "Parameter \"port_clkena0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena1 PORT_UNUSED " "Parameter \"port_clkena1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena2 PORT_UNUSED " "Parameter \"port_clkena2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena3 PORT_UNUSED " "Parameter \"port_clkena3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena4 PORT_UNUSED " "Parameter \"port_clkena4\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena5 PORT_UNUSED " "Parameter \"port_clkena5\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk0 PORT_UNUSED " "Parameter \"port_extclk0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk1 PORT_UNUSED " "Parameter \"port_extclk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk2 PORT_UNUSED " "Parameter \"port_extclk2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk3 PORT_UNUSED " "Parameter \"port_extclk3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_clock 5 " "Parameter \"width_clock\" = \"5\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436018825 ""}  } { { "../ip/MYPLL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/ip/MYPLL.v" 94 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1721436018825 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/mypll_altpll.v 1 1 " "Found 1 design units, including 1 entities, in source file db/mypll_altpll.v" { { "Info" "ISGN_ENTITY_NAME" "1 MYPLL_altpll " "Found entity 1: MYPLL_altpll" {  } { { "db/mypll_altpll.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/mypll_altpll.v" 29 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436018853 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018853 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "MYPLL_altpll MYPLL:inst1\|altpll:altpll_component\|MYPLL_altpll:auto_generated " "Elaborating entity \"MYPLL_altpll\" for hierarchy \"MYPLL:inst1\|altpll:altpll_component\|MYPLL_altpll:auto_generated\"" {  } { { "altpll.tdf" "auto_generated" { Text "d:/intelfpga/18.1/quartus/libraries/megafunctions/altpll.tdf" 897 3 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436018853 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "MASTER_CTRL MASTER_CTRL:inst3 " "Elaborating entity \"MASTER_CTRL\" for hierarchy \"MASTER_CTRL:inst3\"" {  } { { "TOP.bdf" "inst3" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 456 -480 -240 568 "inst3" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436018921 ""}
{ "Warning" "WVRFX_VERI_INCOMPLETE_SENSITIVITY_LIST" "DATA MASTER_CTRL.v(12) " "Verilog HDL Always Construct warning at MASTER_CTRL.v(12): variable \"DATA\" is read inside the Always Construct but isn't in the Always Construct's Event Control" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 12 0 0 } }  } 0 10235 "Verilog HDL Always Construct warning at %2!s!: variable \"%1!s!\" is read inside the Always Construct but isn't in the Always Construct's Event Control" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "CTRL_DATA MASTER_CTRL.v(10) " "Verilog HDL Always Construct warning at MASTER_CTRL.v(10): inferring latch(es) for variable \"CTRL_DATA\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[0\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[0\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[1\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[1\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[2\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[2\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[3\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[3\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[4\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[4\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[5\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[5\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[6\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[6\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[7\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[7\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[8\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[8\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[9\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[9\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[10\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[10\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[11\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[11\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[12\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[12\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[13\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[13\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[14\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[14\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[15\] MASTER_CTRL.v(10) " "Inferred latch for \"CTRL_DATA\[15\]\" at MASTER_CTRL.v(10)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/MASTER_CTRL.v" 10 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018922 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "fmc_control fmc_control:inst " "Elaborating entity \"fmc_control\" for hierarchy \"fmc_control:inst\"" {  } { { "TOP.bdf" "inst" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 64 368 640 464 "inst" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436018927 ""}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[0\] fmc_control.v(105) " "Inferred latch for \"addr\[0\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[1\] fmc_control.v(105) " "Inferred latch for \"addr\[1\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[2\] fmc_control.v(105) " "Inferred latch for \"addr\[2\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[3\] fmc_control.v(105) " "Inferred latch for \"addr\[3\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[4\] fmc_control.v(105) " "Inferred latch for \"addr\[4\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[5\] fmc_control.v(105) " "Inferred latch for \"addr\[5\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[6\] fmc_control.v(105) " "Inferred latch for \"addr\[6\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[7\] fmc_control.v(105) " "Inferred latch for \"addr\[7\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[8\] fmc_control.v(105) " "Inferred latch for \"addr\[8\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[9\] fmc_control.v(105) " "Inferred latch for \"addr\[9\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[10\] fmc_control.v(105) " "Inferred latch for \"addr\[10\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[11\] fmc_control.v(105) " "Inferred latch for \"addr\[11\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[12\] fmc_control.v(105) " "Inferred latch for \"addr\[12\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[13\] fmc_control.v(105) " "Inferred latch for \"addr\[13\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[14\] fmc_control.v(105) " "Inferred latch for \"addr\[14\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[15\] fmc_control.v(105) " "Inferred latch for \"addr\[15\]\" at fmc_control.v(105)" {  } { { "../src/FMC/fmc_control.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/FMC/fmc_control.v" 105 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018936 "|TOP|fmc_control:inst"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "AD_FREQ_MEASURE AD_FREQ_MEASURE:u_AD_FREQ_MEASURE " "Elaborating entity \"AD_FREQ_MEASURE\" for hierarchy \"AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\"" {  } { { "TOP.bdf" "u_AD_FREQ_MEASURE" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 288 1824 2192 464 "u_AD_FREQ_MEASURE" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436018949 ""}
{ "Warning" "WVRFX_L2_VERI_INCOMPLETE_CASE_STATEMENT" "AD_FREQ_MEASURE.v(30) " "Verilog HDL Case Statement warning at AD_FREQ_MEASURE.v(30): incomplete case statement has no default case item" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 30 0 0 } }  } 0 10270 "Verilog HDL Case Statement warning at %1!s!: incomplete case statement has no default case item" 0 0 "Analysis & Synthesis" 0 -1 1721436018950 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "BASE1_FREQ_DATA_H AD_FREQ_MEASURE.v(29) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(29): inferring latch(es) for variable \"BASE1_FREQ_DATA_H\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018950 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "BASE1_FREQ_DATA_L AD_FREQ_MEASURE.v(29) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(29): inferring latch(es) for variable \"BASE1_FREQ_DATA_L\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018950 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "BASE2_FREQ_DATA_H AD_FREQ_MEASURE.v(29) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(29): inferring latch(es) for variable \"BASE2_FREQ_DATA_H\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018950 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "BASE2_FREQ_DATA_L AD_FREQ_MEASURE.v(29) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(29): inferring latch(es) for variable \"BASE2_FREQ_DATA_L\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018950 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_FREQ_DATA_H AD_FREQ_MEASURE.v(29) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(29): inferring latch(es) for variable \"AD1_FREQ_DATA_H\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018951 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_FREQ_DATA_L AD_FREQ_MEASURE.v(29) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(29): inferring latch(es) for variable \"AD1_FREQ_DATA_L\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018951 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_FREQ_DATA_H AD_FREQ_MEASURE.v(29) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(29): inferring latch(es) for variable \"AD2_FREQ_DATA_H\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018951 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_FREQ_DATA_L AD_FREQ_MEASURE.v(29) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(29): inferring latch(es) for variable \"AD2_FREQ_DATA_L\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018951 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[0\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[0\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[1\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[1\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[2\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[2\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[3\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[3\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[4\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[4\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[5\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[5\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[6\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[6\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[7\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[7\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[8\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[8\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[9\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[9\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[10\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[10\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[11\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[11\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[12\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[12\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[13\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[13\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[14\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[14\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[15\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_L\[15\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[0\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[0\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[1\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[1\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[2\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[2\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[3\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[3\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[4\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[4\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[5\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[5\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[6\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[6\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[7\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[7\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[8\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[8\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[9\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[9\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[10\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[10\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[11\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[11\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[12\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[12\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[13\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[13\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[14\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[14\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[15\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD2_FREQ_DATA_H\[15\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[0\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[0\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[1\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[1\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018952 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[2\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[2\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[3\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[3\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[4\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[4\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[5\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[5\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[6\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[6\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[7\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[7\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[8\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[8\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[9\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[9\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[10\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[10\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[11\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[11\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[12\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[12\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[13\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[13\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[14\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[14\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[15\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_L\[15\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[0\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[0\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[1\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[1\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[2\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[2\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[3\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[3\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[4\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[4\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[5\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[5\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[6\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[6\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[7\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[7\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[8\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[8\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[9\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[9\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[10\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[10\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[11\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[11\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[12\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[12\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[13\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[13\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[14\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[14\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[15\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"AD1_FREQ_DATA_H\[15\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[0\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[0\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[1\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[1\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[2\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[2\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018953 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[3\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[3\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[4\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[4\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[5\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[5\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[6\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[6\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[7\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[7\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[8\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[8\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[9\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[9\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[10\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[10\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[11\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[11\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[12\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[12\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[13\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[13\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[14\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[14\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[15\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_L\[15\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[0\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[0\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[1\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[1\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[2\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[2\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[3\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[3\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[4\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[4\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[5\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[5\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[6\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[6\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[7\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[7\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[8\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[8\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[9\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[9\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[10\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[10\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[11\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[11\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[12\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[12\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[13\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[13\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[14\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[14\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[15\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE2_FREQ_DATA_H\[15\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[0\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[0\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[1\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[1\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[2\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[2\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[3\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[3\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[4\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[4\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018955 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[5\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[5\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[6\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[6\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[7\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[7\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[8\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[8\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[9\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[9\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[10\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[10\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[11\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[11\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[12\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[12\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[13\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[13\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[14\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[14\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[15\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_L\[15\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[0\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[0\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[1\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[1\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[2\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[2\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[3\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[3\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[4\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[4\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[5\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[5\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[6\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[6\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[7\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[7\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[8\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[8\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[9\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[9\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[10\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[10\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[11\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[11\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[12\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[12\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[13\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[13\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[14\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[14\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[15\] AD_FREQ_MEASURE.v(29) " "Inferred latch for \"BASE1_FREQ_DATA_H\[15\]\" at AD_FREQ_MEASURE.v(29)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_MEASURE.v" 29 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018956 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "CNT32 CNT32:u_AD1_CNT32 " "Elaborating entity \"CNT32\" for hierarchy \"CNT32:u_AD1_CNT32\"" {  } { { "TOP.bdf" "u_AD1_CNT32" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 96 1664 1888 240 "u_AD1_CNT32" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436018963 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "test test:inst2 " "Elaborating entity \"test\" for hierarchy \"test:inst2\"" {  } { { "TOP.bdf" "inst2" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { -40 848 1064 40 "inst2" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436018971 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "AD_DATA_DEAL AD_DATA_DEAL:u_AD_DATA_DEAL " "Elaborating entity \"AD_DATA_DEAL\" for hierarchy \"AD_DATA_DEAL:u_AD_DATA_DEAL\"" {  } { { "TOP.bdf" "u_AD_DATA_DEAL" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 1272 704 1056 1448 "u_AD_DATA_DEAL" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436018975 ""}
{ "Warning" "WVRFX_L2_VERI_INCOMPLETE_CASE_STATEMENT" "AD_DATA_DEAL.v(23) " "Verilog HDL Case Statement warning at AD_DATA_DEAL.v(23): incomplete case statement has no default case item" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 23 0 0 } }  } 0 10270 "Verilog HDL Case Statement warning at %1!s!: incomplete case statement has no default case item" 0 0 "Analysis & Synthesis" 0 -1 1721436018976 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "i AD_DATA_DEAL.v(22) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(22): inferring latch(es) for variable \"i\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018977 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "ad1_fifo_recv AD_DATA_DEAL.v(22) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(22): inferring latch(es) for variable \"ad1_fifo_recv\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018977 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_FIFO_DATA_OUT AD_DATA_DEAL.v(22) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(22): inferring latch(es) for variable \"AD1_FIFO_DATA_OUT\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018977 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_FLAG_SHOW AD_DATA_DEAL.v(22) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(22): inferring latch(es) for variable \"AD1_FLAG_SHOW\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018977 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "ad2_fifo_recv AD_DATA_DEAL.v(22) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(22): inferring latch(es) for variable \"ad2_fifo_recv\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018977 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_FIFO_DATA_OUT AD_DATA_DEAL.v(22) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(22): inferring latch(es) for variable \"AD2_FIFO_DATA_OUT\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018977 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_FLAG_SHOW AD_DATA_DEAL.v(22) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(22): inferring latch(es) for variable \"AD2_FLAG_SHOW\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436018977 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[0\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[0\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018977 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[1\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[1\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018977 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[2\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[2\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018977 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[3\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[3\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[4\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[4\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[5\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[5\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[6\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[6\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[7\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[7\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[8\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[8\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[9\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[9\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[10\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[10\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[11\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[11\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[12\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[12\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[13\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[13\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[14\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[14\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[15\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FLAG_SHOW\[15\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[0\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[0\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[1\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[1\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[2\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[2\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[3\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[3\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[4\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[4\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[5\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[5\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[6\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[6\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[7\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[7\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[8\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[8\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[9\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[9\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[10\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[10\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[11\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[11\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[12\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[12\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[13\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[13\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[14\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[14\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[15\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[15\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[0\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[0\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[1\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[1\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[2\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[2\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[3\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[3\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[4\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[4\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018978 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[5\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[5\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[6\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[6\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[7\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[7\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[8\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[8\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[9\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[9\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[10\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[10\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[11\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[11\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[12\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[12\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[13\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[13\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[14\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[14\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[15\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FLAG_SHOW\[15\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[0\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[0\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[1\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[1\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[2\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[2\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[3\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[3\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[4\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[4\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[5\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[5\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[6\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[6\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[7\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[7\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[8\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[8\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[9\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[9\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[10\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[10\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[11\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[11\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[12\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[12\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[13\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[13\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[14\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[14\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[15\] AD_DATA_DEAL.v(22) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[15\]\" at AD_DATA_DEAL.v(22)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_DATA_DEAL.v" 22 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436018979 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "TYFIFO TYFIFO:u_AD1_FIFO " "Elaborating entity \"TYFIFO\" for hierarchy \"TYFIFO:u_AD1_FIFO\"" {  } { { "TOP.bdf" "u_AD1_FIFO" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 920 432 608 1112 "u_AD1_FIFO" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436018989 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "dcfifo TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component " "Elaborating entity \"dcfifo\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\"" {  } { { "../ip/TYFIFO.v" "dcfifo_component" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/ip/TYFIFO.v" 75 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019189 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component " "Elaborated megafunction instantiation \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\"" {  } { { "../ip/TYFIFO.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/ip/TYFIFO.v" 75 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019194 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component " "Instantiated megafunction \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019194 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_numwords 1024 " "Parameter \"lpm_numwords\" = \"1024\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019194 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_showahead OFF " "Parameter \"lpm_showahead\" = \"OFF\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019194 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type dcfifo " "Parameter \"lpm_type\" = \"dcfifo\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019194 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_width 12 " "Parameter \"lpm_width\" = \"12\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019194 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_widthu 10 " "Parameter \"lpm_widthu\" = \"10\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019194 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "overflow_checking ON " "Parameter \"overflow_checking\" = \"ON\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019194 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "rdsync_delaypipe 4 " "Parameter \"rdsync_delaypipe\" = \"4\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019194 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "underflow_checking ON " "Parameter \"underflow_checking\" = \"ON\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019194 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "use_eab ON " "Parameter \"use_eab\" = \"ON\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019194 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "wrsync_delaypipe 4 " "Parameter \"wrsync_delaypipe\" = \"4\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019194 ""}  } { { "../ip/TYFIFO.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/ip/TYFIFO.v" 75 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1721436019194 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/dcfifo_vve1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/dcfifo_vve1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 dcfifo_vve1 " "Found entity 1: dcfifo_vve1" {  } { { "db/dcfifo_vve1.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/dcfifo_vve1.tdf" 36 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436019220 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019220 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "dcfifo_vve1 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated " "Elaborating entity \"dcfifo_vve1\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\"" {  } { { "dcfifo.tdf" "auto_generated" { Text "d:/intelfpga/18.1/quartus/libraries/megafunctions/dcfifo.tdf" 190 3 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019221 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/a_graycounter_4p6.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/a_graycounter_4p6.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 a_graycounter_4p6 " "Found entity 1: a_graycounter_4p6" {  } { { "db/a_graycounter_4p6.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/a_graycounter_4p6.tdf" 24 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436019249 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019249 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "a_graycounter_4p6 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|a_graycounter_4p6:rdptr_g1p " "Elaborating entity \"a_graycounter_4p6\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|a_graycounter_4p6:rdptr_g1p\"" {  } { { "db/dcfifo_vve1.tdf" "rdptr_g1p" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/dcfifo_vve1.tdf" 47 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019250 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/a_graycounter_07c.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/a_graycounter_07c.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 a_graycounter_07c " "Found entity 1: a_graycounter_07c" {  } { { "db/a_graycounter_07c.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/a_graycounter_07c.tdf" 24 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436019279 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019279 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "a_graycounter_07c TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|a_graycounter_07c:wrptr_g1p " "Elaborating entity \"a_graycounter_07c\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|a_graycounter_07c:wrptr_g1p\"" {  } { { "db/dcfifo_vve1.tdf" "wrptr_g1p" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/dcfifo_vve1.tdf" 48 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019281 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_ce41.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_ce41.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_ce41 " "Found entity 1: altsyncram_ce41" {  } { { "db/altsyncram_ce41.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/altsyncram_ce41.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436019310 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019310 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_ce41 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram " "Elaborating entity \"altsyncram_ce41\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\"" {  } { { "db/dcfifo_vve1.tdf" "fifo_ram" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/dcfifo_vve1.tdf" 49 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019311 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/alt_synch_pipe_qal.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/alt_synch_pipe_qal.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 alt_synch_pipe_qal " "Found entity 1: alt_synch_pipe_qal" {  } { { "db/alt_synch_pipe_qal.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/alt_synch_pipe_qal.tdf" 26 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436019324 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019324 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "alt_synch_pipe_qal TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_qal:rs_dgwp " "Elaborating entity \"alt_synch_pipe_qal\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_qal:rs_dgwp\"" {  } { { "db/dcfifo_vve1.tdf" "rs_dgwp" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/dcfifo_vve1.tdf" 56 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019326 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/dffpipe_b09.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/dffpipe_b09.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 dffpipe_b09 " "Found entity 1: dffpipe_b09" {  } { { "db/dffpipe_b09.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/dffpipe_b09.tdf" 24 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436019335 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019335 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "dffpipe_b09 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_qal:rs_dgwp\|dffpipe_b09:dffpipe12 " "Elaborating entity \"dffpipe_b09\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_qal:rs_dgwp\|dffpipe_b09:dffpipe12\"" {  } { { "db/alt_synch_pipe_qal.tdf" "dffpipe12" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/alt_synch_pipe_qal.tdf" 33 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019337 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/alt_synch_pipe_ral.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/alt_synch_pipe_ral.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 alt_synch_pipe_ral " "Found entity 1: alt_synch_pipe_ral" {  } { { "db/alt_synch_pipe_ral.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/alt_synch_pipe_ral.tdf" 26 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436019346 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019346 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "alt_synch_pipe_ral TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_ral:ws_dgrp " "Elaborating entity \"alt_synch_pipe_ral\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_ral:ws_dgrp\"" {  } { { "db/dcfifo_vve1.tdf" "ws_dgrp" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/dcfifo_vve1.tdf" 57 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019347 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/dffpipe_c09.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/dffpipe_c09.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 dffpipe_c09 " "Found entity 1: dffpipe_c09" {  } { { "db/dffpipe_c09.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/dffpipe_c09.tdf" 24 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436019358 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019358 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "dffpipe_c09 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_ral:ws_dgrp\|dffpipe_c09:dffpipe15 " "Elaborating entity \"dffpipe_c09\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_ral:ws_dgrp\|dffpipe_c09:dffpipe15\"" {  } { { "db/alt_synch_pipe_ral.tdf" "dffpipe15" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/alt_synch_pipe_ral.tdf" 33 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019359 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/cmpr_o76.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/cmpr_o76.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 cmpr_o76 " "Found entity 1: cmpr_o76" {  } { { "db/cmpr_o76.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/cmpr_o76.tdf" 22 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436019388 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019388 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "cmpr_o76 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|cmpr_o76:rdempty_eq_comp " "Elaborating entity \"cmpr_o76\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|cmpr_o76:rdempty_eq_comp\"" {  } { { "db/dcfifo_vve1.tdf" "rdempty_eq_comp" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/dcfifo_vve1.tdf" 58 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019389 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "AD_FREQ_WORD AD_FREQ_WORD:u_AD_FREQ_WORD " "Elaborating entity \"AD_FREQ_WORD\" for hierarchy \"AD_FREQ_WORD:u_AD_FREQ_WORD\"" {  } { { "TOP.bdf" "u_AD_FREQ_WORD" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 712 -216 16 888 "u_AD_FREQ_WORD" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019397 ""}
{ "Warning" "WVRFX_L2_VERI_INCOMPLETE_CASE_STATEMENT" "AD_FREQ_WORD.v(22) " "Verilog HDL Case Statement warning at AD_FREQ_WORD.v(22): incomplete case statement has no default case item" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 22 0 0 } }  } 0 10270 "Verilog HDL Case Statement warning at %1!s!: incomplete case statement has no default case item" 0 0 "Analysis & Synthesis" 0 -1 1721436019398 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_OUTH AD_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at AD_FREQ_WORD.v(21): inferring latch(es) for variable \"AD1_OUTH\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436019398 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_OUTL AD_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at AD_FREQ_WORD.v(21): inferring latch(es) for variable \"AD1_OUTL\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436019398 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_OUTH AD_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at AD_FREQ_WORD.v(21): inferring latch(es) for variable \"AD2_OUTH\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436019398 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_OUTL AD_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at AD_FREQ_WORD.v(21): inferring latch(es) for variable \"AD2_OUTL\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436019398 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[0\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[0\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[1\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[1\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[2\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[2\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[3\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[3\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[4\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[4\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[5\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[5\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[6\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[6\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[7\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[7\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[8\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[8\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[9\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[9\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[10\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[10\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[11\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[11\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[12\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[12\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[13\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[13\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[14\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[14\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[15\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTL\[15\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[0\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[0\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[1\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[1\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[2\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[2\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[3\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[3\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[4\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[4\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[5\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[5\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[6\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[6\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[7\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[7\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[8\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[8\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[9\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[9\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[10\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[10\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[11\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[11\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[12\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[12\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[13\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[13\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019399 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[14\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[14\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[15\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD2_OUTH\[15\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[0\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[0\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[1\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[1\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[2\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[2\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[3\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[3\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[4\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[4\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[5\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[5\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[6\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[6\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[7\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[7\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[8\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[8\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[9\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[9\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[10\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[10\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[11\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[11\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[12\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[12\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[13\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[13\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[14\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[14\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[15\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTL\[15\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[0\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[0\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[1\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[1\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[2\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[2\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[3\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[3\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[4\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[4\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[5\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[5\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[6\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[6\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[7\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[7\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[8\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[8\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[9\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[9\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[10\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[10\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[11\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[11\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[12\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[12\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[13\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[13\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[14\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[14\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[15\] AD_FREQ_WORD.v(21) " "Inferred latch for \"AD1_OUTH\[15\]\" at AD_FREQ_WORD.v(21)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/AD_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019400 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "DA_FREQ_WORD DA_FREQ_WORD:u_DA_FREQ_WORD " "Elaborating entity \"DA_FREQ_WORD\" for hierarchy \"DA_FREQ_WORD:u_DA_FREQ_WORD\"" {  } { { "TOP.bdf" "u_DA_FREQ_WORD" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 1136 2264 2496 1312 "u_DA_FREQ_WORD" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019589 ""}
{ "Warning" "WVRFX_L2_VERI_INCOMPLETE_CASE_STATEMENT" "DA_FREQ_WORD.v(22) " "Verilog HDL Case Statement warning at DA_FREQ_WORD.v(22): incomplete case statement has no default case item" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 22 0 0 } }  } 0 10270 "Verilog HDL Case Statement warning at %1!s!: incomplete case statement has no default case item" 0 0 "Analysis & Synthesis" 0 -1 1721436019589 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA1_OUTH DA_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at DA_FREQ_WORD.v(21): inferring latch(es) for variable \"DA1_OUTH\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436019589 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA1_OUTL DA_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at DA_FREQ_WORD.v(21): inferring latch(es) for variable \"DA1_OUTL\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436019589 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA2_OUTH DA_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at DA_FREQ_WORD.v(21): inferring latch(es) for variable \"DA2_OUTH\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436019589 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA2_OUTL DA_FREQ_WORD.v(21) " "Verilog HDL Always Construct warning at DA_FREQ_WORD.v(21): inferring latch(es) for variable \"DA2_OUTL\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1721436019589 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[0\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[0\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[1\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[1\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[2\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[2\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[3\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[3\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[4\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[4\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[5\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[5\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[6\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[6\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[7\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[7\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[8\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[8\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[9\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[9\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[10\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[10\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[11\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[11\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[12\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[12\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[13\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[13\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[14\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[14\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[15\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTL\[15\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[0\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[0\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[1\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[1\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[2\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[2\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[3\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[3\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[4\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[4\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[5\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[5\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[6\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[6\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[7\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[7\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[8\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[8\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[9\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[9\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[10\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[10\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[11\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[11\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[12\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[12\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[13\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[13\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019590 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[14\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[14\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[15\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA2_OUTH\[15\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[0\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[0\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[1\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[1\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[2\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[2\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[3\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[3\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[4\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[4\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[5\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[5\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[6\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[6\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[7\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[7\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[8\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[8\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[9\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[9\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[10\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[10\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[11\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[11\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[12\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[12\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[13\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[13\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[14\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[14\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[15\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTL\[15\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[0\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[0\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[1\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[1\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[2\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[2\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[3\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[3\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[4\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[4\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[5\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[5\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[6\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[6\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[7\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[7\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[8\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[8\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[9\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[9\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[10\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[10\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[11\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[11\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[12\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[12\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[13\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[13\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[14\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[14\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[15\] DA_FREQ_WORD.v(21) " "Inferred latch for \"DA1_OUTH\[15\]\" at DA_FREQ_WORD.v(21)" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/DA_FREQ_WORD.v" 21 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019591 "|TOP|DA_FREQ_WORD:u_DA_FREQ_WORD"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "SINROM SINROM:inst6 " "Elaborating entity \"SINROM\" for hierarchy \"SINROM:inst6\"" {  } { { "TOP.bdf" "inst6" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 808 3272 3488 936 "inst6" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019602 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram SINROM:inst6\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"SINROM:inst6\|altsyncram:altsyncram_component\"" {  } { { "../ip/SINROM.v" "altsyncram_component" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/ip/SINROM.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019634 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "SINROM:inst6\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"SINROM:inst6\|altsyncram:altsyncram_component\"" {  } { { "../ip/SINROM.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/ip/SINROM.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019641 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "SINROM:inst6\|altsyncram:altsyncram_component " "Instantiated megafunction \"SINROM:inst6\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019641 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019641 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019641 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file ../script/sin.mif " "Parameter \"init_file\" = \"../script/sin.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019641 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019641 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019641 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019641 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 1024 " "Parameter \"numwords_a\" = \"1024\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019641 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019641 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019641 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a UNREGISTERED " "Parameter \"outdata_reg_a\" = \"UNREGISTERED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019641 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 10 " "Parameter \"widthad_a\" = \"10\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019641 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019641 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1721436019641 ""}  } { { "../ip/SINROM.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/ip/SINROM.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1721436019641 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_eja1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_eja1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_eja1 " "Found entity 1: altsyncram_eja1" {  } { { "db/altsyncram_eja1.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/altsyncram_eja1.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436019671 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436019671 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_eja1 SINROM:inst6\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated " "Elaborating entity \"altsyncram_eja1\" for hierarchy \"SINROM:inst6\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "d:/intelfpga/18.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019672 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "CNT10 CNT10:u_DA1_CNT10 " "Elaborating entity \"CNT10\" for hierarchy \"CNT10:u_DA1_CNT10\"" {  } { { "TOP.bdf" "u_DA1_CNT10" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { 808 3032 3200 888 "u_DA1_CNT10" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436019706 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 10 CNT10.v(7) " "Verilog HDL assignment warning at CNT10.v(7): truncated value with size 32 to match size of target (10)" {  } { { "../src/CNT10.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/src/CNT10.v" 7 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1721436019707 "|TOP|CNT10:u_DA1_CNT10"}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_ca24.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_ca24.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_ca24 " "Found entity 1: altsyncram_ca24" {  } { { "db/altsyncram_ca24.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/altsyncram_ca24.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436021219 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436021219 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/mux_psc.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/mux_psc.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 mux_psc " "Found entity 1: mux_psc" {  } { { "db/mux_psc.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/mux_psc.tdf" 22 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436021337 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436021337 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/decode_dvf.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/decode_dvf.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 decode_dvf " "Found entity 1: decode_dvf" {  } { { "db/decode_dvf.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/decode_dvf.tdf" 22 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436021392 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436021392 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/cntr_egi.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/cntr_egi.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 cntr_egi " "Found entity 1: cntr_egi" {  } { { "db/cntr_egi.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/cntr_egi.tdf" 25 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436021471 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436021471 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/cntr_89j.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/cntr_89j.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 cntr_89j " "Found entity 1: cntr_89j" {  } { { "db/cntr_89j.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/cntr_89j.tdf" 25 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436021520 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436021520 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/cntr_cgi.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/cntr_cgi.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 cntr_cgi " "Found entity 1: cntr_cgi" {  } { { "db/cntr_cgi.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/cntr_cgi.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436021589 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436021589 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/cmpr_rgc.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/cmpr_rgc.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 cmpr_rgc " "Found entity 1: cmpr_rgc" {  } { { "db/cmpr_rgc.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/cmpr_rgc.tdf" 22 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436021622 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436021622 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/cntr_23j.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/cntr_23j.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 cntr_23j " "Found entity 1: cntr_23j" {  } { { "db/cntr_23j.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/cntr_23j.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436021675 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436021675 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/cmpr_ngc.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/cmpr_ngc.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 cmpr_ngc " "Found entity 1: cmpr_ngc" {  } { { "db/cmpr_ngc.tdf" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/cmpr_ngc.tdf" 22 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436021708 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436021708 ""}
{ "Info" "ISGN_AE_SUCCESSFUL" "auto_signaltap_0 " "Analysis and Synthesis generated Signal Tap or debug node instance \"auto_signaltap_0\"" {  } {  } 0 12033 "Analysis and Synthesis generated Signal Tap or debug node instance \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436022432 ""}
{ "Info" "ISCI_START_SUPER_FABRIC_GEN" "alt_sld_fab " "Starting IP generation for the debug fabric: alt_sld_fab." {  } {  } 0 11170 "Starting IP generation for the debug fabric: %1!s!." 0 0 "Analysis & Synthesis" 0 -1 1721436022522 ""}
{ "Info" "ISCI_EXT_PROC_INFO_MSG" "2024.07.20.08:40:24 Progress: Loading sld57ffc29f/alt_sld_fab_wrapper_hw.tcl " "2024.07.20.08:40:24 Progress: Loading sld57ffc29f/alt_sld_fab_wrapper_hw.tcl" {  } {  } 0 11172 "%1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436024930 ""}
{ "Info" "ISCI_EXT_PROC_INFO_MSG" "Alt_sld_fab.alt_sld_fab: SLD fabric agents which did not specify prefer_host were connected to JTAG " "Alt_sld_fab.alt_sld_fab: SLD fabric agents which did not specify prefer_host were connected to JTAG" {  } {  } 0 11172 "%1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436027035 ""}
{ "Info" "ISCI_EXT_PROC_INFO_MSG" "Alt_sld_fab: Generating alt_sld_fab \"alt_sld_fab\" for QUARTUS_SYNTH " "Alt_sld_fab: Generating alt_sld_fab \"alt_sld_fab\" for QUARTUS_SYNTH" {  } {  } 0 11172 "%1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436027156 ""}
{ "Info" "ISCI_EXT_PROC_INFO_MSG" "Alt_sld_fab: \"alt_sld_fab\" instantiated alt_sld_fab \"alt_sld_fab\" " "Alt_sld_fab: \"alt_sld_fab\" instantiated alt_sld_fab \"alt_sld_fab\"" {  } {  } 0 11172 "%1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436029134 ""}
{ "Info" "ISCI_EXT_PROC_INFO_MSG" "Presplit: \"alt_sld_fab\" instantiated altera_super_splitter \"presplit\" " "Presplit: \"alt_sld_fab\" instantiated altera_super_splitter \"presplit\"" {  } {  } 0 11172 "%1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436029213 ""}
{ "Info" "ISCI_EXT_PROC_INFO_MSG" "Splitter: \"alt_sld_fab\" instantiated altera_sld_splitter \"splitter\" " "Splitter: \"alt_sld_fab\" instantiated altera_sld_splitter \"splitter\"" {  } {  } 0 11172 "%1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436029294 ""}
{ "Info" "ISCI_EXT_PROC_INFO_MSG" "Sldfabric: \"alt_sld_fab\" instantiated altera_sld_jtag_hub \"sldfabric\" " "Sldfabric: \"alt_sld_fab\" instantiated altera_sld_jtag_hub \"sldfabric\"" {  } {  } 0 11172 "%1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436029384 ""}
{ "Info" "ISCI_EXT_PROC_INFO_MSG" "Ident: \"alt_sld_fab\" instantiated altera_connection_identification_hub \"ident\" " "Ident: \"alt_sld_fab\" instantiated altera_connection_identification_hub \"ident\"" {  } {  } 0 11172 "%1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436029388 ""}
{ "Info" "ISCI_EXT_PROC_INFO_MSG" "Alt_sld_fab: Done \"alt_sld_fab\" with 6 modules, 6 files " "Alt_sld_fab: Done \"alt_sld_fab\" with 6 modules, 6 files" {  } {  } 0 11172 "%1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436029388 ""}
{ "Info" "ISCI_END_SUPER_FABRIC_GEN" "alt_sld_fab " "Finished IP generation for the debug fabric: alt_sld_fab." {  } {  } 0 11171 "Finished IP generation for the debug fabric: %1!s!." 0 0 "Analysis & Synthesis" 0 -1 1721436030078 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/ip/sld57ffc29f/alt_sld_fab.v 1 1 " "Found 1 design units, including 1 entities, in source file db/ip/sld57ffc29f/alt_sld_fab.v" { { "Info" "ISGN_ENTITY_NAME" "1 alt_sld_fab " "Found entity 1: alt_sld_fab" {  } { { "db/ip/sld57ffc29f/alt_sld_fab.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/ip/sld57ffc29f/alt_sld_fab.v" 9 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436030260 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436030260 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab.v 1 1 " "Found 1 design units, including 1 entities, in source file db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab.v" { { "Info" "ISGN_ENTITY_NAME" "1 alt_sld_fab_alt_sld_fab " "Found entity 1: alt_sld_fab_alt_sld_fab" {  } { { "db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab.v" 9 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436030330 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436030330 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_ident.sv 1 1 " "Found 1 design units, including 1 entities, in source file db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_ident.sv" { { "Info" "ISGN_ENTITY_NAME" "1 alt_sld_fab_alt_sld_fab_ident " "Found entity 1: alt_sld_fab_alt_sld_fab_ident" {  } { { "db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_ident.sv" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_ident.sv" 33 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436030335 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436030335 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_presplit.sv 1 1 " "Found 1 design units, including 1 entities, in source file db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_presplit.sv" { { "Info" "ISGN_ENTITY_NAME" "1 alt_sld_fab_alt_sld_fab_presplit " "Found entity 1: alt_sld_fab_alt_sld_fab_presplit" {  } { { "db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_presplit.sv" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_presplit.sv" 3 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436030388 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436030388 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_sldfabric.vhd 2 1 " "Found 2 design units, including 1 entities, in source file db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_sldfabric.vhd" { { "Info" "ISGN_DESIGN_UNIT_NAME" "1 alt_sld_fab_alt_sld_fab_sldfabric-rtl " "Found design unit 1: alt_sld_fab_alt_sld_fab_sldfabric-rtl" {  } { { "db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_sldfabric.vhd" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_sldfabric.vhd" 102 -1 0 } }  } 0 12022 "Found design unit %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436030458 ""} { "Info" "ISGN_ENTITY_NAME" "1 alt_sld_fab_alt_sld_fab_sldfabric " "Found entity 1: alt_sld_fab_alt_sld_fab_sldfabric" {  } { { "db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_sldfabric.vhd" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_sldfabric.vhd" 11 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436030458 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436030458 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_splitter.sv 1 1 " "Found 1 design units, including 1 entities, in source file db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_splitter.sv" { { "Info" "ISGN_ENTITY_NAME" "1 alt_sld_fab_alt_sld_fab_splitter " "Found entity 1: alt_sld_fab_alt_sld_fab_splitter" {  } { { "db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_splitter.sv" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/ip/sld57ffc29f/submodules/alt_sld_fab_alt_sld_fab_splitter.sv" 3 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1721436030514 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436030514 ""}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING_ON_PARTITION" "Top " "Timing-Driven Synthesis is running on partition \"Top\"" {  } {  } 0 286031 "Timing-Driven Synthesis is running on partition \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436032467 ""}
{ "Info" "IMLS_MLS_PRESET_POWER_UP" "" "Registers with preset signals will power-up high" {  } { { "sld_buffer_manager.vhd" "" { Text "d:/intelfpga/18.1/quartus/libraries/megafunctions/sld_buffer_manager.vhd" 356 -1 0 } }  } 0 13000 "Registers with preset signals will power-up high" 0 0 "Analysis & Synthesis" 0 -1 1721436032987 ""}
{ "Info" "IMLS_MLS_DEV_CLRN_SETS_REGISTERS" "" "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" {  } {  } 0 13003 "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" 0 0 "Analysis & Synthesis" 0 -1 1721436032988 ""}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING_ON_PARTITION" "sld_signaltap:auto_signaltap_0 " "Timing-Driven Synthesis is running on partition \"sld_signaltap:auto_signaltap_0\"" {  } {  } 0 286031 "Timing-Driven Synthesis is running on partition \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436033155 ""}
{ "Info" "IMLS_MLS_PRESET_POWER_UP" "" "Registers with preset signals will power-up high" {  } { { "sld_jtag_hub.vhd" "" { Text "d:/intelfpga/18.1/quartus/libraries/megafunctions/sld_jtag_hub.vhd" 386 -1 0 } }  } 0 13000 "Registers with preset signals will power-up high" 0 0 "Analysis & Synthesis" 0 -1 1721436033574 ""}
{ "Info" "IMLS_MLS_DEV_CLRN_SETS_REGISTERS" "" "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" {  } {  } 0 13003 "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" 0 0 "Analysis & Synthesis" 0 -1 1721436033574 ""}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING_ON_PARTITION" "sld_hub:auto_hub " "Timing-Driven Synthesis is running on partition \"sld_hub:auto_hub\"" {  } {  } 0 286031 "Timing-Driven Synthesis is running on partition \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436033656 ""}
{ "Critical Warning" "WAMERGE_SLD_INSTANCE_WITH_INVALID_CONNECTIONS" "auto_signaltap_0 33 97 0 0 64 " "Partially connected in-system debug instance \"auto_signaltap_0\" to 33 of its 97 required data inputs, trigger inputs, acquisition clocks, and dynamic pins.  There were 0 illegal, 0 inaccessible, and 64 missing sources or connections." {  } {  } 1 35025 "Partially connected in-system debug instance \"%1!s!\" to %2!d! of its %3!d! required data inputs, trigger inputs, acquisition clocks, and dynamic pins.  There were %4!d! illegal, %5!d! inaccessible, and %6!d! missing sources or connections." 0 0 "Analysis & Synthesis" 0 -1 1721436035056 ""}
{ "Info" "IBPM_HARD_BLOCK_PARTITION_CREATED" "hard_block:auto_generated_inst " "Generating hard_block partition \"hard_block:auto_generated_inst\"" { { "Info" "IBPM_HARD_BLOCK_PARTITION_NODE" "1 0 1 0 0 " "Adding 1 node(s), including 0 DDIO, 1 PLL, 0 transceiver and 0 LCELL" {  } {  } 0 16011 "Adding %1!d! node(s), including %2!d! DDIO, %3!d! PLL, %4!d! transceiver and %5!d! LCELL" 0 0 "Design Software" 0 -1 1721436035078 ""}  } {  } 0 16010 "Generating hard_block partition \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1721436035078 ""}
{ "Warning" "WCUT_PLL_MULT_DIV_SPECIFIED_CLOCK_NOT_CONNECTED" "MYPLL:inst1\|altpll:altpll_component\|MYPLL_altpll:auto_generated\|pll1 CLK\[1\] clk1_multiply_by clk1_divide_by " "PLL \"MYPLL:inst1\|altpll:altpll_component\|MYPLL_altpll:auto_generated\|pll1\" has parameters clk1_multiply_by and clk1_divide_by specified but port CLK\[1\] is not connected" {  } { { "db/mypll_altpll.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/db/mypll_altpll.v" 43 -1 0 } } { "altpll.tdf" "" { Text "d:/intelfpga/18.1/quartus/libraries/megafunctions/altpll.tdf" 897 0 0 } } { "../ip/MYPLL.v" "" { Text "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/ip/MYPLL.v" 94 0 0 } } { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/EE_OBJECT/JYX_FPGA_OBJECT/prj/TOP.bdf" { { -152 264 544 40 "inst1" "" } } } }  } 0 15899 "PLL \"%1!s!\" has parameters %3!s! and %4!s! specified but port %2!s! is not connected" 0 0 "Analysis & Synthesis" 0 -1 1721436035154 ""}
{ "Info" "ICUT_CUT_TM_SUMMARY" "2551 " "Implemented 2551 device resources after synthesis - the final resource count might be different" { { "Info" "ICUT_CUT_TM_IPINS" "35 " "Implemented 35 input pins" {  } {  } 0 21058 "Implemented %1!d! input pins" 0 0 "Design Software" 0 -1 1721436035236 ""} { "Info" "ICUT_CUT_TM_OPINS" "33 " "Implemented 33 output pins" {  } {  } 0 21059 "Implemented %1!d! output pins" 0 0 "Design Software" 0 -1 1721436035236 ""} { "Info" "ICUT_CUT_TM_BIDIRS" "16 " "Implemented 16 bidirectional pins" {  } {  } 0 21060 "Implemented %1!d! bidirectional pins" 0 0 "Design Software" 0 -1 1721436035236 ""} { "Info" "ICUT_CUT_TM_LCELLS" "2381 " "Implemented 2381 logic cells" {  } {  } 0 21061 "Implemented %1!d! logic cells" 0 0 "Design Software" 0 -1 1721436035236 ""} { "Info" "ICUT_CUT_TM_RAMS" "84 " "Implemented 84 RAM segments" {  } {  } 0 21064 "Implemented %1!d! RAM segments" 0 0 "Design Software" 0 -1 1721436035236 ""} { "Info" "ICUT_CUT_TM_PLLS" "1 " "Implemented 1 PLLs" {  } {  } 0 21065 "Implemented %1!d! PLLs" 0 0 "Design Software" 0 -1 1721436035236 ""}  } {  } 0 21057 "Implemented %1!d! device resources after synthesis - the final resource count might be different" 0 0 "Analysis & Synthesis" 0 -1 1721436035236 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Analysis & Synthesis 0 s 32 s Quartus Prime " "Quartus Prime Analysis & Synthesis was successful. 0 errors, 32 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4923 " "Peak virtual memory: 4923 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1721436035260 ""} { "Info" "IQEXE_END_BANNER_TIME" "Sat Jul 20 08:40:35 2024 " "Processing ended: Sat Jul 20 08:40:35 2024" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1721436035260 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:22 " "Elapsed time: 00:00:22" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1721436035260 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:14 " "Total CPU time (on all processors): 00:00:14" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1721436035260 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Analysis & Synthesis" 0 -1 1721436035260 ""}
