{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1752772097683 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Timing Analyzer Quartus Prime " "Running Quartus Prime Timing Analyzer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Standard Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Standard Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1752772097686 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Jul 18 01:08:17 2025 " "Processing started: Fri Jul 18 01:08:17 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1752772097686 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Timing Analyzer" 0 -1 1752772097686 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_sta JYX_FPGA_OBJECT -c JYX_FPGA_OBJECT " "Command: quartus_sta JYX_FPGA_OBJECT -c JYX_FPGA_OBJECT" {  } {  } 0 0 "Command: %1!s!" 0 0 "Timing Analyzer" 0 -1 1752772097686 ""}
{ "Info" "0" "" "qsta_default_script.tcl version: #1" {  } {  } 0 0 "qsta_default_script.tcl version: #1" 0 0 "Timing Analyzer" 0 0 1752772097935 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "16 16 " "Parallel compilation is enabled and will use 16 of the 16 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Timing Analyzer" 0 -1 1752772098075 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772098109 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772098109 ""}
{ "Warning" "WTDB_ANALYZE_COMB_LATCHES" "226 " "The Timing Analyzer is analyzing 226 combinational loops as latches. For more details, run the Check Timing command in the Timing Analyzer or view the \"User-Specified and Inferred Latches\" table in the Analysis & Synthesis report." {  } {  } 0 335093 "The Timing Analyzer is analyzing %1!d! combinational loops as latches. For more details, run the Check Timing command in the Timing Analyzer or view the \"User-Specified and Inferred Latches\" table in the Analysis & Synthesis report." 0 0 "Timing Analyzer" 0 -1 1752772098247 ""}
{ "Info" "ISTA_SDC_STATEMENT_PARENT" "" "Evaluating HDL-embedded SDC commands" { { "Info" "ISTA_SDC_STATEMENT_ENTITY" "dcfifo_vve1 " "Entity dcfifo_vve1" { { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_false_path -from *rdptr_g* -to *ws_dgrp\|dffpipe_c09:dffpipe15\|dffe16a*  " "set_false_path -from *rdptr_g* -to *ws_dgrp\|dffpipe_c09:dffpipe15\|dffe16a* " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1752772098298 ""} { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_false_path -from *delayed_wrptr_g* -to *rs_dgwp\|dffpipe_b09:dffpipe12\|dffe13a*  " "set_false_path -from *delayed_wrptr_g* -to *rs_dgwp\|dffpipe_b09:dffpipe12\|dffe13a* " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1752772098298 ""}  } {  } 0 332165 "Entity %1!s!" 0 0 "Design Software" 0 -1 1752772098298 ""}  } {  } 0 332164 "Evaluating HDL-embedded SDC commands" 0 0 "Timing Analyzer" 0 -1 1752772098298 ""}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "JYX_FPGA_OBJECT.sdc " "Synopsys Design Constraints File file not found: 'JYX_FPGA_OBJECT.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 332012 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "Timing Analyzer" 0 -1 1752772098305 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_DERIVING" "generated clocks \"derive_pll_clocks -create_base_clocks\" " "No user constrained generated clocks found in the design. Calling \"derive_pll_clocks -create_base_clocks\"" {  } {  } 0 332142 "No user constrained %1!s! found in the design. Calling %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772098305 ""}
{ "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "Deriving PLL clocks " "Deriving PLL clocks" { { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_clock -period 20.000 -waveform \{0.000 10.000\} -name CLK CLK " "create_clock -period 20.000 -waveform \{0.000 10.000\} -name CLK CLK" {  } {  } 0 332110 "%1!s!" 0 0 "Design Software" 0 -1 1752772098305 ""} { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_generated_clock -source \{inst6\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -multiply_by 3 -duty_cycle 50.00 -name \{inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} " "create_generated_clock -source \{inst6\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -multiply_by 3 -duty_cycle 50.00 -name \{inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]\}" {  } {  } 0 332110 "%1!s!" 0 0 "Design Software" 0 -1 1752772098305 ""}  } {  } 0 332110 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752772098305 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_DERIVING" "base clocks \"derive_clocks -period 1.0\" " "No user constrained base clocks found in the design. Calling \"derive_clocks -period 1.0\"" {  } {  } 0 332142 "No user constrained %1!s! found in the design. Calling %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772098305 ""}
{ "Info" "ISTA_DERIVE_CLOCKS_INFO" "Deriving Clocks " "Deriving Clocks" { { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " "create_clock -period 1.000 -name DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1752772098307 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name FPGA_CS_NEL FPGA_CS_NEL " "create_clock -period 1.000 -name FPGA_CS_NEL FPGA_CS_NEL" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1752772098307 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name AD1_INPUT_CLK AD1_INPUT_CLK " "create_clock -period 1.000 -name AD1_INPUT_CLK AD1_INPUT_CLK" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1752772098307 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name FREQ_DEV:u_AD1_DEV\|FREQ_OUT FREQ_DEV:u_AD1_DEV\|FREQ_OUT " "create_clock -period 1.000 -name FREQ_DEV:u_AD1_DEV\|FREQ_OUT FREQ_DEV:u_AD1_DEV\|FREQ_OUT" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1752772098307 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name FREQ_DEV:u_AD2_DEV\|FREQ_OUT FREQ_DEV:u_AD2_DEV\|FREQ_OUT " "create_clock -period 1.000 -name FREQ_DEV:u_AD2_DEV\|FREQ_OUT FREQ_DEV:u_AD2_DEV\|FREQ_OUT" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1752772098307 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name gate_generator:inst22\|gate gate_generator:inst22\|gate " "create_clock -period 1.000 -name gate_generator:inst22\|gate gate_generator:inst22\|gate" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1752772098307 ""}  } {  } 0 332105 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752772098307 ""}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 332143 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "Timing Analyzer" 0 -1 1752772098318 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752772098319 ""}
{ "Info" "0" "" "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" {  } {  } 0 0 "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" 0 0 "Timing Analyzer" 0 0 1752772098320 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 85C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 85C Model" 0 0 "Timing Analyzer" 0 0 1752772098327 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1752772098430 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1752772098430 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -95.388 " "Worst-case setup slack is -95.388" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098431 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098431 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "  -95.388           -3130.013 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "  -95.388           -3130.013 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098431 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "  -10.658           -4130.889 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "  -10.658           -4130.889 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098431 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -7.626           -1177.368 FPGA_CS_NEL  " "   -7.626           -1177.368 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098431 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.718            -181.500 gate_generator:inst22\|gate  " "   -4.718            -181.500 gate_generator:inst22\|gate " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098431 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.638            -148.747 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -4.638            -148.747 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098431 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.211            -144.821 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -4.211            -144.821 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098431 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.832             -69.868 AD1_INPUT_CLK  " "   -3.832             -69.868 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098431 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772098431 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold -4.765 " "Worst-case hold slack is -4.765" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098441 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098441 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.765            -211.082 FPGA_CS_NEL  " "   -4.765            -211.082 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098441 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.430               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.430               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098441 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.432               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "    0.432               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098441 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.433               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "    0.433               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098441 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.589               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "    0.589               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098441 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.603               0.000 AD1_INPUT_CLK  " "    0.603               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098441 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.844               0.000 gate_generator:inst22\|gate  " "    0.844               0.000 gate_generator:inst22\|gate " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098441 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772098441 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "recovery -5.848 " "Worst-case recovery slack is -5.848" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098442 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098442 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -5.848            -180.601 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   -5.848            -180.601 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098442 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.387             -44.202 AD1_INPUT_CLK  " "   -1.387             -44.202 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098442 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772098442 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "removal 1.580 " "Worst-case removal slack is 1.580" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098444 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098444 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.580               0.000 AD1_INPUT_CLK  " "    1.580               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098444 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    4.350               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    4.350               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098444 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772098444 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -4.000 " "Worst-case minimum pulse width slack is -4.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098445 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098445 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.000            -341.202 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -4.000            -341.202 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098445 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -307.278 FPGA_CS_NEL  " "   -3.201            -307.278 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098445 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -106.939 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -3.201            -106.939 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098445 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -106.939 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -3.201            -106.939 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098445 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -53.558 AD1_INPUT_CLK  " "   -3.000             -53.558 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098445 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.487             -95.168 gate_generator:inst22\|gate  " "   -1.487             -95.168 gate_generator:inst22\|gate " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098445 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    3.020               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    3.020               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098445 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.934               0.000 CLK  " "    9.934               0.000 CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772098445 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772098445 ""}
{ "Info" "ISTA_REPORT_METASTABILITY_INFO" "Report Metastability: Found 70 synchronizer chains. " "Report Metastability: Found 70 synchronizer chains." { { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Design MTBF is not calculated because the design doesn't meet its timing requirements. " "Design MTBF is not calculated because the design doesn't meet its timing requirements." {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1752772098945 ""}  } {  } 0 332114 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752772098945 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 0C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 0C Model" 0 0 "Timing Analyzer" 0 0 1752772098950 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Timing Analyzer" 0 -1 1752772098965 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Timing Analyzer" 0 -1 1752772099317 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752772099431 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1752772099454 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1752772099454 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -87.011 " "Worst-case setup slack is -87.011" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099456 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099456 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "  -87.011           -2873.074 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "  -87.011           -2873.074 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099456 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "  -10.020           -3835.288 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "  -10.020           -3835.288 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099456 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -7.600           -1156.397 FPGA_CS_NEL  " "   -7.600           -1156.397 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099456 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.842            -189.668 gate_generator:inst22\|gate  " "   -4.842            -189.668 gate_generator:inst22\|gate " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099456 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.364            -138.841 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -4.364            -138.841 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099456 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.925            -134.615 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -3.925            -134.615 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099456 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.777             -59.613 AD1_INPUT_CLK  " "   -3.777             -59.613 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099456 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772099456 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold -4.276 " "Worst-case hold slack is -4.276" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099469 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099469 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.276            -190.094 FPGA_CS_NEL  " "   -4.276            -190.094 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099469 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.380               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.380               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099469 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.382               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "    0.382               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099469 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.382               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "    0.382               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099469 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.515               0.000 AD1_INPUT_CLK  " "    0.515               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099469 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.517               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "    0.517               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099469 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.009               0.000 gate_generator:inst22\|gate  " "    1.009               0.000 gate_generator:inst22\|gate " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099469 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772099469 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "recovery -5.386 " "Worst-case recovery slack is -5.386" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099472 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099472 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -5.386            -165.955 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   -5.386            -165.955 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099472 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.207             -38.506 AD1_INPUT_CLK  " "   -1.207             -38.506 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099472 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772099472 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "removal 1.352 " "Worst-case removal slack is 1.352" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099475 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099475 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.352               0.000 AD1_INPUT_CLK  " "    1.352               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099475 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    3.935               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    3.935               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099475 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772099475 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -4.000 " "Worst-case minimum pulse width slack is -4.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099478 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099478 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.000            -341.223 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -4.000            -341.223 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099478 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -297.614 FPGA_CS_NEL  " "   -3.201            -297.614 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099478 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -106.939 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -3.201            -106.939 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099478 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -106.939 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -3.201            -106.939 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099478 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -53.584 AD1_INPUT_CLK  " "   -3.000             -53.584 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099478 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.487             -95.168 gate_generator:inst22\|gate  " "   -1.487             -95.168 gate_generator:inst22\|gate " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099478 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.996               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    2.996               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099478 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.943               0.000 CLK  " "    9.943               0.000 CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772099478 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772099478 ""}
{ "Info" "ISTA_REPORT_METASTABILITY_INFO" "Report Metastability: Found 70 synchronizer chains. " "Report Metastability: Found 70 synchronizer chains." { { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Design MTBF is not calculated because the design doesn't meet its timing requirements. " "Design MTBF is not calculated because the design doesn't meet its timing requirements." {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1752772100025 ""}  } {  } 0 332114 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752772100025 ""}
{ "Info" "0" "" "Analyzing Fast 1200mV 0C Model" {  } {  } 0 0 "Analyzing Fast 1200mV 0C Model" 0 0 "Timing Analyzer" 0 0 1752772100031 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752772100138 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1752772100148 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1752772100148 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -41.203 " "Worst-case setup slack is -41.203" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100152 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100152 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "  -41.203           -1287.874 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "  -41.203           -1287.874 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100152 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.692           -1856.891 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   -4.692           -1856.891 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100152 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.700            -499.699 FPGA_CS_NEL  " "   -3.700            -499.699 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100152 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.132             -64.821 gate_generator:inst22\|gate  " "   -2.132             -64.821 gate_generator:inst22\|gate " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100152 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.986             -20.260 AD1_INPUT_CLK  " "   -1.986             -20.260 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100152 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.438             -39.457 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -1.438             -39.457 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100152 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.282             -36.599 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -1.282             -36.599 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100152 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772100152 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold -2.387 " "Worst-case hold slack is -2.387" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100167 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100167 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.387            -104.335 FPGA_CS_NEL  " "   -2.387            -104.335 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100167 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.132               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "    0.132               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100167 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.167               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "    0.167               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100167 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.177               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.177               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100167 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.178               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "    0.178               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100167 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.253               0.000 AD1_INPUT_CLK  " "    0.253               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100167 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.409               0.000 gate_generator:inst22\|gate  " "    0.409               0.000 gate_generator:inst22\|gate " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100167 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772100167 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "recovery -2.675 " "Worst-case recovery slack is -2.675" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100172 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100172 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.675             -82.326 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   -2.675             -82.326 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100172 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.051              -1.296 AD1_INPUT_CLK  " "   -0.051              -1.296 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100172 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772100172 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "removal 0.630 " "Worst-case removal slack is 0.630" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100178 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100178 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.630               0.000 AD1_INPUT_CLK  " "    0.630               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100178 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.861               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    1.861               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100178 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772100178 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100183 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100183 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000            -165.414 FPGA_CS_NEL  " "   -3.000            -165.414 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100183 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -44.132 AD1_INPUT_CLK  " "   -3.000             -44.132 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100183 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.000            -166.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -1.000            -166.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100183 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.000             -65.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -1.000             -65.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100183 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.000             -65.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -1.000             -65.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100183 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.000             -64.000 gate_generator:inst22\|gate  " "   -1.000             -64.000 gate_generator:inst22\|gate " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100183 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    3.100               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    3.100               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100183 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.594               0.000 CLK  " "    9.594               0.000 CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752772100183 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752772100183 ""}
{ "Info" "ISTA_REPORT_METASTABILITY_INFO" "Report Metastability: Found 70 synchronizer chains. " "Report Metastability: Found 70 synchronizer chains." { { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Design MTBF is not calculated because the design doesn't meet its timing requirements. " "Design MTBF is not calculated because the design doesn't meet its timing requirements." {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1752772100774 ""}  } {  } 0 332114 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752772100774 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "setup " "Design is not fully constrained for setup requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1752772101012 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "hold " "Design is not fully constrained for hold requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1752772101013 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Timing Analyzer 0 s 5 s Quartus Prime " "Quartus Prime Timing Analyzer was successful. 0 errors, 5 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4947 " "Peak virtual memory: 4947 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752772101094 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Jul 18 01:08:21 2025 " "Processing ended: Fri Jul 18 01:08:21 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752772101094 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:04 " "Elapsed time: 00:00:04" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752772101094 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:04 " "Total CPU time (on all processors): 00:00:04" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752772101094 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Timing Analyzer" 0 -1 1752772101094 ""}
