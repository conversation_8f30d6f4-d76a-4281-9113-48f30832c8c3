//&----------------------------------------------------------------------------------------
//& 模块名: FREQ_DEV
//& 文件名: FREQ_DEV.v
//& 作  者: 左岚
//& 日  期: 2025-07-18
//&
//& 功  能: 频率发生器模块。本模块实现了一个标准的32位数字控制振荡器(NCO)，
//&         也称为直接数字频率合成器(DDS)。它根据一个32位的频率控制字，
//&         生成一个指定频率的方波信号。
//&         输出频率计算公式: F_out = (F_clk * FREQ_WORD) / 2^32
//&----------------------------------------------------------------------------------------

module FREQ_DEV (
    // --- 端口定义 ---
    input             CLK,      // 系统主时钟/参考时钟
    input             EN,       // NCO使能信号，高电平有效
    input      [15:0] FREQH_W,  // 频率控制字的高16位
    input      [15:0] FREQL_W,  // 频率控制字的低16位
    output reg        FREQ_OUT  // NCO生成的方波输出
);

  // --- 内部信号定义 ---
  reg [31:0] FREQ_WORD;  // 用于存储拼接后的32位频率控制字
  reg [31:0] ACC = 32'd0;  // 32位的相位累加器，初始值为0

  // --- NCO核心逻辑：相位累加器 ---
  // 在每个时钟上升沿执行
  always @(posedge CLK) begin
    // 仅当使能信号EN为高时，累加器才工作
    if (EN) begin
      // 累加器在每个周期增加一个频率控制字的值（相位步进）
      ACC <= ACC + FREQ_WORD;
    end
    // 将累加器的最高位(MSB)作为方波输出。当累加器值超过一半时，
    // MSB会翻转，从而产生一个占空比为50%的周期信号。
    FREQ_OUT <= ACC[31];
  end

  // --- 输入频率控制字寄存器 ---
  // 在每个时钟上升沿，将输入的16位高低位数据拼接成32位，
  // 并存入FREQ_WORD寄存器。这可以确保频率控制字与内部逻辑同步，防止亚稳态。
  always @(posedge CLK) begin
    FREQ_WORD <= {FREQH_W, FREQL_W};
  end

endmodule
