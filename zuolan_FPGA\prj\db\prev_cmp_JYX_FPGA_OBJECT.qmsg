{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1752766362040 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus Prime " "Running Quartus Prime Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Standard Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Standard Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1752766362043 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 17 23:32:41 2025 " "Processing started: Thu Jul 17 23:32:41 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1752766362043 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766362043 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off JYX_FPGA_OBJECT -c JYX_FPGA_OBJECT " "Command: quartus_map --read_settings_files=on --write_settings_files=off JYX_FPGA_OBJECT -c JYX_FPGA_OBJECT" {  } {  } 0 0 "Command: %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766362044 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "16 16 " "Parallel compilation is enabled and will use 16 of the 16 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Analysis & Synthesis" 0 -1 1752766362437 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/ad_freq_measure.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/ad_freq_measure.v" { { "Info" "ISGN_ENTITY_NAME" "1 AD_FREQ_MEASURE " "Found entity 1: AD_FREQ_MEASURE" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368856 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368856 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/cnt32.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/cnt32.v" { { "Info" "ISGN_ENTITY_NAME" "1 CNT32 " "Found entity 1: CNT32" {  } { { "../src/CNT32.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT32.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368857 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368857 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/ad_data_deal.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/ad_data_deal.v" { { "Info" "ISGN_ENTITY_NAME" "1 AD_DATA_DEAL " "Found entity 1: AD_DATA_DEAL" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_DATA_DEAL.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368858 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368858 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/ad_freq_word.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/ad_freq_word.v" { { "Info" "ISGN_ENTITY_NAME" "1 AD_FREQ_WORD " "Found entity 1: AD_FREQ_WORD" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368860 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368860 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_freq_word.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_freq_word.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_FREQ_WORD " "Found entity 1: DA_FREQ_WORD" {  } { { "../src/DA_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_FREQ_WORD.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368861 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368861 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/cnt10.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/cnt10.v" { { "Info" "ISGN_ENTITY_NAME" "1 CNT10 " "Found entity 1: CNT10" {  } { { "../src/CNT10.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT10.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368863 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368863 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/freq_dev.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/freq_dev.v" { { "Info" "ISGN_ENTITY_NAME" "1 FREQ_DEV " "Found entity 1: FREQ_DEV" {  } { { "../src/FREQ_DEV.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FREQ_DEV.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368864 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368864 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/master_ctrl.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/master_ctrl.v" { { "Info" "ISGN_ENTITY_NAME" "1 MASTER_CTRL " "Found entity 1: MASTER_CTRL" {  } { { "../src/MASTER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/MASTER_CTRL.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368864 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368864 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/fmc/fmc_control.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/fmc/fmc_control.v" { { "Info" "ISGN_ENTITY_NAME" "1 fmc_control " "Found entity 1: fmc_control" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 2 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368866 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368866 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/test.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/test.v" { { "Info" "ISGN_ENTITY_NAME" "1 test " "Found entity 1: test" {  } { { "../src/test.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/test.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368867 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368867 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "top.bdf 1 1 " "Found 1 design units, including 1 entities, in source file top.bdf" { { "Info" "ISGN_ENTITY_NAME" "1 TOP " "Found entity 1: TOP" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { } } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368868 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368868 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/mypll.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/mypll.v" { { "Info" "ISGN_ENTITY_NAME" "1 MYPLL " "Found entity 1: MYPLL" {  } { { "../ip/MYPLL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/MYPLL.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368869 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368869 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sinrom.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sinrom.v" { { "Info" "ISGN_ENTITY_NAME" "1 SINROM " "Found entity 1: SINROM" {  } { { "../ip/SINROM.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/SINROM.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368870 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368870 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/tyfifo.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/tyfifo.v" { { "Info" "ISGN_ENTITY_NAME" "1 TYFIFO " "Found entity 1: TYFIFO" {  } { { "../ip/TYFIFO.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/TYFIFO.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368872 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368872 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sinromvpp/sinromvpp.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sinromvpp/sinromvpp.v" { { "Info" "ISGN_ENTITY_NAME" "1 sinromvpp " "Found entity 1: sinromvpp" {  } { { "../ip/sinromvpp/sinromvpp.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sinromvpp/sinromvpp.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368872 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368872 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/voltage_scaler_clocked.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/voltage_scaler_clocked.v" { { "Info" "ISGN_ENTITY_NAME" "1 voltage_scaler_clocked " "Found entity 1: voltage_scaler_clocked" {  } { { "../src/voltage_scaler_clocked.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/voltage_scaler_clocked.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368873 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368873 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_apmplitude.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_apmplitude.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_APMPLITUDE " "Found entity 1: DA_APMPLITUDE" {  } { { "../src/DA_APMPLITUDE.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_APMPLITUDE.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368874 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368874 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/cnt10_b.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/cnt10_b.v" { { "Info" "ISGN_ENTITY_NAME" "1 CNT10_B " "Found entity 1: CNT10_B" {  } { { "../src/CNT10_B.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT10_B.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368875 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368875 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/triangle_rom/triangle_rom.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/triangle_rom/triangle_rom.v" { { "Info" "ISGN_ENTITY_NAME" "1 triangle_rom " "Found entity 1: triangle_rom" {  } { { "../ip/triangle_rom/triangle_rom.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/triangle_rom/triangle_rom.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368877 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368877 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sqaure_rom/sqaure_rom.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sqaure_rom/sqaure_rom.v" { { "Info" "ISGN_ENTITY_NAME" "1 sqaure_rom " "Found entity 1: sqaure_rom" {  } { { "../ip/sqaure_rom/sqaure_rom.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sqaure_rom/sqaure_rom.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368878 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368878 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sawtooth_rom/sawtooth_rom.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/ip/sawtooth_rom/sawtooth_rom.v" { { "Info" "ISGN_ENTITY_NAME" "1 sawtooth_rom " "Found entity 1: sawtooth_rom" {  } { { "../ip/sawtooth_rom/sawtooth_rom.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/ip/sawtooth_rom/sawtooth_rom.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368879 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368879 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_waveform_a.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_waveform_a.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_WAVEFORM_A " "Found entity 1: DA_WAVEFORM_A" {  } { { "../src/DA_WAVEFORM_A.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_A.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368881 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368881 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_waveform_b.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_waveform_b.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_WAVEFORM_B " "Found entity 1: DA_WAVEFORM_B" {  } { { "../src/DA_WAVEFORM_B.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_WAVEFORM_B.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368882 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368882 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/gate_generator.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/gate_generator.v" { { "Info" "ISGN_ENTITY_NAME" "1 gate_generator " "Found entity 1: gate_generator" {  } { { "../src/gate_generator.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/gate_generator.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368883 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368883 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_clk_ctrl.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_clk_ctrl.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_CLK_CTRL " "Found entity 1: DA_CLK_CTRL" {  } { { "../src/DA_CLK_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_CLK_CTRL.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368884 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368884 ""}
{ "Warning" "WVRFX_L3_VERI_MIXED_BLOCKING_NONBLOCKING_ASSIGNMENT" "DA_PARAMETER_CTRL.v(69) " "Verilog HDL information at DA_PARAMETER_CTRL.v(69): always construct contains both blocking and non-blocking assignments" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 69 0 0 } }  } 0 10268 "Verilog HDL information at %1!s!: always construct contains both blocking and non-blocking assignments" 1 0 "Analysis & Synthesis" 0 -1 1752766368885 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_parameter_ctrl.v 1 1 " "Found 1 design units, including 1 entities, in source file /competition/ee/electronic/ee_object/code/zuolan_signal_fpga_stm32/zuolan_fpga/src/da_parameter_ctrl.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_PARAMETER_CTRL " "Found entity 1: DA_PARAMETER_CTRL" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752766368885 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368885 ""}
{ "Info" "ISGN_START_ELABORATION_TOP" "TOP " "Elaborating entity \"TOP\" for the top level hierarchy" {  } {  } 0 12127 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "Analysis & Synthesis" 0 -1 1752766368922 ""}
{ "Error" "EGDFX_INCORRECT_CONNECTOR_STYLE_CONDUIT" "" "Can't use conduits in network without blocks" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { -120 1280 1280 768 "" "" } { 768 1280 2832 768 "" "" } { -120 1280 2832 -120 "" "" } { -120 2832 2832 768 "" "" } } } }  } 0 275031 "Can't use conduits in network without blocks" 0 0 "Analysis & Synthesis" 0 -1 1752766368929 ""}
{ "Error" "EGDFX_INCORRECT_CONNECTOR_STYLE_CONDUIT" "" "Can't use conduits in network without blocks" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 896 3072 5672 896 "" "" } { -96 3072 3072 896 "" "" } { -96 5672 5672 896 "" "" } { -96 3072 5672 -96 "" "" } } } }  } 0 275031 "Can't use conduits in network without blocks" 0 0 "Analysis & Synthesis" 0 -1 1752766368929 ""}
{ "Error" "EGDFX_INCORRECT_CONNECTOR_STYLE_CONDUIT" "" "Can't use conduits in network without blocks" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1080 5160 5160 2160 "" "" } { 1080 3392 5160 1080 "" "" } { 2160 3392 5160 2160 "" "" } { 1080 3392 3392 2160 "" "" } } } }  } 0 275031 "Can't use conduits in network without blocks" 0 0 "Analysis & Synthesis" 0 -1 1752766368929 ""}
{ "Error" "EGDFX_INCORRECT_CONNECTOR_STYLE_CONDUIT" "" "Can't use conduits in network without blocks" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1056 1248 1248 2168 "" "" } { 1056 1248 3032 1056 "" "" } { 1056 3032 3032 2168 "" "" } { 2168 1248 3032 2168 "" "" } } } }  } 0 275031 "Can't use conduits in network without blocks" 0 0 "Analysis & Synthesis" 0 -1 1752766368929 ""}
{ "Error" "ESGN_TOP_HIER_ELABORATION_FAILURE" "" "Can't elaborate top-level user hierarchy" {  } {  } 0 12153 "Can't elaborate top-level user hierarchy" 0 0 "Analysis & Synthesis" 0 -1 1752766368930 ""}
{ "Error" "EQEXE_ERROR_COUNT" "Analysis & Synthesis 5 s 0 s Quartus Prime " "Quartus Prime Analysis & Synthesis was unsuccessful. 5 errors, 0 warnings" { { "Error" "EQEXE_END_PEAK_VSIZE_MEMORY" "4720 " "Peak virtual memory: 4720 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752766368977 ""} { "Error" "EQEXE_END_BANNER_TIME" "Thu Jul 17 23:32:48 2025 " "Processing ended: Thu Jul 17 23:32:48 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752766368977 ""} { "Error" "EQEXE_ELAPSED_TIME" "00:00:07 " "Elapsed time: 00:00:07" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752766368977 ""} { "Error" "EQEXE_ELAPSED_CPU_TIME" "00:00:13 " "Total CPU time (on all processors): 00:00:13" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752766368977 ""}  } {  } 0 0 "%6!s! %1!s! was unsuccessful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766368977 ""}
{ "Error" "EFLOW_ERROR_COUNT" "Full Compilation 7 s 0 s " "Quartus Prime Full Compilation was unsuccessful. 7 errors, 0 warnings" {  } {  } 0 293001 "Quartus Prime %1!s! was unsuccessful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Analysis & Synthesis" 0 -1 1752766369549 ""}
