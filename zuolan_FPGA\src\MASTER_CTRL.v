//&----------------------------------------------------------------------------------------
//& 模块名: MASTER_CTRL
//& 文件名: MASTER_CTRL.v
//& 作  者: 左岚
//& 日  期: 2025年7月18日
//&
//& 功  能: 主控寄存器模块。该模块实现了一个16位的写操作寄存器，
//&         用于接收来自总线的主控制字。
//&
//& 设计警告:
//& **严重警告 - 锁存器(Latch)推断**:
//& 本模块的 `always` 块对电平敏感 (例如 `CS`, `WR_EN`) 而非时钟边沿，
//& 并且在条件不满足时没有为 `CTRL_DATA` 提供默认赋值。
//& 这种编码风格会导致综合工具推断出一个锁存器来保存数据，而不是一个
//& 时钟同步的触发器 (Flip-Flop)。
//& 锁存器在FPGA/ASIC设计中通常是需要避免的，因为它们对输入信号的毛刺
//& 敏感，会使静态时序分析变得非常困难。
//& **推荐的修改方法**: 使用同步时序逻辑 `always @(posedge clk)` 来实现寄存器。
//&----------------------------------------------------------------------------------------

module MASTER_CTRL #(
    // --- 参数定义 ---
    parameter ADDR1 = 16'h0001  // 主控寄存器的总线地址
) (
    // --- 端口定义 ---
    input             CS,        // 片选信号，低电平有效
    input             WR_EN,     // 写使能信号，高电平有效
    input      [15:0] ADDR,      // 16位地址总线
    input      [15:0] DATA,      // 16位数据总线
    output reg [15:0] CTRL_DATA  // 16位控制寄存器的输出 (将作为锁存器实现)
);

  // --- 锁存器实现逻辑 ---
  // **警告**: 此 always 块描述了一个锁存器。
  // 当敏感列表中的任何信号(CS, WR_EN, ADDR)发生电平变化时，此块被触发。
  always @(CS or WR_EN or ADDR) begin
    // 当片选有效、写使能有效且地址匹配时，锁存器是“透明”的，
    // 即输入DATA会直接传递给输出CTRL_DATA。
    if (!CS && WR_EN && (ADDR == ADDR1)) begin
      CTRL_DATA <= DATA;
    end
    // 当上述条件不满足时，没有指定CTRL_DATA的值，
    // 因此它会“锁存”并保持之前的值。
  end

endmodule
