{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1721436044566 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Timing Analyzer Quartus Prime " "Running Quartus Prime Timing Analyzer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Standard Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Standard Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1721436044570 ""} { "Info" "IQEXE_START_BANNER_TIME" "Sat Jul 20 08:40:44 2024 " "Processing started: Sat Jul 20 08:40:44 2024" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1721436044570 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Timing Analyzer" 0 -1 1721436044570 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_sta JYX_FPGA_OBJECT -c FMC " "Command: quartus_sta JYX_FPGA_OBJECT -c FMC" {  } {  } 0 0 "Command: %1!s!" 0 0 "Timing Analyzer" 0 -1 1721436044570 ""}
{ "Info" "0" "" "qsta_default_script.tcl version: #1" {  } {  } 0 0 "qsta_default_script.tcl version: #1" 0 0 "Timing Analyzer" 0 0 1721436044613 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "12 12 " "Parallel compilation is enabled and will use 12 of the 12 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Timing Analyzer" 0 -1 1721436044758 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436044794 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436044794 ""}
{ "Warning" "WTDB_ANALYZE_COMB_LATCHES" "314 " "The Timing Analyzer is analyzing 314 combinational loops as latches. For more details, run the Check Timing command in the Timing Analyzer or view the \"User-Specified and Inferred Latches\" table in the Analysis & Synthesis report." {  } {  } 0 335093 "The Timing Analyzer is analyzing %1!d! combinational loops as latches. For more details, run the Check Timing command in the Timing Analyzer or view the \"User-Specified and Inferred Latches\" table in the Analysis & Synthesis report." 0 0 "Timing Analyzer" 0 -1 1721436044920 ""}
{ "Info" "ISTA_SDC_STATEMENT_PARENT" "" "Evaluating HDL-embedded SDC commands" { { "Info" "ISTA_SDC_STATEMENT_ENTITY" "dcfifo_vve1 " "Entity dcfifo_vve1" { { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_false_path -from *rdptr_g* -to *ws_dgrp\|dffpipe_c09:dffpipe15\|dffe16a*  " "set_false_path -from *rdptr_g* -to *ws_dgrp\|dffpipe_c09:dffpipe15\|dffe16a* " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1721436044957 ""} { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_false_path -from *delayed_wrptr_g* -to *rs_dgwp\|dffpipe_b09:dffpipe12\|dffe13a*  " "set_false_path -from *delayed_wrptr_g* -to *rs_dgwp\|dffpipe_b09:dffpipe12\|dffe13a* " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1721436044957 ""}  } {  } 0 332165 "Entity %1!s!" 0 0 "Design Software" 0 -1 1721436044957 ""} { "Info" "ISTA_SDC_STATEMENT_ENTITY" "sld_hub " "Entity sld_hub" { { "Info" "ISTA_SDC_STATEMENT_EVAL" "create_clock -name altera_reserved_tck \[get_ports \{altera_reserved_tck\}\] -period 10MHz    " "create_clock -name altera_reserved_tck \[get_ports \{altera_reserved_tck\}\] -period 10MHz   " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1721436044957 ""} { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_clock_groups -asynchronous -group \{altera_reserved_tck\} " "set_clock_groups -asynchronous -group \{altera_reserved_tck\}" {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1721436044957 ""}  } {  } 0 332165 "Entity %1!s!" 0 0 "Design Software" 0 -1 1721436044957 ""}  } {  } 0 332164 "Evaluating HDL-embedded SDC commands" 0 0 "Timing Analyzer" 0 -1 1721436044957 ""}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "FMC.sdc " "Synopsys Design Constraints File file not found: 'FMC.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 332012 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "Timing Analyzer" 0 -1 1721436044967 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_DA1_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_DA1_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register SINROM:inst6\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a5~porta_address_reg0 FREQ_DEV:u_DA1_DEV\|FREQ_OUT " "Register SINROM:inst6\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a5~porta_address_reg0 is being clocked by FREQ_DEV:u_DA1_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436044972 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436044972 "|TOP|FREQ_DEV:u_DA1_DEV|FREQ_OUT"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "CLK " "Node: CLK was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register FREQ_DEV:u_DA1_DEV\|FREQ_OUT CLK " "Register FREQ_DEV:u_DA1_DEV\|FREQ_OUT is being clocked by CLK" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436044972 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436044972 "|TOP|CLK"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FPGA_CS_NEL " "Node: FPGA_CS_NEL was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Latch MASTER_CTRL:inst3\|CTRL_DATA\[1\] FPGA_CS_NEL " "Latch MASTER_CTRL:inst3\|CTRL_DATA\[1\] is being clocked by FPGA_CS_NEL" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436044972 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436044972 "|TOP|FPGA_CS_NEL"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_DA2_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_DA2_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register SINROM:inst14\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a5~porta_address_reg0 FREQ_DEV:u_DA2_DEV\|FREQ_OUT " "Register SINROM:inst14\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a5~porta_address_reg0 is being clocked by FREQ_DEV:u_DA2_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436044972 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436044972 "|TOP|FREQ_DEV:u_DA2_DEV|FREQ_OUT"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "AD2_INPUT_CLK " "Node: AD2_INPUT_CLK was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register inst4 AD2_INPUT_CLK " "Register inst4 is being clocked by AD2_INPUT_CLK" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436044972 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436044972 "|TOP|AD2_INPUT_CLK"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "AD1_INPUT_CLK " "Node: AD1_INPUT_CLK was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register inst13 AD1_INPUT_CLK " "Register inst13 is being clocked by AD1_INPUT_CLK" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436044972 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436044972 "|TOP|AD1_INPUT_CLK"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_AD2_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_AD2_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register TYFIFO:u_AD2_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg FREQ_DEV:u_AD2_DEV\|FREQ_OUT " "Register TYFIFO:u_AD2_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg is being clocked by FREQ_DEV:u_AD2_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436044972 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436044972 "|TOP|FREQ_DEV:u_AD2_DEV|FREQ_OUT"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_AD1_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_AD1_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg FREQ_DEV:u_AD1_DEV\|FREQ_OUT " "Register TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg is being clocked by FREQ_DEV:u_AD1_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436044972 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436044972 "|TOP|FREQ_DEV:u_AD1_DEV|FREQ_OUT"}
{ "Warning" "WSTA_GENERIC_WARNING" "PLL cross checking found inconsistent PLL clock settings: " "PLL cross checking found inconsistent PLL clock settings:" { { "Warning" "WSTA_GENERIC_WARNING" "Node: inst1\|altpll_component\|auto_generated\|pll1\|clk\[0\] was found missing 1 generated clock that corresponds to a base clock with a period of: 20.000 " "Node: inst1\|altpll_component\|auto_generated\|pll1\|clk\[0\] was found missing 1 generated clock that corresponds to a base clock with a period of: 20.000" {  } {  } 0 332056 "%1!s!" 0 0 "Design Software" 0 -1 1721436044978 ""}  } {  } 0 332056 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1721436044978 ""}
{ "Critical Warning" "WSTA_NO_UNCERTAINTY_WAS_SET_PARENT" "" "The following clock transfers have no clock uncertainty assignment. For more accurate results, apply clock uncertainty assignments or use the derive_clock_uncertainty command." { { "Critical Warning" "WSTA_NO_UNCERTAINTY_WAS_SET_CHILD" "altera_reserved_tck (Rise) altera_reserved_tck (Rise) setup and hold " "From altera_reserved_tck (Rise) to altera_reserved_tck (Rise) (setup and hold)" {  } {  } 1 332169 "From %1!s! to %2!s! (%3!s!)" 0 0 "Design Software" 0 -1 1721436044978 ""} { "Critical Warning" "WSTA_NO_UNCERTAINTY_WAS_SET_CHILD" "altera_reserved_tck (Rise) altera_reserved_tck (Fall) setup and hold " "From altera_reserved_tck (Rise) to altera_reserved_tck (Fall) (setup and hold)" {  } {  } 1 332169 "From %1!s! to %2!s! (%3!s!)" 0 0 "Design Software" 0 -1 1721436044978 ""}  } {  } 1 332168 "The following clock transfers have no clock uncertainty assignment. For more accurate results, apply clock uncertainty assignments or use the derive_clock_uncertainty command." 0 0 "Timing Analyzer" 0 -1 1721436044978 ""}
{ "Info" "0" "" "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" {  } {  } 0 0 "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" 0 0 "Timing Analyzer" 0 0 1721436044979 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 85C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 85C Model" 0 0 "Timing Analyzer" 0 0 1721436044985 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 43.305 " "Worst-case setup slack is 43.305" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436044998 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436044998 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   43.305               0.000 altera_reserved_tck  " "   43.305               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436044998 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436044998 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.453 " "Worst-case hold slack is 0.453" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045000 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045000 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.453               0.000 altera_reserved_tck  " "    0.453               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045000 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436045000 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "recovery 96.406 " "Worst-case recovery slack is 96.406" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045002 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045002 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   96.406               0.000 altera_reserved_tck  " "   96.406               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045002 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436045002 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "removal 1.128 " "Worst-case removal slack is 1.128" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045004 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045004 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.128               0.000 altera_reserved_tck  " "    1.128               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045004 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436045004 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 49.448 " "Worst-case minimum pulse width slack is 49.448" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045005 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045005 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   49.448               0.000 altera_reserved_tck  " "   49.448               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045005 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436045005 ""}
{ "Info" "ISTA_REPORT_METASTABILITY_INFO" "Report Metastability: Found 2 synchronizer chains. " "Report Metastability: Found 2 synchronizer chains." { { "Info" "ISTA_REPORT_METASTABILITY_INFO" "The design MTBF is not calculated because there are no specified synchronizers in the design. " "The design MTBF is not calculated because there are no specified synchronizers in the design." {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045034 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Number of Synchronizer Chains Found: 2 " "Number of Synchronizer Chains Found: 2" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045034 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Shortest Synchronizer Chain: 4 Registers " "Shortest Synchronizer Chain: 4 Registers" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045034 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Fraction of Chains for which MTBFs Could Not be Calculated: 1.000 " "Fraction of Chains for which MTBFs Could Not be Calculated: 1.000" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045034 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Worst Case Available Settling Time: 340.761 ns " "Worst Case Available Settling Time: 340.761 ns" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045034 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" " " "" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045034 ""}  } {  } 0 332114 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1721436045034 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 0C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 0C Model" 0 0 "Timing Analyzer" 0 0 1721436045036 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Timing Analyzer" 0 -1 1721436045051 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Timing Analyzer" 0 -1 1721436045316 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_DA1_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_DA1_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register SINROM:inst6\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a5~porta_address_reg0 FREQ_DEV:u_DA1_DEV\|FREQ_OUT " "Register SINROM:inst6\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a5~porta_address_reg0 is being clocked by FREQ_DEV:u_DA1_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045409 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045409 "|TOP|FREQ_DEV:u_DA1_DEV|FREQ_OUT"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "CLK " "Node: CLK was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register FREQ_DEV:u_DA1_DEV\|FREQ_OUT CLK " "Register FREQ_DEV:u_DA1_DEV\|FREQ_OUT is being clocked by CLK" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045409 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045409 "|TOP|CLK"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FPGA_CS_NEL " "Node: FPGA_CS_NEL was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Latch MASTER_CTRL:inst3\|CTRL_DATA\[1\] FPGA_CS_NEL " "Latch MASTER_CTRL:inst3\|CTRL_DATA\[1\] is being clocked by FPGA_CS_NEL" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045409 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045409 "|TOP|FPGA_CS_NEL"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_DA2_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_DA2_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register SINROM:inst14\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a5~porta_address_reg0 FREQ_DEV:u_DA2_DEV\|FREQ_OUT " "Register SINROM:inst14\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a5~porta_address_reg0 is being clocked by FREQ_DEV:u_DA2_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045409 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045409 "|TOP|FREQ_DEV:u_DA2_DEV|FREQ_OUT"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "AD2_INPUT_CLK " "Node: AD2_INPUT_CLK was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register inst4 AD2_INPUT_CLK " "Register inst4 is being clocked by AD2_INPUT_CLK" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045409 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045409 "|TOP|AD2_INPUT_CLK"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "AD1_INPUT_CLK " "Node: AD1_INPUT_CLK was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register inst13 AD1_INPUT_CLK " "Register inst13 is being clocked by AD1_INPUT_CLK" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045409 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045409 "|TOP|AD1_INPUT_CLK"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_AD2_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_AD2_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register TYFIFO:u_AD2_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg FREQ_DEV:u_AD2_DEV\|FREQ_OUT " "Register TYFIFO:u_AD2_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg is being clocked by FREQ_DEV:u_AD2_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045409 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045409 "|TOP|FREQ_DEV:u_AD2_DEV|FREQ_OUT"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_AD1_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_AD1_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg FREQ_DEV:u_AD1_DEV\|FREQ_OUT " "Register TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg is being clocked by FREQ_DEV:u_AD1_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045409 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045409 "|TOP|FREQ_DEV:u_AD1_DEV|FREQ_OUT"}
{ "Warning" "WSTA_GENERIC_WARNING" "PLL cross checking found inconsistent PLL clock settings: " "PLL cross checking found inconsistent PLL clock settings:" { { "Warning" "WSTA_GENERIC_WARNING" "Node: inst1\|altpll_component\|auto_generated\|pll1\|clk\[0\] was found missing 1 generated clock that corresponds to a base clock with a period of: 20.000 " "Node: inst1\|altpll_component\|auto_generated\|pll1\|clk\[0\] was found missing 1 generated clock that corresponds to a base clock with a period of: 20.000" {  } {  } 0 332056 "%1!s!" 0 0 "Design Software" 0 -1 1721436045412 ""}  } {  } 0 332056 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1721436045412 ""}
{ "Critical Warning" "WSTA_NO_UNCERTAINTY_WAS_SET_PARENT" "" "The following clock transfers have no clock uncertainty assignment. For more accurate results, apply clock uncertainty assignments or use the derive_clock_uncertainty command." { { "Critical Warning" "WSTA_NO_UNCERTAINTY_WAS_SET_CHILD" "altera_reserved_tck (Rise) altera_reserved_tck (Rise) setup and hold " "From altera_reserved_tck (Rise) to altera_reserved_tck (Rise) (setup and hold)" {  } {  } 1 332169 "From %1!s! to %2!s! (%3!s!)" 0 0 "Design Software" 0 -1 1721436045413 ""} { "Critical Warning" "WSTA_NO_UNCERTAINTY_WAS_SET_CHILD" "altera_reserved_tck (Rise) altera_reserved_tck (Fall) setup and hold " "From altera_reserved_tck (Rise) to altera_reserved_tck (Fall) (setup and hold)" {  } {  } 1 332169 "From %1!s! to %2!s! (%3!s!)" 0 0 "Design Software" 0 -1 1721436045413 ""}  } {  } 1 332168 "The following clock transfers have no clock uncertainty assignment. For more accurate results, apply clock uncertainty assignments or use the derive_clock_uncertainty command." 0 0 "Timing Analyzer" 0 -1 1721436045413 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 43.699 " "Worst-case setup slack is 43.699" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045426 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045426 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   43.699               0.000 altera_reserved_tck  " "   43.699               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045426 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436045426 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.401 " "Worst-case hold slack is 0.401" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045430 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045430 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.401               0.000 altera_reserved_tck  " "    0.401               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045430 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436045430 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "recovery 96.597 " "Worst-case recovery slack is 96.597" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045432 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045432 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   96.597               0.000 altera_reserved_tck  " "   96.597               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045432 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436045432 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "removal 1.029 " "Worst-case removal slack is 1.029" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045435 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045435 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.029               0.000 altera_reserved_tck  " "    1.029               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045435 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436045435 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 49.301 " "Worst-case minimum pulse width slack is 49.301" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045437 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045437 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   49.301               0.000 altera_reserved_tck  " "   49.301               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045437 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436045437 ""}
{ "Info" "ISTA_REPORT_METASTABILITY_INFO" "Report Metastability: Found 2 synchronizer chains. " "Report Metastability: Found 2 synchronizer chains." { { "Info" "ISTA_REPORT_METASTABILITY_INFO" "The design MTBF is not calculated because there are no specified synchronizers in the design. " "The design MTBF is not calculated because there are no specified synchronizers in the design." {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045471 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Number of Synchronizer Chains Found: 2 " "Number of Synchronizer Chains Found: 2" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045471 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Shortest Synchronizer Chain: 4 Registers " "Shortest Synchronizer Chain: 4 Registers" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045471 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Fraction of Chains for which MTBFs Could Not be Calculated: 1.000 " "Fraction of Chains for which MTBFs Could Not be Calculated: 1.000" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045471 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Worst Case Available Settling Time: 341.450 ns " "Worst Case Available Settling Time: 341.450 ns" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045471 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" " " "" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045471 ""}  } {  } 0 332114 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1721436045471 ""}
{ "Info" "0" "" "Analyzing Fast 1200mV 0C Model" {  } {  } 0 0 "Analyzing Fast 1200mV 0C Model" 0 0 "Timing Analyzer" 0 0 1721436045474 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_DA1_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_DA1_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register SINROM:inst6\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a5~porta_address_reg0 FREQ_DEV:u_DA1_DEV\|FREQ_OUT " "Register SINROM:inst6\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a5~porta_address_reg0 is being clocked by FREQ_DEV:u_DA1_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045567 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045567 "|TOP|FREQ_DEV:u_DA1_DEV|FREQ_OUT"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "CLK " "Node: CLK was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register FREQ_DEV:u_DA1_DEV\|FREQ_OUT CLK " "Register FREQ_DEV:u_DA1_DEV\|FREQ_OUT is being clocked by CLK" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045567 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045567 "|TOP|CLK"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FPGA_CS_NEL " "Node: FPGA_CS_NEL was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Latch MASTER_CTRL:inst3\|CTRL_DATA\[1\] FPGA_CS_NEL " "Latch MASTER_CTRL:inst3\|CTRL_DATA\[1\] is being clocked by FPGA_CS_NEL" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045567 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045567 "|TOP|FPGA_CS_NEL"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_DA2_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_DA2_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register SINROM:inst14\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a5~porta_address_reg0 FREQ_DEV:u_DA2_DEV\|FREQ_OUT " "Register SINROM:inst14\|altsyncram:altsyncram_component\|altsyncram_eja1:auto_generated\|ram_block1a5~porta_address_reg0 is being clocked by FREQ_DEV:u_DA2_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045567 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045567 "|TOP|FREQ_DEV:u_DA2_DEV|FREQ_OUT"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "AD2_INPUT_CLK " "Node: AD2_INPUT_CLK was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register inst4 AD2_INPUT_CLK " "Register inst4 is being clocked by AD2_INPUT_CLK" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045567 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045567 "|TOP|AD2_INPUT_CLK"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "AD1_INPUT_CLK " "Node: AD1_INPUT_CLK was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register inst13 AD1_INPUT_CLK " "Register inst13 is being clocked by AD1_INPUT_CLK" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045567 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045567 "|TOP|AD1_INPUT_CLK"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_AD2_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_AD2_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register TYFIFO:u_AD2_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg FREQ_DEV:u_AD2_DEV\|FREQ_OUT " "Register TYFIFO:u_AD2_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg is being clocked by FREQ_DEV:u_AD2_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045567 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045567 "|TOP|FREQ_DEV:u_AD2_DEV|FREQ_OUT"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "FREQ_DEV:u_AD1_DEV\|FREQ_OUT " "Node: FREQ_DEV:u_AD1_DEV\|FREQ_OUT was determined to be a clock but was found without an associated clock assignment." { { "Info" "ISTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT_DETAILS" "Register TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg FREQ_DEV:u_AD1_DEV\|FREQ_OUT " "Register TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\|ram_block11a0~porta_we_reg is being clocked by FREQ_DEV:u_AD1_DEV\|FREQ_OUT" {  } {  } 0 13166 "%1!s! %2!s! is being clocked by %3!s!" 0 0 "Design Software" 0 -1 1721436045567 ""}  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Timing Analyzer" 0 -1 1721436045567 "|TOP|FREQ_DEV:u_AD1_DEV|FREQ_OUT"}
{ "Warning" "WSTA_GENERIC_WARNING" "PLL cross checking found inconsistent PLL clock settings: " "PLL cross checking found inconsistent PLL clock settings:" { { "Warning" "WSTA_GENERIC_WARNING" "Node: inst1\|altpll_component\|auto_generated\|pll1\|clk\[0\] was found missing 1 generated clock that corresponds to a base clock with a period of: 20.000 " "Node: inst1\|altpll_component\|auto_generated\|pll1\|clk\[0\] was found missing 1 generated clock that corresponds to a base clock with a period of: 20.000" {  } {  } 0 332056 "%1!s!" 0 0 "Design Software" 0 -1 1721436045570 ""}  } {  } 0 332056 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1721436045570 ""}
{ "Critical Warning" "WSTA_NO_UNCERTAINTY_WAS_SET_PARENT" "" "The following clock transfers have no clock uncertainty assignment. For more accurate results, apply clock uncertainty assignments or use the derive_clock_uncertainty command." { { "Critical Warning" "WSTA_NO_UNCERTAINTY_WAS_SET_CHILD" "altera_reserved_tck (Rise) altera_reserved_tck (Rise) setup and hold " "From altera_reserved_tck (Rise) to altera_reserved_tck (Rise) (setup and hold)" {  } {  } 1 332169 "From %1!s! to %2!s! (%3!s!)" 0 0 "Design Software" 0 -1 1721436045570 ""} { "Critical Warning" "WSTA_NO_UNCERTAINTY_WAS_SET_CHILD" "altera_reserved_tck (Rise) altera_reserved_tck (Fall) setup and hold " "From altera_reserved_tck (Rise) to altera_reserved_tck (Fall) (setup and hold)" {  } {  } 1 332169 "From %1!s! to %2!s! (%3!s!)" 0 0 "Design Software" 0 -1 1721436045570 ""}  } {  } 1 332168 "The following clock transfers have no clock uncertainty assignment. For more accurate results, apply clock uncertainty assignments or use the derive_clock_uncertainty command." 0 0 "Timing Analyzer" 0 -1 1721436045570 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 47.210 " "Worst-case setup slack is 47.210" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045574 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045574 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   47.210               0.000 altera_reserved_tck  " "   47.210               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045574 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436045574 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.186 " "Worst-case hold slack is 0.186" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045577 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045577 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.186               0.000 altera_reserved_tck  " "    0.186               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045577 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436045577 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "recovery 98.289 " "Worst-case recovery slack is 98.289" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045580 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045580 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   98.289               0.000 altera_reserved_tck  " "   98.289               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045580 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436045580 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "removal 0.495 " "Worst-case removal slack is 0.495" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045583 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045583 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.495               0.000 altera_reserved_tck  " "    0.495               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045583 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436045583 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 49.450 " "Worst-case minimum pulse width slack is 49.450" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045585 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045585 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   49.450               0.000 altera_reserved_tck  " "   49.450               0.000 altera_reserved_tck " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1721436045585 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1721436045585 ""}
{ "Info" "ISTA_REPORT_METASTABILITY_INFO" "Report Metastability: Found 2 synchronizer chains. " "Report Metastability: Found 2 synchronizer chains." { { "Info" "ISTA_REPORT_METASTABILITY_INFO" "The design MTBF is not calculated because there are no specified synchronizers in the design. " "The design MTBF is not calculated because there are no specified synchronizers in the design." {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045616 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Number of Synchronizer Chains Found: 2 " "Number of Synchronizer Chains Found: 2" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045616 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Shortest Synchronizer Chain: 4 Registers " "Shortest Synchronizer Chain: 4 Registers" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045616 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Fraction of Chains for which MTBFs Could Not be Calculated: 1.000 " "Fraction of Chains for which MTBFs Could Not be Calculated: 1.000" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045616 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Worst Case Available Settling Time: 346.253 ns " "Worst Case Available Settling Time: 346.253 ns" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045616 ""} { "Info" "ISTA_REPORT_METASTABILITY_INFO" " " "" {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1721436045616 ""}  } {  } 0 332114 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1721436045616 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "setup " "Design is not fully constrained for setup requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1721436045868 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "hold " "Design is not fully constrained for hold requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1721436045869 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Timing Analyzer 0 s 41 s Quartus Prime " "Quartus Prime Timing Analyzer was successful. 0 errors, 41 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4877 " "Peak virtual memory: 4877 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1721436045972 ""} { "Info" "IQEXE_END_BANNER_TIME" "Sat Jul 20 08:40:45 2024 " "Processing ended: Sat Jul 20 08:40:45 2024" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1721436045972 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1721436045972 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:00 " "Total CPU time (on all processors): 00:00:00" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1721436045972 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Timing Analyzer" 0 -1 1721436045972 ""}
