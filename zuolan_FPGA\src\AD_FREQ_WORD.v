//&----------------------------------------------------------------------------------------
//& 模块名: AD_FREQ_WORD
//& 文件名: AD_FREQ_WORD.v
//& 作  者: 左岚
//& 日  期: 2025-07-18
//&
//& 功  能: AD频率字设定模块。该模块包含4个16位的写操作寄存器，用于从外部总线
//&         接收配置字（例如频率控制字的高低位）。
//&
//& 设计说明:
//& 1. 本模块使用了非标准的总线接口，有四个独立的数据输入端(DATA0-DATA3)，
//&    分别对应不同的写入地址。标准总线通常只有一个数据输入端。
//& 2. **重要警告**: 此代码在组合逻辑块(always @(*))中使用了非阻塞赋值(<=)。
//&    这会导致综合工具生成锁存器(Latch)，而不是期望的时钟同步寄存器(Flip-Flop)。
//&    锁存器对信号毛刺敏感，在设计中应谨慎使用，因为它可能导致时序问题。
//&    正确的寄存器实现方式应使用时序逻辑块 (例如: always @(posedge clk))。
//&----------------------------------------------------------------------------------------

module AD_FREQ_WORD #(
    // --- 参数定义 (地址映射) ---
    parameter ADDR6 = 16'h0006,  // AD1 配置字高16位 写入地址
    parameter ADDR7 = 16'h0007,  // AD1 配置字低16位 写入地址
    parameter ADDR8 = 16'h0008,  // AD2 配置字高16位 写入地址
    parameter ADDR9 = 16'h0009   // AD2 配置字低16位 写入地址
) (
    // --- 端口定义 ---
    // -- 总线控制信号
    input             CS,        // 片选信号，低电平有效
    input             WR_EN,     // 写使能信号，高电平有效
    // -- 数据输入 (非标准接口)
    input      [15:0] DATA0,     // 对应地址ADDR6的数据输入
    input      [15:0] DATA1,     // 对应地址ADDR7的数据输入
    input      [15:0] DATA2,     // 对应地址ADDR8的数据输入
    input      [15:0] DATA3,     // 对应地址ADDR9的数据输入
    // -- 总线地址输入
    input      [15:0] ADDR,      // 16位地址总线
    // -- 寄存器输出 (将作为锁存器实现)
    output reg [15:0] AD1_OUTH,  // 存储AD1配置字的高16位
    output reg [15:0] AD1_OUTL,  // 存储AD1配置字的低16位
    output reg [15:0] AD2_OUTH,  // 存储AD2配置字的高16位
    output reg [15:0] AD2_OUTL   // 存储AD2配置字的低16位
);

  // --- 逻辑实现 ---
  // **警告**: 这是一个组合逻辑块，但使用了非阻塞赋值(<=)，会综合成锁存器。
  // 理想情况下，寄存器的写入操作应该在一个由时钟沿触发的 `always` 块中完成。
  always @(*) begin
    // 当片选有效(CS为低)且写使能有效(WR_EN为高)时，执行写操作
    if (!CS && WR_EN) begin
      // 根据地址ADDR，将对应的数据输入锁存到输出寄存器中
      case (ADDR)
        ADDR6: AD1_OUTH <= DATA0;  // 地址为ADDR6时，锁存DATA0到AD1_OUTH
        ADDR7: AD1_OUTL <= DATA1;  // 地址为ADDR7时，锁存DATA1到AD1_OUTL
        ADDR8: AD2_OUTH <= DATA2;  // 地址为ADDR8时，锁存DATA2到AD2_OUTH
        ADDR9: AD2_OUTL <= DATA3;  // 地址为ADDR9时，锁存DATA3到AD2_OUTL
        // default: ; // 在组合逻辑中，case语句需要覆盖所有情况以避免意外生成锁存器，
        // 但此处的设计意图本就是生成锁存器。
      endcase
    end
  end
endmodule
