--sld_ela_trigger DATA_BITS=60 INVERSION_MASK=00000000 INVERSION_MASK_LENGTH=1 LEVEL_NAMES="sld_reserved_JYX_FPGA_OBJECT_auto_signaltap_0_1_53e7," POWER_UP_TRIGGER=0 TRIGGER_LEVEL=1 acq_clk data_in reset_all setup_bit_in setup_bit_out setup_ena tck trigger_level_match_out
--VERSION_BEGIN 18.1 cbx_mgl 2018:09:12:13:10:36:SJ cbx_sld_ela_trigger 2018:09:12:13:04:24:SJ  VERSION_END


-- Copyright (C) 2018  Intel Corporation. All rights reserved.
--  Your use of Intel Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Intel Program License 
--  Subscription Agreement, the Intel Quartus Prime License Agreement,
--  the Intel FPGA IP License Agreement, or other applicable license
--  agreement, including, without limitation, that your use is for
--  the sole purpose of programming logic devices manufactured by
--  Intel and sold by Intel or its authorized distributors.  Please
--  refer to the applicable agreement for further details.


FUNCTION sld_reserved_JYX_FPGA_OBJECT_auto_signaltap_0_1_53e7 (acq_clk, data_in[59..0], reset_all, setup_bit_in, setup_ena, tck)
WITH ( DATA_BITS, SYNC_ENABLED, TRIGGER_LEVEL)
RETURNS ( setup_bit_out, trigger);

--synthesis_resources = sld_reserved_JYX_FPGA_OBJECT_auto_signaltap_0_1_53e7 1 
SUBDESIGN sld_ela_trigger_mgp
( 
	acq_clk	:	input;
	data_in[59..0]	:	input;
	reset_all	:	input;
	setup_bit_in	:	input;
	setup_bit_out	:	output;
	setup_ena	:	input;
	tck	:	input;
	trigger_level_match_out[0..0]	:	output;
) 
VARIABLE 
	mgl_prim1 : sld_reserved_JYX_FPGA_OBJECT_auto_signaltap_0_1_53e7
		WITH (
			DATA_BITS = 60,
			SYNC_ENABLED = 0,
			TRIGGER_LEVEL = 1
		);

BEGIN 
	mgl_prim1.acq_clk = acq_clk;
	mgl_prim1.data_in[] = data_in[];
	mgl_prim1.reset_all = reset_all;
	mgl_prim1.setup_bit_in = setup_bit_in;
	mgl_prim1.setup_ena = setup_ena;
	mgl_prim1.tck = tck;
	setup_bit_out = mgl_prim1.setup_bit_out;
	trigger_level_match_out[] = ( mgl_prim1.trigger);
END;
--VALID FILE
