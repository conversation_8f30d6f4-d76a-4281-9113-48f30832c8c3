//&----------------------------------------------------------------------------------------
//& 模块名: test
//& 文件名: test.v
//& 作  者: 左岚
//& 日  期: 2025年7月18日
//&
//& 功  能: 一个简单的测试模块。
//&         该模块的功能是直接将一个16位的输入端口连接到一个16位的输出端口，
//&         在硬件上实现为一组导线连接。
//&----------------------------------------------------------------------------------------

module test (
    // --- 端口定义 ---
    input  [15:0] old_data,  // 16位输入数据
    output [15:0] new_data   // 16位输出数据
);

  // --- 逻辑实现 ---
  // 使用连续赋值(assign)语句，将输入old_data的值直接赋给输出new_data。
  // 这描述了一段纯组合逻辑，相当于在输入和输出之间直接连接了16根导线。
  assign new_data = old_data;

endmodule
