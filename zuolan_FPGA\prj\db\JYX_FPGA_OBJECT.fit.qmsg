{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "16 16 " "Parallel compilation is enabled and will use 16 of the 16 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Fitter" 0 -1 1752772073293 ""}
{ "Info" "IMPP_MPP_USER_DEVICE" "JYX_FPGA_OBJECT EP4CE10F17C8 " "Selected device EP4CE10F17C8 for design \"JYX_FPGA_OBJECT\"" {  } {  } 0 119006 "Selected device %2!s! for design \"%1!s!\"" 0 0 "Fitter" 0 -1 1752772073314 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1752772073349 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1752772073349 ""}
{ "Info" "ICUT_CUT_PLL_COMPUTATION_SUCCESS" "MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated\|pll1 Cyclone IV E PLL " "Implemented PLL \"MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated\|pll1\" as Cyclone IV E PLL type" { { "Info" "ICUT_CUT_YGR_PLL_PARAMETERS_FACTORS" "MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated\|wire_pll1_clk\[0\] 3 1 0 0 " "Implementing clock multiplication of 3, clock division of 1, and phase shift of 0 degrees (0 ps) for MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated\|wire_pll1_clk\[0\] port" {  } { { "db/mypll_altpll1.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/mypll_altpll1.v" 43 -1 0 } } { "" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1334 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 15099 "Implementing clock multiplication of %2!d!, clock division of %3!d!, and phase shift of %4!d! degrees (%5!d! ps) for %1!s! port" 0 0 "Design Software" 0 -1 1752772073376 ""}  } { { "db/mypll_altpll1.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/mypll_altpll1.v" 43 -1 0 } } { "" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1334 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 15535 "Implemented %3!s! \"%1!s!\" as %2!s! PLL type" 0 0 "Fitter" 0 -1 1752772073376 ""}
{ "Info" "IFITCC_FITCC_INFO_AUTO_FIT_COMPILATION_ON" "" "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" {  } {  } 0 171003 "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" 0 0 "Fitter" 0 -1 1752772073441 ""}
{ "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED" "" "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" { { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE6F17C8 " "Device EP4CE6F17C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1752772073524 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE15F17C8 " "Device EP4CE15F17C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1752772073524 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE22F17C8 " "Device EP4CE22F17C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1752772073524 ""}  } {  } 2 176444 "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" 0 0 "Fitter" 0 -1 1752772073524 ""}
{ "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION" "5 " "Fitter converted 5 user pins into dedicated programming pins" { { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_ASDO_DATA1~ C1 " "Pin ~ALTERA_ASDO_DATA1~ is reserved at location C1" {  } { { "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_ASDO_DATA1~ } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 11784 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1752772073529 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_FLASH_nCE_nCSO~ D2 " "Pin ~ALTERA_FLASH_nCE_nCSO~ is reserved at location D2" {  } { { "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_FLASH_nCE_nCSO~ } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 11786 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1752772073529 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DCLK~ H1 " "Pin ~ALTERA_DCLK~ is reserved at location H1" {  } { { "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DCLK~ } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 11788 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1752772073529 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DATA0~ H2 " "Pin ~ALTERA_DATA0~ is reserved at location H2" {  } { { "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DATA0~ } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 11790 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1752772073529 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_nCEO~ F16 " "Pin ~ALTERA_nCEO~ is reserved at location F16" {  } { { "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "d:/intelfpga/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_nCEO~ } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 11792 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1752772073529 ""}  } {  } 0 169124 "Fitter converted %1!d! user pins into dedicated programming pins" 0 0 "Fitter" 0 -1 1752772073529 ""}
{ "Warning" "WCUT_CUT_ATOM_PINS_WITH_INCOMPLETE_IO_ASSIGNMENTS" "" "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" {  } {  } 0 15714 "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" 0 0 "Fitter" 0 -1 1752772073531 ""}
{ "Info" "IFSAC_FSAC_RAM_METASTABILITY_INFO" "" "Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." {  } {  } 0 176045 "Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." 0 0 "Fitter" 0 -1 1752772073571 ""}
{ "Warning" "WTDB_ANALYZE_COMB_LATCHES" "226 " "The Timing Analyzer is analyzing 226 combinational loops as latches. For more details, run the Check Timing command in the Timing Analyzer or view the \"User-Specified and Inferred Latches\" table in the Analysis & Synthesis report." {  } {  } 0 335093 "The Timing Analyzer is analyzing %1!d! combinational loops as latches. For more details, run the Check Timing command in the Timing Analyzer or view the \"User-Specified and Inferred Latches\" table in the Analysis & Synthesis report." 0 0 "Fitter" 0 -1 1752772074083 ""}
{ "Info" "ISTA_SDC_STATEMENT_PARENT" "" "Evaluating HDL-embedded SDC commands" { { "Info" "ISTA_SDC_STATEMENT_ENTITY" "dcfifo_vve1 " "Entity dcfifo_vve1" { { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_false_path -from *rdptr_g* -to *ws_dgrp\|dffpipe_c09:dffpipe15\|dffe16a*  " "set_false_path -from *rdptr_g* -to *ws_dgrp\|dffpipe_c09:dffpipe15\|dffe16a* " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1752772074084 ""} { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_false_path -from *delayed_wrptr_g* -to *rs_dgwp\|dffpipe_b09:dffpipe12\|dffe13a*  " "set_false_path -from *delayed_wrptr_g* -to *rs_dgwp\|dffpipe_b09:dffpipe12\|dffe13a* " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1752772074084 ""}  } {  } 0 332165 "Entity %1!s!" 0 0 "Design Software" 0 -1 1752772074084 ""}  } {  } 0 332164 "Evaluating HDL-embedded SDC commands" 0 0 "Fitter" 0 -1 1752772074084 ""}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "JYX_FPGA_OBJECT.sdc " "Synopsys Design Constraints File file not found: 'JYX_FPGA_OBJECT.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 332012 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "Fitter" 0 -1 1752772074091 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_NO_DERIVING_MSG" "generated clocks " "No user constrained generated clocks found in the design" {  } {  } 0 332144 "No user constrained %1!s! found in the design" 0 0 "Fitter" 0 -1 1752772074091 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_NO_DERIVING_MSG" "base clocks " "No user constrained base clocks found in the design" {  } {  } 0 332144 "No user constrained %1!s! found in the design" 0 0 "Fitter" 0 -1 1752772074092 ""}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 332143 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "Fitter" 0 -1 1752772074108 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Fitter" 0 -1 1752772074109 ""}
{ "Info" "ISTA_TDC_NO_DEFAULT_OPTIMIZATION_GOALS" "" "Timing requirements not specified -- quality metrics such as performance may be sacrificed to reduce compilation time." {  } {  } 0 332130 "Timing requirements not specified -- quality metrics such as performance may be sacrificed to reduce compilation time." 0 0 "Fitter" 0 -1 1752772074109 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated\|wire_pll1_clk\[0\] (placed in counter C0 of PLL_1) " "Automatically promoted node MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated\|wire_pll1_clk\[0\] (placed in counter C0 of PLL_1)" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G3 " "Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G3" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1752772074225 ""}  } { { "db/mypll_altpll1.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/db/mypll_altpll1.v" 77 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1334 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1752772074225 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "fmc_control:inst\|fmc_rd_en  " "Automatically promoted node fmc_control:inst\|fmc_rd_en " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[0\]~output " "Destination node FPGA_DB\[0\]~output" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2656 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1904 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 9661 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[1\]~output " "Destination node FPGA_DB\[1\]~output" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2656 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1904 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 9660 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[2\]~output " "Destination node FPGA_DB\[2\]~output" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2656 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1904 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 9659 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[3\]~output " "Destination node FPGA_DB\[3\]~output" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2656 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1904 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 9658 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[4\]~output " "Destination node FPGA_DB\[4\]~output" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2656 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1904 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 9657 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[5\]~output " "Destination node FPGA_DB\[5\]~output" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2656 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1904 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 9656 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[6\]~output " "Destination node FPGA_DB\[6\]~output" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2656 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1904 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 9655 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[7\]~output " "Destination node FPGA_DB\[7\]~output" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2656 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1904 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 9654 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[8\]~output " "Destination node FPGA_DB\[8\]~output" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2656 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1904 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 9653 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[9\]~output " "Destination node FPGA_DB\[9\]~output" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2656 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1904 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 9652 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_LIMITED_TO_SUB" "10 " "Non-global destination nodes limited to 10 nodes" {  } {  } 0 176358 "Non-global destination nodes limited to %1!d! nodes" 0 0 "Design Software" 0 -1 1752772074225 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1752772074225 ""}  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 48 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1287 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1752772074225 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "Automatically promoted node DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "DA_PARAMETER_CTRL:inst7\|FREQ_OUT_B_FINAL " "Destination node DA_PARAMETER_CTRL:inst7\|FREQ_OUT_B_FINAL" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 25 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1538 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "DA1_OUTCLK~output " "Destination node DA1_OUTCLK~output" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 648 5336 5525 664 "DA1_OUTCLK" "" } } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 11698 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1752772074225 ""}  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 30 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1530 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1752772074225 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "DA_PARAMETER_CTRL:inst7\|FREQ_OUT_B_FINAL  " "Automatically promoted node DA_PARAMETER_CTRL:inst7\|FREQ_OUT_B_FINAL " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "DA2_OUTCLK~output " "Destination node DA2_OUTCLK~output" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 704 5336 5525 720 "DA2_OUTCLK" "" } } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 11700 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1752772074225 ""}  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 25 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1538 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1752772074225 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "Automatically promoted node FREQ_DEV:u_AD1_DEV\|FREQ_OUT " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "AD1_OUTCLK~output " "Destination node AD1_OUTCLK~output" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1512 3816 4005 1528 "AD1_OUTCLK" "" } } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 11701 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1752772074225 ""}  } { { "../src/FREQ_DEV.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FREQ_DEV.v" 7 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 662 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1752772074225 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "Automatically promoted node FREQ_DEV:u_AD2_DEV\|FREQ_OUT " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "AD2_OUTCLK~output " "Destination node AD2_OUTCLK~output" {  } { { "TOP.bdf" "" { Schematic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/TOP.bdf" { { 1512 4272 4456 1528 "AD2_OUTCLK" "" } } } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 11702 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1752772074225 ""}  } { { "../src/FREQ_DEV.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FREQ_DEV.v" 7 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1683 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1752772074225 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "gate_generator:inst22\|gate  " "Automatically promoted node gate_generator:inst22\|gate " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "CNT32:u_AD1_CNT32\|Q1\[15\] " "Destination node CNT32:u_AD1_CNT32\|Q1\[15\]" {  } { { "../src/CNT32.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT32.v" 14 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 884 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "CNT32:u_AD1_CNT32\|Q1\[31\] " "Destination node CNT32:u_AD1_CNT32\|Q1\[31\]" {  } { { "../src/CNT32.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT32.v" 14 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 868 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "CNT32:u_AD1_CNT32\|Q1BASE\[15\] " "Destination node CNT32:u_AD1_CNT32\|Q1BASE\[15\]" {  } { { "../src/CNT32.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT32.v" 22 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 916 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "CNT32:u_AD1_CNT32\|Q1BASE\[31\] " "Destination node CNT32:u_AD1_CNT32\|Q1BASE\[31\]" {  } { { "../src/CNT32.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT32.v" 22 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 900 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "CNT32:u_AD1_CNT32\|Q1\[14\] " "Destination node CNT32:u_AD1_CNT32\|Q1\[14\]" {  } { { "../src/CNT32.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT32.v" 14 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 885 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "CNT32:u_AD1_CNT32\|Q1\[30\] " "Destination node CNT32:u_AD1_CNT32\|Q1\[30\]" {  } { { "../src/CNT32.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT32.v" 14 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 869 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "CNT32:u_AD1_CNT32\|Q1BASE\[14\] " "Destination node CNT32:u_AD1_CNT32\|Q1BASE\[14\]" {  } { { "../src/CNT32.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT32.v" 22 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 917 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "CNT32:u_AD1_CNT32\|Q1BASE\[30\] " "Destination node CNT32:u_AD1_CNT32\|Q1BASE\[30\]" {  } { { "../src/CNT32.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT32.v" 22 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 901 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "CNT32:u_AD1_CNT32\|Q1\[13\] " "Destination node CNT32:u_AD1_CNT32\|Q1\[13\]" {  } { { "../src/CNT32.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT32.v" 14 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 886 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "CNT32:u_AD1_CNT32\|Q1\[29\] " "Destination node CNT32:u_AD1_CNT32\|Q1\[29\]" {  } { { "../src/CNT32.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/CNT32.v" 14 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 870 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074225 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_LIMITED_TO_SUB" "10 " "Non-global destination nodes limited to 10 nodes" {  } {  } 0 176358 "Non-global destination nodes limited to %1!d! nodes" 0 0 "Design Software" 0 -1 1752772074225 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1752772074225 ""}  } { { "../src/gate_generator.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/gate_generator.v" 6 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 962 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1752772074225 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "AD_FREQ_WORD:u_AD_FREQ_WORD\|AD1_OUTH\[15\]~0  " "Automatically promoted node AD_FREQ_WORD:u_AD_FREQ_WORD\|AD1_OUTH\[15\]~0 " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1752772074226 ""}  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 8233 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1752772074226 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "AD_FREQ_WORD:u_AD_FREQ_WORD\|AD1_OUTL\[15\]~0  " "Automatically promoted node AD_FREQ_WORD:u_AD_FREQ_WORD\|AD1_OUTL\[15\]~0 " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1752772074226 ""}  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 8234 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1752772074226 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "AD_FREQ_WORD:u_AD_FREQ_WORD\|AD2_OUTH\[15\]~1  " "Automatically promoted node AD_FREQ_WORD:u_AD_FREQ_WORD\|AD2_OUTH\[15\]~1 " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1752772074226 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "fmc_control:inst\|read_data_8__reg\[15\] " "Destination node fmc_control:inst\|read_data_8__reg\[15\]" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 115 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1164 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074226 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "fmc_control:inst\|read_data_8__reg\[14\] " "Destination node fmc_control:inst\|read_data_8__reg\[14\]" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 115 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1165 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074226 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "fmc_control:inst\|read_data_8__reg\[13\] " "Destination node fmc_control:inst\|read_data_8__reg\[13\]" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 115 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1166 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074226 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "fmc_control:inst\|read_data_8__reg\[12\] " "Destination node fmc_control:inst\|read_data_8__reg\[12\]" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 115 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1167 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074226 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "fmc_control:inst\|read_data_8__reg\[11\] " "Destination node fmc_control:inst\|read_data_8__reg\[11\]" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 115 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1168 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074226 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "fmc_control:inst\|read_data_8__reg\[10\] " "Destination node fmc_control:inst\|read_data_8__reg\[10\]" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 115 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1169 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074226 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "fmc_control:inst\|read_data_8__reg\[9\] " "Destination node fmc_control:inst\|read_data_8__reg\[9\]" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 115 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1170 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074226 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "fmc_control:inst\|read_data_8__reg\[8\] " "Destination node fmc_control:inst\|read_data_8__reg\[8\]" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 115 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1171 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074226 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "fmc_control:inst\|read_data_8__reg\[7\] " "Destination node fmc_control:inst\|read_data_8__reg\[7\]" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 115 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1172 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074226 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "fmc_control:inst\|read_data_8__reg\[6\] " "Destination node fmc_control:inst\|read_data_8__reg\[6\]" {  } { { "../src/FMC/fmc_control.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/FMC/fmc_control.v" 115 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1173 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1752772074226 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_LIMITED_TO_SUB" "10 " "Non-global destination nodes limited to 10 nodes" {  } {  } 0 176358 "Non-global destination nodes limited to %1!d! nodes" 0 0 "Design Software" 0 -1 1752772074226 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1752772074226 ""}  } { { "../src/AD_FREQ_WORD.v" "" { Text "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/src/AD_FREQ_WORD.v" 21 -1 0 } } { "temporary_test_loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 8236 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1752772074226 ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_START_REGPACKING_INFO" "" "Starting register packing" {  } {  } 0 176233 "Starting register packing" 0 0 "Fitter" 0 -1 1752772074502 ""}
{ "Extra Info" "IFSAC_FSAC_START_REG_LOCATION_PROCESSING" "" "Performing register packing on registers with non-logic cell location assignments" {  } {  } 1 176273 "Performing register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1752772074505 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_REG_LOCATION_PROCESSING" "" "Completed register packing on registers with non-logic cell location assignments" {  } {  } 1 176274 "Completed register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1752772074505 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_BEGIN_FAST_REGISTER_INFO" "" "Started Fast Input/Output/OE register processing" {  } {  } 1 176236 "Started Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1752772074507 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_FAST_REGISTER_INFO" "" "Finished Fast Input/Output/OE register processing" {  } {  } 1 176237 "Finished Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1752772074509 ""}
{ "Extra Info" "IFSAC_FSAC_START_IO_MULT_RAM_PACKING" "" "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" {  } {  } 1 176248 "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" 1 0 "Fitter" 0 -1 1752772074512 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_IO_MULT_RAM_PACKING" "" "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" {  } {  } 1 176249 "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" 1 0 "Fitter" 0 -1 1752772074624 ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_REGPACKING_INFO" "" "Finished register packing" { { "Extra Info" "IFSAC_NUM_REGISTERS_PACKED_INTO_ATOM_TYPE" "28 Embedded multiplier block " "Packed 28 registers into blocks of type Embedded multiplier block" {  } {  } 1 176218 "Packed %1!d! registers into blocks of type %2!s!" 0 0 "Design Software" 0 -1 1752772074626 ""} { "Extra Info" "IFSAC_NUM_REGISTERS_DUPLICATED" "28 " "Created 28 register duplicates" {  } {  } 1 176220 "Created %1!d! register duplicates" 0 0 "Design Software" 0 -1 1752772074626 ""}  } {  } 0 176235 "Finished register packing" 0 0 "Fitter" 0 -1 1752772074626 ""}
{ "Info" "IFITCC_FITTER_PREPARATION_END" "00:00:01 " "Fitter preparation operations ending: elapsed time is 00:00:01" {  } {  } 0 171121 "Fitter preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1752772074710 ""}
{ "Info" "IVPR20K_VPR_FAMILY_APL_ERROR" "" "Fitter has disabled Advanced Physical Optimization because it is not supported for the current family." {  } {  } 0 14896 "Fitter has disabled Advanced Physical Optimization because it is not supported for the current family." 0 0 "Fitter" 0 -1 1752772074717 ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_START" "" "Fitter placement preparation operations beginning" {  } {  } 0 170189 "Fitter placement preparation operations beginning" 0 0 "Fitter" 0 -1 1752772075134 ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_END" "00:00:00 " "Fitter placement preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 170190 "Fitter placement preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1752772075492 ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_START" "" "Fitter placement operations beginning" {  } {  } 0 170191 "Fitter placement operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_INFO_VPR_PLACEMENT_FINISH" "" "Fitter placement was successful" {  } {  } 0 170137 "Fitter placement was successful" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_END" "00:00:06 " "Fitter placement operations ending: elapsed time is 00:00:06" {  } {  } 0 170192 "Fitter placement operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_START" "" "Fitter routing operations beginning" {  } {  } 0 170193 "Fitter routing operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_STATUS_DELAY_ADDED_FOR_HOLD" "2e+03 ns 5.8% " "2e+03 ns of routing delay (approximately 5.8% of available device routing delay) has been added to meet hold timing. For more information, refer to the Estimated Delay Added for Hold Timing section in the Fitter report." {  } {  } 0 170089 "%1!s! of routing delay (approximately %2!s! of available device routing delay) has been added to meet hold timing. For more information, refer to the Estimated Delay Added for Hold Timing section in the Fitter report." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_PERCENT_ROUTING_RESOURCE_USAGE" "21 " "Router estimated average interconnect usage is 21% of the available device resources" { { "Info" "IFITAPI_FITAPI_VPR_PEAK_ROUTING_REGION" "32 X0_Y0 X10_Y11 " "Router estimated peak interconnect usage is 32% of the available device resources in the region that extends from location X0_Y0 to location X10_Y11" {  } { { "loc" "" { Generic "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/" { { 1 { 0 "Router estimated peak interconnect usage is 32% of the available device resources in the region that extends from location X0_Y0 to location X10_Y11"} { { 12 { 0 ""} 0 0 11 12 }  }  }  }  } }  } 0 170196 "Router estimated peak interconnect usage is %1!d!%% of the available device resources in the region that extends from location %2!s! to location %3!s!" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 170195 "Router estimated average interconnect usage is %1!d!%% of the available device resources" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED" "" "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." { { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_ROUTABILITY" "" "Optimizations that may affect the design's routability were skipped" {  } {  } 0 170201 "Optimizations that may affect the design's routability were skipped" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 170199 "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_END" "00:00:10 " "Fitter routing operations ending: elapsed time is 00:00:10" {  } {  } 0 170194 "Fitter routing operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IVPR20K_VPR_TIMING_ANALYSIS_TIME" "the Fitter 3.64 " "Total time spent on timing analysis during the Fitter is 3.64 seconds." {  } {  } 0 11888 "Total time spent on timing analysis during %1!s! is %2!s! seconds." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 1752772092859 ""}
{ "Info" "IFITCC_FITTER_POST_OPERATION_END" "00:00:01 " "Fitter post-fit operations ending: elapsed time is 00:00:01" {  } {  } 0 11218 "Fitter post-fit operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1752772093538 ""}
{ "Info" "IRDB_WROTE_SUPPRESSED_MSGS" "E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/output_files/JYX_FPGA_OBJECT.fit.smsg " "Generated suppressed messages file E:/competition/EE/Electronic/EE_OBJECT/code/zuolan_signal_fpga_stm32/zuolan_FPGA/prj/output_files/JYX_FPGA_OBJECT.fit.smsg" {  } {  } 0 144001 "Generated suppressed messages file %1!s!" 0 0 "Fitter" 0 -1 1752772093907 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Fitter 0 s 3 s Quartus Prime " "Quartus Prime Fitter was successful. 0 errors, 3 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "6617 " "Peak virtual memory: 6617 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752772094436 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Jul 18 01:08:14 2025 " "Processing ended: Fri Jul 18 01:08:14 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752772094436 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:22 " "Elapsed time: 00:00:22" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752772094436 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:54 " "Total CPU time (on all processors): 00:00:54" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752772094436 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Fitter" 0 -1 1752772094436 ""}
